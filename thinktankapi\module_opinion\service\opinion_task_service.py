from datetime import datetime, timed<PERSON>ta
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from exceptions.exception import ServiceException
from module_opinion.dao.opinion_task_dao import OpinionTaskDao
from module_opinion.dao.opinion_requirement_dao import OpinionRequirementDao
from module_opinion.entity.vo.opinion_task_vo import (
    OpinionTaskModel,
    OpinionTaskPageQueryModel,
    CreateOpinionTaskModel,
    UpdateOpinionTaskModel,
    UpdateTaskStatusModel,
    TaskScheduleConfigModel
)
from module_admin.entity.vo.common_vo import CrudResponseModel
from utils.common_util import CamelCaseUtil
import json


class OpinionTaskService:
    """
    舆情任务管理模块服务层
    """

    @classmethod
    async def get_opinion_task_list_services(
        cls, query_db: AsyncSession, query_object: OpinionTaskPageQueryModel, is_page: bool = False
    ):
        """
        获取舆情任务列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 舆情任务列表信息对象
        """
        task_list_result = await OpinionTaskDao.get_opinion_task_list(query_db, query_object, is_page)
        
        if is_page:
            return task_list_result
        else:
            return CamelCaseUtil.transform_result(task_list_result)

    @classmethod
    async def get_opinion_task_detail_services(cls, query_db: AsyncSession, task_id: int, current_user_id: int = None):
        """
        获取舆情任务详细信息service（支持用户权限验证）

        :param query_db: orm对象
        :param task_id: 任务ID
        :param current_user_id: 当前用户ID，用于权限验证
        :return: 舆情任务详细信息对象
        """
        task_detail_result = await OpinionTaskDao.get_opinion_task_by_id(query_db, task_id)

        if not task_detail_result:
            raise ServiceException(message='舆情任务不存在')

        # 如果提供了当前用户ID，验证用户权限
        if current_user_id is not None and task_detail_result.user_id != current_user_id:
            raise ServiceException(message='无权限访问该任务')

        return CamelCaseUtil.transform_result(task_detail_result)

    @classmethod
    async def add_opinion_task_services(cls, query_db: AsyncSession, add_task: CreateOpinionTaskModel, current_user_id: int):
        """
        新增舆情任务信息service

        :param query_db: orm对象
        :param add_task: 新增舆情任务对象
        :param current_user_id: 当前用户ID
        :return: 新增舆情任务校验结果
        """
        # 检查需求是否存在
        requirement = await OpinionRequirementDao.get_opinion_requirement_by_id(query_db, add_task.requirement_id)
        if not requirement:
            raise ServiceException(message='关联的舆情需求不存在')

        # 检查基于内容的任务是否已存在
        if add_task.push_url:
            existing_task = await OpinionTaskDao.check_task_exists_by_content(
                query_db, add_task.requirement_id, add_task.task_type, add_task.push_url
            )
            if existing_task:
                return CrudResponseModel(
                    is_success=True,
                    message='任务已存在，跳过保存',
                    data={
                        'id': existing_task.id,
                        'exists': True,
                        'existing_task': {
                            'id': existing_task.id,
                            'task_name': existing_task.task_name,
                            'create_time': existing_task.create_time.isoformat() if existing_task.create_time else None
                        }
                    }
                )

        # 检查任务名称在需求下是否唯一
        if not await OpinionTaskDao.check_task_name_unique(query_db, add_task.requirement_id, add_task.task_name):
            raise ServiceException(message=f'任务名称"{add_task.task_name}"在该需求下已存在')

        # 构建任务对象
        task_data = OpinionTaskModel(
            requirement_id=add_task.requirement_id,
            user_id=current_user_id,
            task_name=add_task.task_name,
            task_description=add_task.task_description,
            task_type=add_task.task_type,
            frequency=add_task.frequency or add_task.schedule_type or 'daily',  # 使用频率字段或调度类型
            execute_time=add_task.execute_time or '09:00',  # 默认执行时间
            cron_expression=None,  # 暂时不设置cron表达式
            status='completed',
            push_url=add_task.push_url,
            push_config=add_task.push_config,
            create_by=str(current_user_id),
            remark=add_task.remark,
            positive_count=0,  # 初始化情感统计数据
            negative_count=0,
            neutral_count=0
        )

        try:
            new_task = await OpinionTaskDao.add_opinion_task_dao(query_db, task_data)
            # 在commit之前获取ID
            task_id = new_task.id
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='新增成功', data={'id': task_id, 'exists': False})
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    def _calculate_execution_time(cls, schedule_type: str, schedule_config: Optional[TaskScheduleConfigModel]) -> datetime:
        """
        根据调度类型和配置计算执行时间

        :param schedule_type: 调度类型
        :param schedule_config: 调度配置
        :return: 执行时间
        """
        now = datetime.now()
        
        if schedule_type == 'immediate':
            return now
        elif schedule_type == 'scheduled' and schedule_config and schedule_config.scheduled_time:
            return schedule_config.scheduled_time
        elif schedule_type == 'daily' and schedule_config and schedule_config.daily_time:
            # 计算今天或明天的指定时间
            today = now.date()
            scheduled_datetime = datetime.combine(today, schedule_config.daily_time)
            if scheduled_datetime <= now:
                scheduled_datetime += timedelta(days=1)
            return scheduled_datetime
        elif schedule_type == 'weekly' and schedule_config and schedule_config.weekly_day is not None and schedule_config.weekly_time:
            # 计算本周或下周的指定时间
            days_ahead = schedule_config.weekly_day - now.weekday()
            if days_ahead <= 0:  # 目标日期已过或是今天
                days_ahead += 7
            target_date = now.date() + timedelta(days=days_ahead)
            return datetime.combine(target_date, schedule_config.weekly_time)
        elif schedule_type == 'monthly' and schedule_config and schedule_config.monthly_day and schedule_config.monthly_time:
            # 计算本月或下月的指定时间
            try:
                target_date = now.replace(day=schedule_config.monthly_day).date()
                scheduled_datetime = datetime.combine(target_date, schedule_config.monthly_time)
                if scheduled_datetime <= now:
                    # 下个月
                    if now.month == 12:
                        target_date = now.replace(year=now.year + 1, month=1, day=schedule_config.monthly_day).date()
                    else:
                        target_date = now.replace(month=now.month + 1, day=schedule_config.monthly_day).date()
                    scheduled_datetime = datetime.combine(target_date, schedule_config.monthly_time)
                return scheduled_datetime
            except ValueError:
                # 日期无效，使用月末
                return now + timedelta(days=30)
        else:
            return now

    @classmethod
    async def edit_opinion_task_services(cls, query_db: AsyncSession, edit_task: UpdateOpinionTaskModel, current_user_id: int):
        """
        编辑舆情任务信息service（用户权限验证）

        :param query_db: orm对象
        :param edit_task: 编辑舆情任务对象
        :param current_user_id: 当前用户ID
        :return: 编辑舆情任务校验结果
        """
        # 检查任务是否存在
        existing_task = await OpinionTaskDao.get_opinion_task_by_id(query_db, edit_task.id)
        if not existing_task:
            raise ServiceException(message='舆情任务不存在')

        # 验证用户权限
        if existing_task.user_id != current_user_id:
            raise ServiceException(message='无权限编辑该任务')

        # 检查任务名称在需求下是否唯一（排除自己）
        if edit_task.task_name and not await OpinionTaskDao.check_task_name_unique(
            query_db, existing_task.requirement_id, edit_task.task_name, edit_task.id
        ):
            raise ServiceException(message=f'任务名称"{edit_task.task_name}"在该需求下已存在')

        # 构建更新数据
        update_data = {
            'id': edit_task.id,
            'update_time': datetime.now(),
            'update_by': str(current_user_id)
        }
        
        # 只更新非空字段，处理字段映射
        for field, value in edit_task.model_dump(exclude={'id'}, exclude_none=True).items():
            if field == 'schedule_type':
                # schedule_type 映射到 frequency 字段
                update_data['frequency'] = value
            elif field == 'schedule_config' and value:
                # 从 schedule_config 中提取 execute_time
                if isinstance(value, dict) and 'execute_time' in value:
                    update_data['execute_time'] = value['execute_time']
                # 其他配置存储到 push_config（如果需要的话）
                # update_data['push_config'] = json.dumps(value) if isinstance(value, dict) else value
            elif field == 'task_status':
                # task_status 映射到 status 字段
                update_data['status'] = value
            elif field == 'execution_time':
                # execution_time 映射到 next_execute_time 字段
                update_data['next_execute_time'] = value
            else:
                update_data[field] = value

        try:
            affected_rows = await OpinionTaskDao.edit_opinion_task_dao(query_db, update_data)
            if affected_rows == 0:
                raise ServiceException(message='任务更新失败，可能任务不存在或数据未发生变化')
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='更新成功', data={'affected_rows': affected_rows})
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def update_task_status_services(cls, query_db: AsyncSession, update_status: UpdateTaskStatusModel, current_user_id: int = None):
        """
        更新任务状态service（用户权限验证）

        :param query_db: orm对象
        :param update_status: 更新状态对象
        :param current_user_id: 当前用户ID，用于权限验证
        :return: 更新结果
        """
        # 检查任务是否存在
        existing_task = await OpinionTaskDao.get_opinion_task_by_id(query_db, update_status.task_id)
        if not existing_task:
            raise ServiceException(message='舆情任务不存在')

        # 如果提供了当前用户ID，验证用户权限
        if current_user_id is not None and existing_task.user_id != current_user_id:
            raise ServiceException(message='无权限修改该任务状态')

        try:
            await OpinionTaskDao.update_task_status(query_db, update_status.task_id, update_status.task_status)
            await query_db.commit()
            return CrudResponseModel(is_success=True, message='任务状态更新成功')
        except Exception as e:
            await query_db.rollback()
            raise e

    @classmethod
    async def delete_opinion_task_services(cls, query_db: AsyncSession, task_ids: List[int], current_user_id: int):
        """
        删除舆情任务信息service（物理删除，用户权限验证）

        :param query_db: orm对象
        :param task_ids: 任务ID列表
        :param current_user_id: 当前用户ID
        :return: 删除舆情任务校验结果
        """
        from utils.log_util import logger

        if not task_ids:
            raise ServiceException(message='请选择要删除的任务')

        logger.info(f'开始删除任务，用户ID: {current_user_id}, 任务IDs: {task_ids}')

        # 验证所有任务的存在性和用户权限
        valid_task_ids = []
        for task_id in task_ids:
            existing_task = await OpinionTaskDao.get_opinion_task_by_id(query_db, task_id)
            if not existing_task:
                logger.warning(f'任务ID为{task_id}的任务不存在，跳过删除')
                continue

            if existing_task.user_id != current_user_id:
                logger.error(f'用户{current_user_id}无权限删除任务ID为{task_id}的任务（任务所有者：{existing_task.user_id}）')
                raise ServiceException(message=f'无权限删除任务ID为{task_id}的任务')

            valid_task_ids.append(task_id)

        if not valid_task_ids:
            raise ServiceException(message='没有有效的任务可以删除')

        try:
            # 执行物理删除
            deleted_count = await OpinionTaskDao.delete_opinion_task_dao(query_db, valid_task_ids)
            await query_db.commit()

            logger.info(f'成功删除{deleted_count}个任务，任务IDs: {valid_task_ids}')
            return CrudResponseModel(is_success=True, message=f'成功删除{deleted_count}个任务')
        except Exception as e:
            await query_db.rollback()
            logger.error(f'删除任务失败: {str(e)}')
            raise ServiceException(message=f'删除任务失败: {str(e)}')

    @classmethod
    async def get_tasks_by_requirement_services(cls, query_db: AsyncSession, requirement_id: int, current_user_id: int = None):
        """
        根据需求ID获取任务列表service（支持用户级别隔离）

        :param query_db: orm对象
        :param requirement_id: 需求ID
        :param current_user_id: 当前用户ID，用于用户级别隔离
        :return: 任务列表
        """
        tasks = await OpinionTaskDao.get_tasks_by_requirement(query_db, requirement_id, current_user_id)
        return CamelCaseUtil.transform_result(tasks)

    @classmethod
    async def get_task_statistics_services(cls, query_db: AsyncSession, requirement_id: Optional[int] = None):
        """
        获取任务统计信息service

        :param query_db: orm对象
        :param requirement_id: 需求ID（可选）
        :return: 统计信息
        """
        return await OpinionTaskDao.get_task_statistics(query_db, requirement_id)

    @classmethod
    async def check_task_exists_services(cls, query_db: AsyncSession, requirement_id: int, task_type: str, push_url: str):
        """
        检查任务是否已存在service

        :param query_db: orm对象
        :param requirement_id: 需求ID
        :param task_type: 任务类型
        :param push_url: 推送URL
        :return: 检查结果
        """
        existing_task = await OpinionTaskDao.check_task_exists_by_content(query_db, requirement_id, task_type, push_url)

        if existing_task:
            return {
                'exists': True,
                'task_id': existing_task.id,
                'task_name': existing_task.task_name,
                'create_time': existing_task.create_time.isoformat() if existing_task.create_time else None,
                'status': existing_task.status
            }
        else:
            return {
                'exists': False
            }

    @classmethod
    async def save_report_oss_url_services(cls, query_db: AsyncSession, task_id: int, report_oss_url: str):
        """
        保存报告OSS URL到数据库service

        :param query_db: orm对象
        :param task_id: 任务ID
        :param report_oss_url: 报告OSS访问URL
        :return: 保存结果
        """
        try:
            # 更新数据库中的OSS URL
            affected_rows = await OpinionTaskDao.update_report_oss_url(query_db, task_id, report_oss_url)

            if affected_rows > 0:
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='报告OSS URL保存成功')
            else:
                return CrudResponseModel(is_success=False, message='任务不存在或更新失败')

        except Exception as e:
            await query_db.rollback()
            raise ServiceException(message=f'保存报告OSS URL失败: {str(e)}')

    @classmethod
    async def save_push_url_services(cls, query_db: AsyncSession, task_id: int, push_url: str):
        """
        保存推送URL到数据库service

        :param query_db: orm对象
        :param task_id: 任务ID
        :param push_url: 推送URL
        :return: 保存结果
        """
        try:
            # 更新数据库中的推送URL
            affected_rows = await OpinionTaskDao.update_push_url(query_db, task_id, push_url)

            if affected_rows > 0:
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='推送URL保存成功')
            else:
                return CrudResponseModel(is_success=False, message='任务不存在或更新失败')

        except Exception as e:
            await query_db.rollback()
            raise ServiceException(message=f'保存推送URL失败: {str(e)}')

    @classmethod
    async def update_sentiment_counts_services(
        cls,
        query_db: AsyncSession,
        task_id: int,
        positive_count: int,
        negative_count: int,
        neutral_count: int
    ):
        """
        更新任务的情感统计数据service

        :param query_db: orm对象
        :param task_id: 任务ID
        :param positive_count: 正面情感数量
        :param negative_count: 负面情感数量
        :param neutral_count: 中性情感数量
        :return: 更新结果
        """
        try:
            # 检查任务是否存在
            existing_task = await OpinionTaskDao.get_opinion_task_by_id(query_db, task_id)
            if not existing_task:
                raise ServiceException(message='任务不存在')

            # 更新情感统计数据
            updated_rows = await OpinionTaskDao.update_sentiment_counts(
                query_db, task_id, positive_count, negative_count, neutral_count
            )

            if updated_rows > 0:
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='情感统计数据更新成功')
            else:
                return CrudResponseModel(is_success=False, message='更新失败')

        except Exception as e:
            await query_db.rollback()
            raise ServiceException(message=f'更新情感统计数据失败: {str(e)}')
