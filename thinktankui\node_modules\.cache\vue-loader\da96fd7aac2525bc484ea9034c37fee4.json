{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\analyze-record\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\analyze-record\\index.vue", "mtime": 1753341129010}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753065270161}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753065271554}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753065270161}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753065272328}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAshBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/analyze-record", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 搜索区域 -->\r\n    <el-form v-show=\"showSearch\" ref=\"queryForm\" :model=\"queryParams\" size=\"small\" :inline=\"true\" label-width=\"68px\">\r\n\r\n      <el-form-item label=\"需求名称\" prop=\"requirementName\">\r\n        <el-input\r\n          id=\"requirementName\"\r\n          v-model=\"queryParams.requirementName\"\r\n          placeholder=\"请输入需求名称\"\r\n          clearable\r\n          @input=\"onRequirementNameInput\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"关键词\" prop=\"entityKeyword\">\r\n        <el-input\r\n          id=\"entityKeyword\"\r\n          v-model=\"queryParams.entityKeyword\"\r\n          placeholder=\"请输入实体关键词\"\r\n          clearable\r\n          @input=\"onEntityKeywordInput\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"分析状态\" prop=\"analysisStatus\">\r\n        <el-select\r\n          id=\"analysisStatus\"\r\n          v-model=\"queryParams.analysisStatus\"\r\n          placeholder=\"请选择分析状态\"\r\n          clearable\r\n          @change=\"onAnalysisStatusChange\"\r\n        >\r\n          <el-option label=\"未开始\" value=\"0\" />\r\n          <el-option label=\"分析中\" value=\"1\" />\r\n          <el-option label=\"已完成\" value=\"2\" />\r\n          <el-option label=\"失败\" value=\"3\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"创建时间\">\r\n        <el-date-picker\r\n          id=\"create_time_range\"\r\n          v-model=\"dateRange\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 操作按钮区域 -->\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          v-hasPermi=\"['opinion:record:export']\"\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-time\"\r\n          size=\"mini\"\r\n          @click=\"handleTimedPush\"\r\n        >定时推送</el-button>\r\n      </el-col>\r\n      <right-toolbar :show-search.sync=\"showSearch\" @queryTable=\"getList\" />\r\n    </el-row>\r\n\r\n    <!-- 数据表格 -->\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"recordList\"\r\n      :default-sort=\"{prop: 'createTime', order: 'descending'}\"\r\n      @selection-change=\"handleSelectionChange\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"ID\" align=\"center\" prop=\"id\" width=\"80\" />\r\n      <el-table-column label=\"需求名称\" align=\"center\" prop=\"requirementName\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"关键词\" align=\"center\" prop=\"entityKeyword\" width=\"120\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"具体需求\" align=\"center\" prop=\"specificRequirement\" width=\"200\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"生成状态\" align=\"center\" prop=\"status\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            :type=\"getStatusTagType(scope.row.status)\"\r\n            size=\"mini\"\r\n          >\r\n            {{ getStatusText(scope.row.status) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"380\">\r\n        <template slot-scope=\"scope\">\r\n\r\n          <el-button\r\n            v-if=\"scope.row.status === 2\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-notebook-1\"\r\n            @click=\"handleReport(scope.row)\"\r\n          >报告</el-button>\r\n          <el-button\r\n            v-if=\"scope.row.status === 2\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-s-promotion\"\r\n            @click=\"handlePushNow(scope.row)\"\r\n          >立即推送</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <!-- 分页 -->\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.page_num\"\r\n      :limit.sync=\"queryParams.page_size\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 分析日志对话框 -->\r\n    <el-dialog title=\"分析日志\" :visible.sync=\"logDialogVisible\" width=\"1000px\" append-to-body>\r\n      <el-table\r\n        v-loading=\"logLoading\"\r\n        :data=\"logList\"\r\n        height=\"400\"\r\n        :default-sort=\"{prop: 'createTime', order: 'descending'}\"\r\n      >\r\n        <el-table-column label=\"时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"级别\" align=\"center\" prop=\"logLevel\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag\r\n              :type=\"getLogLevelTagType(scope.row.logLevel)\"\r\n              size=\"mini\"\r\n            >\r\n              {{ scope.row.logLevel.toUpperCase() }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"步骤\" align=\"center\" prop=\"stepName\" width=\"120\" />\r\n        <el-table-column label=\"进度\" align=\"center\" prop=\"progressPercentage\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ scope.row.progressPercentage }}%</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"日志内容\" align=\"center\" prop=\"logMessage\" :show-overflow-tooltip=\"true\" />\r\n      </el-table>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"logDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 查看报告对话框 -->\r\n    <el-dialog title=\"分析报告\" :visible.sync=\"reportDialogVisible\" width=\"1200px\" append-to-body>\r\n      <div v-loading=\"reportLoading\" class=\"report-content\">\r\n        <div v-if=\"reportData\">\r\n          <el-descriptions title=\"基本信息\" :column=\"2\" border>\r\n            <el-descriptions-item label=\"需求名称\">{{ reportData.requirementName }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"关键词\">{{ reportData.entityKeyword }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"具体需求\" :span=\"2\">{{ reportData.specificRequirement }}</el-descriptions-item>\r\n          </el-descriptions>\r\n\r\n          <el-divider content-position=\"left\">分析结果</el-divider>\r\n          <div class=\"analysis-results\">\r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"8\">\r\n                <el-card shadow=\"never\">\r\n                  <div slot=\"header\">\r\n                    <span>文章总数</span>\r\n                  </div>\r\n                  <div class=\"statistic-value\">{{ reportData.totalArticles || 0 }}</div>\r\n                </el-card>\r\n              </el-col>\r\n              <el-col :span=\"8\">\r\n                <el-card shadow=\"never\">\r\n                  <div slot=\"header\">\r\n                    <span>正面情感</span>\r\n                  </div>\r\n                  <div class=\"statistic-value positive\">{{ reportData.sentiment?.positive || 0 }}</div>\r\n                </el-card>\r\n              </el-col>\r\n              <el-col :span=\"8\">\r\n                <el-card shadow=\"never\">\r\n                  <div slot=\"header\">\r\n                    <span>负面情感</span>\r\n                  </div>\r\n                  <div class=\"statistic-value negative\">{{ reportData.sentiment?.negative || 0 }}</div>\r\n                </el-card>\r\n              </el-col>\r\n            </el-row>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"handleExportReport\">导出报告</el-button>\r\n        <el-button @click=\"reportDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 立即推送对话框 -->\r\n    <el-dialog title=\"立即推送\" :visible.sync=\"pushDialogVisible\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"pushForm\" :model=\"pushForm\" :rules=\"pushRules\" label-width=\"100px\">\r\n        <el-form-item label=\"推送地址\" prop=\"push_url\">\r\n          <el-input v-model=\"pushForm.push_url\" placeholder=\"请输入推送地址\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"推送内容\" prop=\"push_content\">\r\n          <el-input\r\n            v-model=\"pushForm.push_content\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n            placeholder=\"请输入推送内容\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"pushDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" :loading=\"pushLoading\" @click=\"submitPush\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 定时推送对话框 -->\r\n    <el-dialog title=\"定时推送\" :visible.sync=\"schedulePushDialogVisible\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"schedulePushForm\" :model=\"schedulePushForm\" :rules=\"schedulePushRules\" label-width=\"100px\">\r\n        <el-form-item label=\"推送地址\" prop=\"push_url\">\r\n          <el-input v-model=\"schedulePushForm.push_url\" placeholder=\"请输入推送地址\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"推送时间\" prop=\"push_time\">\r\n          <el-date-picker\r\n            v-model=\"schedulePushForm.push_time\"\r\n            type=\"datetime\"\r\n            placeholder=\"选择推送时间\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            style=\"width: 100%\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"推送频率\" prop=\"frequency\">\r\n          <el-select v-model=\"schedulePushForm.frequency\" placeholder=\"请选择推送频率\">\r\n            <el-option label=\"仅一次\" value=\"once\" />\r\n            <el-option label=\"每日\" value=\"daily\" />\r\n            <el-option label=\"每周\" value=\"weekly\" />\r\n            <el-option label=\"每月\" value=\"monthly\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"推送内容\" prop=\"push_content\">\r\n          <el-input\r\n            v-model=\"schedulePushForm.push_content\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n            placeholder=\"请输入推送内容\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"schedulePushDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" :loading=\"schedulePushLoading\" @click=\"submitSchedulePush\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 定时任务抽屉 -->\r\n    <el-drawer\r\n      title=\"定时任务\"\r\n      :visible.sync=\"timedTaskDialogVisible\"\r\n      direction=\"rtl\"\r\n      size=\"600px\"\r\n      :before-close=\"closeTimedTaskDialog\"\r\n      custom-class=\"timed-task-drawer\"\r\n    >\r\n      <!-- 抽屉头部右侧按钮 -->\r\n      <div slot=\"title\" class=\"drawer-header\">\r\n        <span class=\"drawer-title\">定时任务</span>\r\n        <el-button\r\n          type=\"primary\"\r\n          size=\"mini\"\r\n          icon=\"el-icon-plus\"\r\n          class=\"add-task-btn\"\r\n          @click=\"handleAddTimedTask\"\r\n        >\r\n          定时任务\r\n        </el-button>\r\n      </div>\r\n\r\n      <!-- 抽屉内容 -->\r\n      <div class=\"drawer-content\">\r\n        <!-- 空状态 -->\r\n        <div v-if=\"timedTaskList.length === 0\" class=\"empty-state\">\r\n          <div class=\"empty-content\">\r\n            <!-- 空状态图标 -->\r\n            <div class=\"empty-icon\">\r\n              <svg width=\"120\" height=\"120\" viewBox=\"0 0 120 120\" fill=\"none\">\r\n                <!-- 文件夹图标 -->\r\n                <path d=\"M20 30h25l5-10h50v70H20V30z\" fill=\"#f0f0f0\" stroke=\"#d0d0d0\" stroke-width=\"2\" />\r\n                <path d=\"M25 35h70v50H25V35z\" fill=\"#fafafa\" stroke=\"#e0e0e0\" stroke-width=\"1\" />\r\n                <!-- 文档图标 -->\r\n                <rect x=\"35\" y=\"45\" width=\"30\" height=\"25\" fill=\"#ffffff\" stroke=\"#d0d0d0\" stroke-width=\"1\" rx=\"2\" />\r\n                <rect x=\"70\" y=\"50\" width=\"20\" height=\"15\" fill=\"#ffffff\" stroke=\"#d0d0d0\" stroke-width=\"1\" rx=\"2\" />\r\n                <!-- 装饰线条 -->\r\n                <line x1=\"40\" y1=\"52\" x2=\"60\" y2=\"52\" stroke=\"#e0e0e0\" stroke-width=\"1\" />\r\n                <line x1=\"40\" y1=\"57\" x2=\"55\" y2=\"57\" stroke=\"#e0e0e0\" stroke-width=\"1\" />\r\n                <line x1=\"40\" y1=\"62\" x2=\"58\" y2=\"62\" stroke=\"#e0e0e0\" stroke-width=\"1\" />\r\n                <line x1=\"75\" y1=\"55\" x2=\"85\" y2=\"55\" stroke=\"#e0e0e0\" stroke-width=\"1\" />\r\n                <line x1=\"75\" y1=\"60\" x2=\"82\" y2=\"60\" stroke=\"#e0e0e0\" stroke-width=\"1\" />\r\n              </svg>\r\n            </div>\r\n            <p class=\"empty-text\">暂无定时任务</p>\r\n            <el-button type=\"primary\" class=\"create-btn\" @click=\"handleCreateTimedTask\">\r\n              去创建\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 任务列表 -->\r\n        <div v-else class=\"task-list\">\r\n          <div v-if=\"timedTaskList.length === 0\" class=\"empty-task-list\">\r\n            <div class=\"empty-icon\">📅</div>\r\n            <div class=\"empty-text\">暂无定时任务</div>\r\n            <el-button type=\"primary\" size=\"small\" class=\"add-task-btn\" @click=\"handleAddTimedTask\">\r\n              添加任务\r\n            </el-button>\r\n          </div>\r\n          <div v-else class=\"task-items\">\r\n            <div v-for=\"(task, index) in timedTaskList\" :key=\"index\" class=\"task-item\">\r\n              <div class=\"task-info\">\r\n                <div class=\"task-header\">\r\n                  <div class=\"task-name\">{{ task.name }}</div>\r\n                  <div class=\"task-status\" :class=\"{ 'status-running': task.status === 'running', 'status-pending': task.status === 'pending' }\">\r\n                    {{ task.status === 'running' ? '运行中' : '待运行' }}\r\n                  </div>\r\n                </div>\r\n                <!-- 任务描述已隐藏 -->\r\n                <div class=\"task-schedule\">\r\n                  <i class=\"el-icon-time\" />\r\n                  <span>{{ getTaskScheduleText(task) }}</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"task-actions\">\r\n                <el-button type=\"text\" size=\"mini\" title=\"预览任务详情\" @click.stop=\"previewTask(index)\">\r\n                  <i class=\"el-icon-view\" />\r\n                </el-button>\r\n                <el-button type=\"text\" size=\"mini\" :title=\"task.status === 'running' ? '暂停任务' : '启动任务'\" @click.stop=\"toggleTaskStatus(index)\">\r\n                  <i :class=\"task.status === 'running' ? 'el-icon-video-pause' : 'el-icon-video-play'\" />\r\n                </el-button>\r\n                <el-button type=\"text\" size=\"mini\" title=\"编辑任务\" style=\"margin-right: 5px;\" @click.stop=\"editTask(index)\">\r\n                  <i class=\"el-icon-edit\" />\r\n                </el-button>\r\n                <el-button type=\"text\" size=\"mini\" title=\"删除任务\" style=\"color: #f56c6c;\" @click.stop=\"deleteTask(index)\">\r\n                  <i class=\"el-icon-delete\" />\r\n                </el-button>\r\n                <!-- 测试按钮 -->\r\n                <el-button type=\"text\" size=\"mini\" title=\"测试点击\" style=\"color: #409eff;\" @click.stop=\"testClick(index)\">\r\n                  <i class=\"el-icon-info\" />\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 任务预览对话框 -->\r\n      <el-dialog\r\n        title=\"任务详情\"\r\n        :visible.sync=\"taskPreviewDialog.visible\"\r\n        width=\"600px\"\r\n        append-to-body\r\n      >\r\n        <div v-if=\"taskPreviewDialog.taskData\" class=\"task-preview-content\">\r\n          <el-descriptions title=\"基本信息\" :column=\"2\" border>\r\n            <el-descriptions-item label=\"任务名称\">{{ taskPreviewDialog.taskData.name }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"任务状态\">\r\n              <el-tag :type=\"taskPreviewDialog.taskData.status === 'running' ? 'success' : 'info'\">\r\n                {{ taskPreviewDialog.taskData.status === 'running' ? '运行中' : '待运行' }}\r\n              </el-tag>\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"执行频率\">{{ getFrequencyText(taskPreviewDialog.taskData.frequency) }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"执行时间\">{{ taskPreviewDialog.taskData.executeTime }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"推送地址\" :span=\"2\">{{ taskPreviewDialog.taskData.pushUrl || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"任务描述\" :span=\"2\">{{ taskPreviewDialog.taskData.description || '无描述' }}</el-descriptions-item>\r\n          </el-descriptions>\r\n        </div>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button @click=\"taskPreviewDialog.visible = false\">关 闭</el-button>\r\n        </div>\r\n      </el-dialog>\r\n\r\n      <!-- 创建/编辑任务弹窗 -->\r\n      <el-dialog\r\n        :title=\"editingTaskIndex === -1 ? '创建定时任务' : '编辑定时任务'\"\r\n        :visible.sync=\"createTaskDialogVisible\"\r\n        width=\"500px\"\r\n        :before-close=\"closeCreateTaskDialog\"\r\n        :append-to-body=\"true\"\r\n        class=\"create-task-dialog\"\r\n      >\r\n        <div class=\"task-form\">\r\n          <!-- 任务需求 -->\r\n          <div class=\"task-requirement-section\">\r\n            <div class=\"section-label\">\r\n              任务需求\r\n              <span class=\"required\">*</span>\r\n            </div>\r\n            <div class=\"form-group\">\r\n              <div class=\"input-label\">关联任务</div>\r\n              <el-select\r\n                v-model=\"taskForm.requirement_id\"\r\n                placeholder=\"请选择要关联的舆情分析任务\"\r\n                class=\"task-name-input\"\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"task in requirementList\"\r\n                  :key=\"task.id\"\r\n                  :label=\"task.requirementName\"\r\n                  :value=\"task.id\"\r\n                >\r\n                  <span style=\"float: left\">{{ task.requirementName }}</span>\r\n                  <span style=\"float: right; color: #8492a6; font-size: 13px\">ID: {{ task.id }}</span>\r\n                </el-option>\r\n              </el-select>\r\n            </div>\r\n            <div class=\"form-group\">\r\n              <div class=\"input-label\">任务名称</div>\r\n              <el-input\r\n                v-model=\"taskForm.name\"\r\n                placeholder=\"请输入任务名称\"\r\n                class=\"task-name-input\"\r\n              />\r\n            </div>\r\n            <div class=\"form-group\">\r\n              <div class=\"input-label\">任务描述</div>\r\n              <el-input\r\n                v-model=\"taskForm.description\"\r\n                type=\"textarea\"\r\n                :rows=\"3\"\r\n                placeholder=\"请描述具体的任务需求，例如：基于当前分析需求自动生成AI新闻总结\"\r\n                class=\"task-description-input\"\r\n              />\r\n              <div class=\"form-group\">\r\n                <div class=\"input-label\">推送地址</div>\r\n                <el-input\r\n                  v-model=\"taskForm.push_url\"\r\n                  placeholder=\"例如：https://www.baidu.com\"\r\n                  class=\"task-name-input\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 执行时间 -->\r\n          <div class=\"execute-time-section\">\r\n            <div class=\"section-label\">执行时间</div>\r\n            <div class=\"time-selector\">\r\n              <el-select v-model=\"taskForm.frequency\" placeholder=\"选择频率\" class=\"frequency-select\">\r\n                <el-option label=\"仅一次\" value=\"once\" />\r\n                <el-option label=\"每天\" value=\"daily\" />\r\n                <el-option label=\"每周\" value=\"weekly\" />\r\n                <el-option label=\"每月\" value=\"monthly\" />\r\n              </el-select>\r\n              <!-- 一次性任务：选择具体日期时间 -->\r\n              <el-date-picker\r\n                v-if=\"taskForm.frequency === 'once'\"\r\n                v-model=\"taskForm.execute_date_time\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择执行日期和时间\"\r\n                format=\"yyyy-MM-dd HH:mm\"\r\n                value-format=\"yyyy-MM-dd HH:mm\"\r\n                class=\"datetime-picker\"\r\n                :picker-options=\"{\r\n                  disabledDate(time) {\r\n                    return time.getTime() < Date.now() - 8.64e7\r\n                  }\r\n                }\"\r\n              />\r\n              <!-- 周期性任务：选择时间 -->\r\n              <el-time-picker\r\n                v-else\r\n                v-model=\"taskForm.execute_time\"\r\n                format=\"HH:mm\"\r\n                value-format=\"HH:mm\"\r\n                placeholder=\"选择时间\"\r\n                class=\"time-picker\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 底部按钮 -->\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button class=\"modify-btn\" @click=\"modifyPlan\">修改计划</el-button>\r\n          <el-button type=\"primary\" class=\"run-btn\" @click=\"saveAndRunTask\">保存并运行</el-button>\r\n          <el-button type=\"success\" class=\"save-btn\" @click=\"saveTaskPlan\">保存计划</el-button>\r\n        </div>\r\n      </el-dialog>\r\n    </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  getAnalysisTaskList,\r\n  deleteAnalysisTask,\r\n  batchDeleteAnalysisTasks,\r\n  exportRequirements,\r\n  getAnalysisProgress,\r\n  pushReport,\r\n  createTimedTask,\r\n  getTimedTaskList,\r\n  getTimedTaskDetail,\r\n  updateTimedTask,\r\n  updateTaskStatus,\r\n  deleteTimedTask\r\n} from '@/api/opinion-analysis'\r\nimport { parseTime } from '@/utils/ruoyi'\r\n\r\nexport default {\r\n  name: 'AnalyzeRecord',\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 分析记录表格数据\r\n      recordList: [],\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        page_num: 1,\r\n        page_size: 10,\r\n        requirementName: null,\r\n        entityKeyword: null,\r\n        analysisStatus: null\r\n      },\r\n      // 分析日志对话框\r\n      logDialogVisible: false,\r\n      logLoading: false,\r\n      logList: [],\r\n      // 查看报告对话框\r\n      reportDialogVisible: false,\r\n      reportLoading: false,\r\n      reportData: null,\r\n      // 立即推送对话框\r\n      pushDialogVisible: false,\r\n      pushLoading: false,\r\n      pushForm: {\r\n        push_url: '',\r\n        push_content: ''\r\n      },\r\n      pushRules: {\r\n        push_url: [\r\n          { required: true, message: '推送地址不能为空', trigger: 'blur' }\r\n        ],\r\n        push_content: [\r\n          { required: true, message: '推送内容不能为空', trigger: 'blur' }\r\n        ]\r\n      },\r\n      // 定时推送对话框\r\n      schedulePushDialogVisible: false,\r\n      schedulePushLoading: false,\r\n      schedulePushForm: {\r\n        push_url: '',\r\n        push_time: '',\r\n        frequency: 'once',\r\n        push_content: ''\r\n      },\r\n      schedulePushRules: {\r\n        push_url: [\r\n          { required: true, message: '推送地址不能为空', trigger: 'blur' }\r\n        ],\r\n        push_time: [\r\n          { required: true, message: '推送时间不能为空', trigger: 'change' }\r\n        ],\r\n        push_content: [\r\n          { required: true, message: '推送内容不能为空', trigger: 'blur' }\r\n        ]\r\n      },\r\n      // 当前操作的记录\r\n      currentRecord: null,\r\n      // 定时任务相关数据\r\n      timedTaskDialogVisible: false, // 定时任务抽屉显示状态\r\n      timedTaskList: [], // 定时任务列表\r\n      createTaskDialogVisible: false, // 创建任务弹窗显示状态\r\n      editingTaskIndex: -1, // 当前编辑的任务索引，-1表示新建任务\r\n      taskForm: {\r\n        requirement_id: '', // 需求ID\r\n        name: '', // 任务名称\r\n        description: '',\r\n        execute_time: '16:00',\r\n        execute_date_time: '', // 一次性任务的执行日期时间\r\n        frequency: 'daily',\r\n        push_url: '' // 推送地址\r\n      },\r\n      requirementList: [], // 需求列表\r\n      // 任务预览弹窗状态\r\n      taskPreviewDialog: {\r\n        visible: false,\r\n        taskData: null,\r\n        loading: false\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    // 只重置日期范围，保留其他查询参数\r\n    this.dateRange = []\r\n    this.getList()\r\n    // 注释掉页面加载时的定时任务列表加载，改为点击定时推送按钮时才加载\r\n    // this.loadTimedTaskList()\r\n  },\r\n  methods: {\r\n    /** 查询分析记录列表 */\r\n    getList(paginationParams) {\r\n      this.loading = true\r\n\r\n      // 处理分页参数\r\n      if (paginationParams) {\r\n        this.queryParams.page_num = paginationParams.page\r\n        this.queryParams.page_size = paginationParams.limit\r\n      }\r\n\r\n      const params = {\r\n        page_num: this.queryParams.page_num,\r\n        page_size: this.queryParams.page_size\r\n      }\r\n\r\n      // 添加搜索条件（正确映射到任务表字段）\r\n      if (this.queryParams.requirementName && this.queryParams.requirementName.trim() !== '') {\r\n        params.task_name = this.queryParams.requirementName.trim() // 映射到任务名称\r\n      }\r\n      if (this.queryParams.entityKeyword && this.queryParams.entityKeyword.trim() !== '') {\r\n        params.task_description = this.queryParams.entityKeyword.trim() // 映射到任务描述（包含关键词）\r\n      }\r\n      if (this.queryParams.analysisStatus !== null && this.queryParams.analysisStatus !== undefined && this.queryParams.analysisStatus !== '') {\r\n        // 将前端状态映射回任务状态\r\n        const statusMap = { 0: 'pending', 1: 'running', 2: 'completed', 3: 'failed' }\r\n        params.status = statusMap[this.queryParams.analysisStatus] || 'pending'\r\n      }\r\n\r\n      // 添加时间范围\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.create_time_start = this.dateRange[0]\r\n        params.create_time_end = this.dateRange[1]\r\n        console.log('🔍 添加时间范围筛选:', this.dateRange)\r\n      }\r\n\r\n      // 调用真实API获取任务列表数据\r\n      console.log('🔍 发送API请求，参数:', params)\r\n      getAnalysisTaskList(params).then(response => {\r\n        console.log('🔍 API响应:', response)\r\n        // 处理响应数据，将数据库字段映射到前端显示字段\r\n\r\n        // 适配新的响应格式：使用 model_content 后，PageResponseModel 会直接合并到响应根级别\r\n        let requirements = []\r\n        if (response.records) {\r\n          // 新格式：model_content 使 PageResponseModel 直接合并到响应根级别\r\n          requirements = response.records\r\n        } else if (response.data && response.data.records) {\r\n          // 备用格式：PageResponseModel 在 data 字段中\r\n          requirements = response.data.records\r\n        } else if (response.data && response.data.rows) {\r\n          // 旧的分页数据格式\r\n          requirements = response.data.rows\r\n        } else if (response.data && Array.isArray(response.data)) {\r\n          // 直接数组格式\r\n          requirements = response.data\r\n        } else if (response.rows) {\r\n          // 兼容旧格式\r\n          requirements = response.rows\r\n        } else {\r\n          requirements = []\r\n        }\r\n        this.recordList = requirements\r\n          .filter(item => item.id != null && item.id !== undefined && item.id !== '') // 过滤掉无效id的数据\r\n          .map(item => {\r\n            // 确保ID是有效的整数\r\n            const id = parseInt(item.id)\r\n            if (isNaN(id)) {\r\n              return null\r\n            }\r\n\r\n            const mappedItem = {\r\n              id: id, // 确保id是整数\r\n              requirementName: item.task_name || item.taskName || '', // 任务名称\r\n              entityKeyword: item.task_description || item.taskDescription || '', // 任务描述作为关键词显示\r\n              specificRequirement: item.task_description || item.taskDescription || '', // 任务描述\r\n              createTime: item.create_time || item.createTime || '',\r\n              // 将任务状态映射到前端的status字段\r\n              // pending-待执行->0待生成, running-执行中->1生成中, completed-已完成->2已生成, failed-失败->-1生成失败\r\n              status: this.mapTaskStatus(item.status || 'pending'),\r\n              // 添加报告OSS URL字段\r\n              reportOssUrl: item.report_oss_url || item.reportOssUrl || null\r\n            }\r\n            return mappedItem\r\n          })\r\n          .filter(item => item !== null) // 过滤掉映射失败的项\r\n\r\n        // 适配新的总记录数格式：使用 model_content 后，total 会直接在响应根级别\r\n        if (response.total !== undefined) {\r\n          this.total = response.total\r\n        } else if (response.data && response.data.total !== undefined) {\r\n          this.total = response.data.total\r\n        } else {\r\n          this.total = 0\r\n        }\r\n\r\n        this.loading = false\r\n      }).catch(error => {\r\n        this.$modal.msgError('获取分析记录列表失败: ' + (error.message || '未知错误'))\r\n        this.loading = false\r\n      })\r\n    },\r\n\r\n    /** 映射任务状态 */\r\n    mapTaskStatus(taskStatus) {\r\n      // 数据库task status: pending-待执行，running-执行中，completed-已完成，failed-失败，paused-暂停\r\n      // 前端status: 0-待生成，1-生成中，2-已生成，-1-生成失败\r\n      const statusMap = {\r\n        'pending': 0, // 待执行 -> 待生成\r\n        'running': 1, // 执行中 -> 生成中\r\n        'completed': 2, // 已完成 -> 已生成\r\n        'failed': -1, // 失败 -> 生成失败\r\n        'paused': 0 // 暂停 -> 待生成\r\n      }\r\n      return statusMap[taskStatus] !== undefined ? statusMap[taskStatus] : 0\r\n    },\r\n\r\n    /** 映射分析状态（保留兼容性） */\r\n    mapAnalysisStatus(analysisStatus) {\r\n      // 数据库analysis_status: 0-未开始，1-分析中，2-已完成，3-失败\r\n      // 前端status: 0-待生成，1-生成中，2-已生成，-1-生成失败\r\n      const statusMap = {\r\n        0: 0, // 未开始 -> 待生成\r\n        1: 1, // 分析中 -> 生成中\r\n        2: 2, // 已完成 -> 已生成\r\n        3: -1 // 失败 -> 生成失败\r\n      }\r\n      return statusMap[analysisStatus] !== undefined ? statusMap[analysisStatus] : 0\r\n    },\r\n\r\n    /** 需求名称输入监听 */\r\n    onRequirementNameInput(value) {\r\n      console.log('🔍 需求名称输入变化:', value)\r\n      // 可以添加防抖逻辑，避免频繁查询\r\n      // this.debounceSearch()\r\n    },\r\n\r\n    /** 关键词输入监听 */\r\n    onEntityKeywordInput(value) {\r\n      console.log('🔍 关键词输入变化:', value)\r\n      // 可以添加防抖逻辑，避免频繁查询\r\n      // this.debounceSearch()\r\n    },\r\n\r\n    /** 分析状态变化监听 */\r\n    onAnalysisStatusChange(value) {\r\n      console.log('🔍 分析状态变化:', value)\r\n      // 状态变化时立即搜索\r\n      this.handleQuery()\r\n    },\r\n\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      // 重置到第一页\r\n      this.queryParams.page_num = 1\r\n      // 添加搜索日志\r\n      console.log('🔍 执行搜索，查询参数:', this.queryParams)\r\n      this.getList()\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = []\r\n      // 手动重置查询参数\r\n      this.queryParams = {\r\n        page_num: 1,\r\n        page_size: 10,\r\n        requirementName: null,\r\n        entityKeyword: null,\r\n        analysisStatus: null\r\n      }\r\n      // 重置表单\r\n      this.resetForm('queryForm')\r\n      // 添加重置日志\r\n      console.log('🔄 重置搜索条件，查询参数:', this.queryParams)\r\n      this.handleQuery()\r\n    },\r\n\r\n    /** 多选框选中数据 */\r\n    handleSelectionChange(selection) {\r\n      // 过滤掉id为null或undefined的项，并确保id是整数\r\n      this.ids = selection\r\n        .filter(item => item.id != null && item.id !== undefined)\r\n        .map(item => parseInt(item.id))\r\n\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n\r\n    /** 查看分析日志 */\r\n    handleViewLogs(row) {\r\n      this.currentRecord = row\r\n      this.logDialogVisible = true\r\n      this.logLoading = true\r\n\r\n      // 获取分析进度日志\r\n      getAnalysisProgress(row.taskId || row.id).then(response => {\r\n        this.logList = response.data || []\r\n        this.logLoading = false\r\n      }).catch(() => {\r\n        this.logLoading = false\r\n      })\r\n    },\r\n\r\n    /** 查看报告 */\r\n    handleViewReport(row) {\r\n      this.currentRecord = row\r\n      this.reportDialogVisible = true\r\n      this.reportLoading = true\r\n\r\n      // 模拟获取报告数据\r\n      setTimeout(() => {\r\n        this.reportData = {\r\n          requirementName: row.requirementName,\r\n          entityKeyword: row.entityKeyword,\r\n          specificRequirement: row.specificRequirement,\r\n          totalArticles: Math.floor(Math.random() * 1000) + 100,\r\n          sentiment: {\r\n            positive: Math.floor(Math.random() * 50) + 20,\r\n            neutral: Math.floor(Math.random() * 30) + 10,\r\n            negative: Math.floor(Math.random() * 20) + 5\r\n          }\r\n        }\r\n        this.reportLoading = false\r\n      }, 1000)\r\n    },\r\n\r\n    /** 报告 */\r\n    handleReport(row) {\r\n      // 检查是否有报告OSS URL\r\n      if (row.reportOssUrl) {\r\n        // 如果有OSS URL，直接在新窗口打开\r\n        window.open(row.reportOssUrl, '_blank')\r\n      } else {\r\n        // 如果没有OSS URL，显示暂无报告提示\r\n        this.$modal.msg('暂无报告')\r\n      }\r\n    },\r\n\r\n    /** 立即推送 */\r\n    handlePushNow(row) {\r\n      // 移除OSS URL检查，让后端处理报告生成和上传\r\n      // 如果没有reportOssUrl，后端会自动生成HTML报告并上传到OSS\r\n\r\n      this.currentRecord = row\r\n      this.pushForm = {\r\n        push_url: '',\r\n        push_content: `分析报告：${row.requirementName} - 立即推送`\r\n      }\r\n      this.pushDialogVisible = true\r\n    },\r\n\r\n    /** 定时推送 */\r\n    handleSchedulePush(row) {\r\n      this.currentRecord = row\r\n      this.schedulePushForm = {\r\n        push_url: '',\r\n        push_time: '',\r\n        frequency: 'once',\r\n        push_content: `分析报告：${row.requirementName}`\r\n      }\r\n      this.schedulePushDialogVisible = true\r\n    },\r\n\r\n    /** 提交立即推送 */\r\n    submitPush() {\r\n      this.$refs['pushForm'].validate(valid => {\r\n        if (valid) {\r\n          this.pushLoading = true\r\n\r\n          // 构建推送数据\r\n          // 如果没有reportOssUrl，后端会自动生成HTML报告并上传到OSS\r\n          const pushData = {\r\n            target_url: this.pushForm.push_url,\r\n            report_data: {\r\n              requirementName: this.currentRecord.requirementName,\r\n              entityKeyword: this.currentRecord.entityKeyword,\r\n              specificRequirement: this.currentRecord.specificRequirement,\r\n              reportOssUrl: this.currentRecord.reportOssUrl || null, // 可能为空，后端会处理\r\n              totalArticles: 0, // 后端会从实际数据中获取\r\n              sentiment: {}, // 后端会从实际数据中获取\r\n              dataSources: 0, // 后端会从实际数据中获取\r\n              // 添加更多信息以便后端生成完整报告\r\n              taskId: this.currentRecord.id,\r\n              createTime: this.currentRecord.createTime,\r\n              status: this.currentRecord.status\r\n            },\r\n            analysis_results: {\r\n              summary: this.pushForm.push_content,\r\n              reportUrl: this.currentRecord.reportOssUrl || null // 可能为空，后端会生成\r\n            },\r\n            requirement_id: this.currentRecord.id,\r\n            push_type: 'immediate'\r\n          }\r\n\r\n          pushReport(pushData).then(response => {\r\n            // 检查响应并显示详细信息\r\n            if (response && response.success) {\r\n              this.$modal.msgSuccess('推送成功')\r\n\r\n              // 如果后端生成了新的报告URL，可以更新本地记录\r\n              if (response.data && response.data.report_url && !this.currentRecord.reportOssUrl) {\r\n                this.currentRecord.reportOssUrl = response.data.report_url\r\n                console.log('更新本地报告URL:', response.data.report_url)\r\n              }\r\n            } else {\r\n              this.$modal.msgSuccess('推送完成')\r\n            }\r\n\r\n            this.pushDialogVisible = false\r\n            this.pushLoading = false\r\n          }).catch(error => {\r\n            console.error('推送失败:', error)\r\n            if (error.response && error.response.data && error.response.data.msg) {\r\n              this.$modal.msgError('推送失败: ' + error.response.data.msg)\r\n            } else {\r\n              this.$modal.msgError('推送失败，请重试')\r\n            }\r\n            this.pushLoading = false\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    /** 提交定时推送 */\r\n    submitSchedulePush() {\r\n      this.$refs['schedulePushForm'].validate(valid => {\r\n        if (valid) {\r\n          this.schedulePushLoading = true\r\n          const taskData = {\r\n            requirement_id: this.currentRecord.id,\r\n            push_url: this.schedulePushForm.push_url,\r\n            push_time: this.schedulePushForm.push_time,\r\n            frequency: this.schedulePushForm.frequency,\r\n            push_content: this.schedulePushForm.push_content\r\n          }\r\n\r\n          createTimedTask(taskData).then(response => {\r\n            this.$modal.msgSuccess('定时推送任务创建成功')\r\n            this.schedulePushDialogVisible = false\r\n            this.schedulePushLoading = false\r\n          }).catch(() => {\r\n            this.schedulePushLoading = false\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      let ids, names\r\n\r\n      // 判断是否为批量删除（无参数或参数为事件对象）\r\n      if (!row || row instanceof Event) {\r\n        // 批量删除\r\n        if (!this.ids || this.ids.length === 0) {\r\n          this.$modal.msgError('请先选择要删除的记录')\r\n          return\r\n        }\r\n        // 过滤并验证IDs\r\n        ids = this.ids.filter(id => id != null && !isNaN(id)).map(id => parseInt(id))\r\n        if (ids.length === 0) {\r\n          this.$modal.msgError('选中的记录ID无效，无法删除')\r\n          return\r\n        }\r\n        names = this.recordList.filter(item => ids.includes(item.id)).map(item => item.requirementName).join('、')\r\n      } else {\r\n        // 单个删除\r\n        if (!row.id || row.id == null) {\r\n          this.$modal.msgError('记录ID无效，无法删除')\r\n          return\r\n        }\r\n        ids = [parseInt(row.id)]\r\n        names = row.requirementName || '未知记录'\r\n      }\r\n      this.$modal.confirm(`是否确认删除分析记录\"${names}\"？`).then(() => {\r\n        // 根据IDs数量判断是单个删除还是批量删除\r\n        if (ids.length === 1) {\r\n          // 单个删除\r\n          return deleteAnalysisTask(ids[0])\r\n        } else {\r\n          // 批量删除\r\n          return batchDeleteAnalysisTasks(ids)\r\n        }\r\n      }).then(response => {\r\n        console.log('🗑️ 删除操作成功，响应:', response)\r\n\r\n        // 检查是否需要重置分页（物理删除后需要重新计算分页）\r\n        const currentPageRecords = this.recordList.length\r\n        const deletedCount = ids.length\r\n\r\n        // 如果删除后当前页可能没有记录了，且不是第一页，则回到上一页\r\n        if (currentPageRecords <= deletedCount && this.queryParams.page_num > 1) {\r\n          this.queryParams.page_num = this.queryParams.page_num - 1\r\n          console.log('🔄 删除后回到上一页:', this.queryParams.page_num)\r\n        }\r\n\r\n        // 重置选中状态\r\n        this.ids = []\r\n        this.single = true\r\n        this.multiple = true\r\n\r\n        // 物理删除成功后，直接重新获取数据以确保数据一致性\r\n        this.getList()\r\n\r\n        this.$modal.msgSuccess('删除成功')\r\n      }).catch(error => {\r\n        if (error.response && error.response.data) {\r\n          if (error.response.data.msg) {\r\n            this.$modal.msgError(error.response.data.msg)\r\n          } else if (error.response.data.message) {\r\n            this.$modal.msgError(error.response.data.message)\r\n          } else if (error.response.data.detail) {\r\n            // 处理FastAPI的验证错误\r\n            if (Array.isArray(error.response.data.detail)) {\r\n              const details = error.response.data.detail.map(d => d.msg || d.message || JSON.stringify(d)).join(', ')\r\n              this.$modal.msgError(`删除失败: ${details}`)\r\n            } else {\r\n              this.$modal.msgError(`删除失败: ${error.response.data.detail}`)\r\n            }\r\n          } else {\r\n            this.$modal.msgError(`删除失败: ${JSON.stringify(error.response.data)}`)\r\n          }\r\n        } else if (error.message) {\r\n          this.$modal.msgError(`删除失败: ${error.message}`)\r\n        } else {\r\n          this.$modal.msgError('删除失败，请重试')\r\n        }\r\n      })\r\n    },\r\n\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.$modal.confirm('是否确认导出所有分析记录数据项？').then(() => {\r\n        const params = {}\r\n\r\n        // 添加搜索条件（使用后端期望的下划线命名）\r\n        if (this.queryParams.requirementName) {\r\n          params.requirement_name = this.queryParams.requirementName\r\n        }\r\n        if (this.queryParams.entityKeyword) {\r\n          params.entity_keyword = this.queryParams.entityKeyword\r\n        }\r\n        if (this.queryParams.analysisStatus !== null && this.queryParams.analysisStatus !== undefined && this.queryParams.analysisStatus !== '') {\r\n          params.analysis_status = this.queryParams.analysisStatus\r\n        }\r\n\r\n        // 添加时间范围\r\n        if (this.dateRange && this.dateRange.length === 2) {\r\n          params.create_time_start = this.dateRange[0]\r\n          params.create_time_end = this.dateRange[1]\r\n        }\r\n\r\n        return exportRequirements(params)\r\n      }).then(response => {\r\n        // 处理文件下载\r\n        const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })\r\n        const url = window.URL.createObjectURL(blob)\r\n        const link = document.createElement('a')\r\n        link.href = url\r\n        link.download = `分析记录_${new Date().toISOString().slice(0, 10)}.xlsx`\r\n        document.body.appendChild(link)\r\n        link.click()\r\n        document.body.removeChild(link)\r\n        window.URL.revokeObjectURL(url)\r\n\r\n        this.$modal.msgSuccess('导出成功')\r\n      }).catch(error => {\r\n        if (error.response && error.response.data && error.response.data.msg) {\r\n          this.$modal.msgError(error.response.data.msg)\r\n        } else {\r\n          this.$modal.msgError('导出失败，请重试')\r\n        }\r\n      })\r\n    },\r\n\r\n    /** 导出报告 */\r\n    handleExportReport() {\r\n      this.$modal.msgSuccess('报告导出成功')\r\n    },\r\n\r\n    /** 获取状态标签类型 */\r\n    getStatusTagType(status) {\r\n      const statusMap = {\r\n        0: 'info', // 待生成 - 灰色\r\n        1: 'warning', // 生成中 - 橙色\r\n        2: 'success', // 已生成 - 绿色\r\n        '-1': 'danger' // 生成失败 - 红色\r\n      }\r\n      return statusMap[status] || 'info'\r\n    },\r\n\r\n    /** 获取状态文本 */\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        0: '待生成',\r\n        1: '生成中',\r\n        2: '已生成',\r\n        '-1': '生成失败'\r\n      }\r\n      return statusMap[status] || '未知'\r\n    },\r\n\r\n    /** 获取日志级别标签类型 */\r\n    getLogLevelTagType(level) {\r\n      const levelMap = {\r\n        'info': 'info',\r\n        'success': 'success',\r\n        'warning': 'warning',\r\n        'error': 'danger'\r\n      }\r\n      return levelMap[level] || 'info'\r\n    },\r\n\r\n    // ==================== 定时任务相关方法 ====================\r\n\r\n    // 处理定时推送按钮点击\r\n    handleTimedPush() {\r\n      this.timedTaskDialogVisible = true\r\n      // 打开定时推送弹窗时加载定时任务列表\r\n      this.loadTimedTaskList()\r\n    },\r\n\r\n    // 关闭定时任务弹窗\r\n    closeTimedTaskDialog() {\r\n      this.timedTaskDialogVisible = false\r\n    },\r\n\r\n    // 处理创建定时任务\r\n    handleCreateTimedTask() {\r\n      this.resetTaskForm()\r\n      this.editingTaskIndex = -1 // 设置为新建模式\r\n      this.loadOpinionTaskList() // 加载舆情任务列表\r\n      this.createTaskDialogVisible = true\r\n    },\r\n\r\n    // 处理添加定时任务按钮\r\n    handleAddTimedTask() {\r\n      this.resetTaskForm()\r\n      this.editingTaskIndex = -1 // 设置为新建模式\r\n      this.loadOpinionTaskList() // 加载舆情任务列表\r\n      this.createTaskDialogVisible = true\r\n    },\r\n\r\n    // 加载舆情任务列表（用于定时推送绑定）\r\n    async loadOpinionTaskList() {\r\n      try {\r\n        const response = await getAnalysisTaskList({\r\n          page_num: 1,\r\n          page_size: 100\r\n        })\r\n\r\n        if (response.success) {\r\n          // 处理分页数据，从 opinion_task 表获取数据\r\n          let tasks = []\r\n\r\n          if (response.data && response.data.rows) {\r\n            tasks = response.data.rows\r\n          } else if (Array.isArray(response.data)) {\r\n            tasks = response.data\r\n          } else if (response.rows) {\r\n            tasks = response.rows\r\n          }\r\n\r\n          // 映射任务数据到需求列表格式\r\n          this.requirementList = tasks.map(task => ({\r\n            id: task.id,\r\n            requirementName: task.requirementName || task.requirement_name || `任务${task.id}`,\r\n            taskId: task.id,\r\n            keywords: task.keywords || task.entity_keyword,\r\n            specificRequirement: task.specificRequirement || task.specific_requirement,\r\n            createTime: task.createTime || task.create_time\r\n          }))\r\n        } else {\r\n          this.$message.error(response.msg || '获取舆情任务列表失败')\r\n          this.requirementList = []\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('加载舆情任务列表失败')\r\n        this.requirementList = []\r\n      }\r\n    },\r\n\r\n    // 加载定时任务列表\r\n    async loadTimedTaskList() {\r\n      try {\r\n        const response = await getTimedTaskList()\r\n\r\n        if (response.success) {\r\n          // 处理分页数据 - 后端返回的是PageResponseModel格式\r\n\r\n          if (response.data && response.data.rows) {\r\n            this.timedTaskList = response.data.rows.map(task => ({\r\n              id: task.id,\r\n              requirementId: task.requirementId,\r\n              name: task.taskName,\r\n              description: task.taskDescription,\r\n              executeTime: task.executeTime,\r\n              executeDateTime: task.frequency === 'once' && task.execute_date ? `${task.execute_date} ${task.executeTime}` : '',\r\n              frequency: task.frequency,\r\n              status: task.status === 'running' ? 'running' : 'pending',\r\n              pushUrl: task.pushUrl\r\n            }))\r\n          } else if (response.data && response.data.records && Array.isArray(response.data.records)) {\r\n            this.timedTaskList = response.data.records.map(task => ({\r\n              id: task.id,\r\n              requirementId: task.requirementId,\r\n              name: task.taskName,\r\n              description: task.taskDescription,\r\n              executeTime: task.executeTime,\r\n              executeDateTime: task.frequency === 'once' && task.execute_date ? `${task.execute_date} ${task.executeTime}` : '',\r\n              frequency: task.frequency,\r\n              status: task.status === 'running' ? 'running' : 'pending',\r\n              pushUrl: task.pushUrl\r\n            }))\r\n          } else if (Array.isArray(response.data)) {\r\n            this.timedTaskList = response.data.map(task => ({\r\n              id: task.id,\r\n              requirementId: task.requirementId,\r\n              name: task.taskName,\r\n              description: task.taskDescription,\r\n              executeTime: task.executeTime,\r\n              executeDateTime: task.frequency === 'once' && task.execute_date ? `${task.execute_date} ${task.executeTime}` : '',\r\n              frequency: task.frequency,\r\n              status: task.status === 'running' ? 'running' : 'pending',\r\n              pushUrl: task.pushUrl\r\n            }))\r\n          } else if (response.rows && Array.isArray(response.rows)) {\r\n            this.timedTaskList = response.rows.map(task => ({\r\n              id: task.id,\r\n              requirementId: task.requirementId,\r\n              name: task.taskName,\r\n              description: task.taskDescription,\r\n              executeTime: task.executeTime,\r\n              executeDateTime: task.frequency === 'once' && task.execute_date ? `${task.execute_date} ${task.executeTime}` : '',\r\n              frequency: task.frequency,\r\n              status: task.status === 'running' ? 'running' : 'pending',\r\n              pushUrl: task.pushUrl\r\n            }))\r\n          } else if (response.records && Array.isArray(response.records)) {\r\n            this.timedTaskList = response.records.map(task => ({\r\n              id: task.id,\r\n              requirementId: task.requirementId,\r\n              name: task.taskName,\r\n              description: task.taskDescription,\r\n              executeTime: task.executeTime,\r\n              executeDateTime: task.frequency === 'once' && task.execute_date ? `${task.execute_date} ${task.executeTime}` : '',\r\n              frequency: task.frequency,\r\n              status: task.status === 'running' ? 'running' : 'pending',\r\n              pushUrl: task.pushUrl\r\n            }))\r\n          } else {\r\n            this.timedTaskList = []\r\n          }\r\n        } else {\r\n          this.$message.error(response.msg || '获取定时任务列表失败')\r\n          this.timedTaskList = []\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('加载定时任务列表失败')\r\n        this.timedTaskList = []\r\n      }\r\n    },\r\n\r\n    // 关闭创建任务弹窗\r\n    closeCreateTaskDialog() {\r\n      this.createTaskDialogVisible = false\r\n      this.resetTaskForm()\r\n      this.editingTaskIndex = -1 // 重置编辑状态\r\n    },\r\n\r\n    // 重置任务表单\r\n    resetTaskForm() {\r\n      this.taskForm = {\r\n        requirement_id: '',\r\n        name: '',\r\n        description: '',\r\n        execute_time: '16:00',\r\n        execute_date_time: '',\r\n        frequency: 'daily',\r\n        push_url: ''\r\n      }\r\n    },\r\n\r\n    // 保存并运行任务\r\n    async saveAndRunTask() {\r\n      if (!this.validateTaskForm()) {\r\n        return\r\n      }\r\n\r\n      try {\r\n        const taskData = this.buildTaskData()\r\n\r\n        if (this.editingTaskIndex === -1) {\r\n          // 新建任务\r\n          const response = await createTimedTask(taskData)\r\n          if (response.success) {\r\n            this.$message.success('任务已保存并开始运行')\r\n            // 重新加载任务列表\r\n            await this.loadTimedTaskList()\r\n            // 更新任务状态为运行中\r\n            if (response.data && response.data.id) {\r\n              await updateTaskStatus(response.data.id, 'running')\r\n              await this.loadTimedTaskList()\r\n            }\r\n          } else {\r\n            this.$message.error('创建任务失败：' + response.msg)\r\n            return\r\n          }\r\n        } else {\r\n          // 编辑任务\r\n          const task = this.timedTaskList[this.editingTaskIndex]\r\n          const response = await updateTimedTask(task.id, taskData)\r\n\r\n          if (response.success) {\r\n            // 更新任务状态为运行中\r\n            await updateTaskStatus(task.id, 'running')\r\n            this.$message.success('任务已更新并开始运行')\r\n            // 重新加载任务列表\r\n            await this.loadTimedTaskList()\r\n          } else {\r\n            this.$message.error('更新任务失败：' + response.msg)\r\n            return\r\n          }\r\n        }\r\n\r\n        this.createTaskDialogVisible = false\r\n        this.resetTaskForm()\r\n        this.editingTaskIndex = -1 // 重置编辑状态\r\n      } catch (error) {\r\n        this.$message.error('保存任务失败，请重试')\r\n      }\r\n    },\r\n\r\n    // 保存任务计划\r\n    async saveTaskPlan() {\r\n      if (!this.validateTaskForm()) {\r\n        return\r\n      }\r\n\r\n      try {\r\n        const taskData = this.buildTaskData()\r\n\r\n        if (this.editingTaskIndex === -1) {\r\n          // 新建任务\r\n          const response = await createTimedTask(taskData)\r\n          if (response.success) {\r\n            this.$message.success('任务计划已保存')\r\n            // 重新加载任务列表\r\n            await this.loadTimedTaskList()\r\n          } else {\r\n            this.$message.error('保存任务计划失败：' + response.msg)\r\n            return\r\n          }\r\n        } else {\r\n          // 编辑任务\r\n          const task = this.timedTaskList[this.editingTaskIndex]\r\n          const response = await updateTimedTask(task.id, taskData)\r\n\r\n          if (response.success) {\r\n            this.$message.success('任务计划已更新')\r\n            // 重新加载任务列表\r\n            await this.loadTimedTaskList()\r\n          } else {\r\n            this.$message.error('更新任务计划失败：' + response.msg)\r\n            return\r\n          }\r\n        }\r\n\r\n        this.createTaskDialogVisible = false\r\n        this.resetTaskForm()\r\n        this.editingTaskIndex = -1 // 重置编辑状态\r\n      } catch (error) {\r\n        this.$message.error('保存任务计划失败，请重试')\r\n      }\r\n    },\r\n\r\n    // 验证任务表单\r\n    validateTaskForm() {\r\n      if (!this.taskForm.requirement_id) {\r\n        this.$message.warning('请选择要关联的舆情分析任务')\r\n        return false\r\n      }\r\n\r\n      if (!this.taskForm.name || !this.taskForm.name.trim()) {\r\n        this.$message.warning('请输入任务名称')\r\n        return false\r\n      }\r\n\r\n      if (!this.taskForm.description || !this.taskForm.description.trim()) {\r\n        this.$message.warning('请输入任务描述')\r\n        return false\r\n      }\r\n\r\n      // 验证执行时间\r\n      if (this.taskForm.frequency === 'once') {\r\n        if (!this.taskForm.execute_date_time) {\r\n          this.$message.warning('请选择执行日期和时间')\r\n          return false\r\n        }\r\n        // 检查是否是未来时间\r\n        const executeTime = new Date(this.taskForm.execute_date_time)\r\n        const now = new Date()\r\n        if (executeTime <= now) {\r\n          this.$message.warning('执行时间必须是未来时间')\r\n          return false\r\n        }\r\n      } else {\r\n        if (!this.taskForm.execute_time) {\r\n          this.$message.warning('请选择执行时间')\r\n          return false\r\n        }\r\n      }\r\n\r\n      return true\r\n    },\r\n\r\n    // 构建任务数据\r\n    buildTaskData() {\r\n      // 处理执行时间：对于一次性任务，只提取时间部分；对于周期性任务，直接使用时间\r\n      let executeTime\r\n      if (this.taskForm.frequency === 'once') {\r\n        // 一次性任务：从完整日期时间中提取时间部分 (HH:MM)\r\n        if (this.taskForm.execute_date_time) {\r\n          const dateTime = new Date(this.taskForm.execute_date_time)\r\n          executeTime = `${dateTime.getHours().toString().padStart(2, '0')}:${dateTime.getMinutes().toString().padStart(2, '0')}`\r\n        } else {\r\n          executeTime = '09:00' // 默认时间\r\n        }\r\n      } else {\r\n        // 周期性任务：直接使用时间\r\n        executeTime = this.taskForm.execute_time || '09:00'\r\n      }\r\n\r\n      const taskData = {\r\n        requirement_id: this.taskForm.requirement_id,\r\n        task_name: this.taskForm.name,\r\n        task_type: 'scheduled',\r\n        frequency: this.taskForm.frequency, // 使用 frequency 而不是 schedule_type\r\n        execute_time: executeTime, // 只存储时间部分 (HH:MM)\r\n        task_description: this.taskForm.description,\r\n        push_url: this.taskForm.push_url || ''\r\n      }\r\n\r\n      // 对于一次性任务，可能需要额外的日期信息\r\n      if (this.taskForm.frequency === 'once' && this.taskForm.execute_date_time) {\r\n        taskData['execute_date'] = this.taskForm.execute_date_time.split(' ')[0] // 提取日期部分 (YYYY-MM-DD)\r\n      }\r\n\r\n      return taskData\r\n    },\r\n\r\n    // 删除任务\r\n    async deleteTask(index) {\r\n      try {\r\n        const task = this.timedTaskList[index]\r\n\r\n        if (!task) {\r\n          this.$message.error('任务数据不存在')\r\n          return\r\n        }\r\n\r\n        await this.$confirm(`确定要删除任务「${task.name}」吗？`, '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        })\r\n\r\n        const response = await deleteTimedTask(task.id)\r\n\r\n        if (response.success) {\r\n          this.$message.success('删除成功')\r\n          // 重新加载任务列表\r\n          await this.loadTimedTaskList()\r\n        } else {\r\n          this.$message.error('删除失败：' + (response.msg || response.message || '未知错误'))\r\n        }\r\n      } catch (error) {\r\n        if (error === 'cancel') {\r\n          return\r\n        }\r\n        this.$message.error('删除任务失败，请重试')\r\n      }\r\n    },\r\n\r\n    // 编辑任务\r\n    editTask(index) {\r\n      const task = this.timedTaskList[index]\r\n\r\n      if (!task) {\r\n        this.$message.error('任务数据不存在')\r\n        return\r\n      }\r\n\r\n      this.editingTaskIndex = index\r\n      this.taskForm = {\r\n        requirement_id: task.requirementId || '',\r\n        name: task.name || '',\r\n        description: task.description || '',\r\n        execute_time: task.executeTime || '16:00',\r\n        execute_date_time: task.executeDateTime || '',\r\n        frequency: task.frequency || 'daily',\r\n        push_url: task.pushUrl || ''\r\n      }\r\n\r\n      this.loadOpinionTaskList() // 加载舆情任务列表\r\n      this.createTaskDialogVisible = true\r\n    },\r\n\r\n    // 切换任务状态\r\n    async toggleTaskStatus(index) {\r\n      try {\r\n        const task = this.timedTaskList[index]\r\n        const newStatus = task.status === 'running' ? 'pending' : 'running'\r\n\r\n        const response = await updateTaskStatus(task.id, newStatus)\r\n\r\n        if (response.success) {\r\n          if (newStatus === 'running') {\r\n            this.$message.success(`任务「${task.name}」已启动`)\r\n          } else {\r\n            this.$message.info(`任务「${task.name}」已暂停`)\r\n          }\r\n          // 重新加载任务列表\r\n          await this.loadTimedTaskList()\r\n        } else {\r\n          this.$message.error('状态更新失败：' + response.msg)\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('状态更新失败，请重试')\r\n      }\r\n    },\r\n\r\n    // 预览任务\r\n    previewTask(index) {\r\n      const task = this.timedTaskList[index]\r\n      if (!task) {\r\n        this.$message.error('任务数据不存在')\r\n        return\r\n      }\r\n      this.taskPreviewDialog.taskData = task\r\n      this.taskPreviewDialog.visible = true\r\n    },\r\n\r\n    // 获取任务计划文本\r\n    getTaskScheduleText(task) {\r\n      if (!task) return ''\r\n\r\n      if (task.frequency === 'once') {\r\n        // 一次性任务显示具体执行时间\r\n        return `仅一次 ${task.executeDateTime || task.executeTime}`\r\n      } else {\r\n        // 周期性任务显示频率和时间\r\n        const frequencyText = this.getFrequencyText(task.frequency)\r\n        return `${frequencyText} ${task.executeTime}`\r\n      }\r\n    },\r\n\r\n    // 获取频率文本\r\n    getFrequencyText(frequency) {\r\n      const frequencyMap = {\r\n        'once': '仅一次',\r\n        'daily': '每天',\r\n        'weekly': '每周',\r\n        'monthly': '每月'\r\n      }\r\n      return frequencyMap[frequency] || frequency\r\n    },\r\n\r\n    // 修改计划\r\n    modifyPlan() {\r\n      // 这里可以添加修改计划的逻辑\r\n      this.$message.info('修改计划功能待实现')\r\n    },\r\n\r\n    /** 时间格式化 */\r\n    parseTime\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n.mb8 {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.report-content {\r\n  min-height: 400px;\r\n\r\n  .analysis-results {\r\n    margin-top: 20px;\r\n\r\n    .statistic-value {\r\n      font-size: 24px;\r\n      font-weight: bold;\r\n      text-align: center;\r\n      padding: 20px 0;\r\n\r\n      &.positive {\r\n        color: #67c23a;\r\n      }\r\n\r\n      &.negative {\r\n        color: #f56c6c;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.el-table .cell {\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.small-padding .cell {\r\n  padding-left: 5px;\r\n  padding-right: 5px;\r\n}\r\n\r\n.fixed-width .el-button--mini {\r\n  margin-right: 5px;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n\r\n// 状态标签样式\r\n.el-tag {\r\n  &.el-tag--info {\r\n    background-color: #f4f4f5;\r\n    border-color: #e9e9eb;\r\n    color: #909399;\r\n  }\r\n\r\n  &.el-tag--warning {\r\n    background-color: #fdf6ec;\r\n    border-color: #f5dab1;\r\n    color: #e6a23c;\r\n  }\r\n\r\n  &.el-tag--success {\r\n    background-color: #f0f9ff;\r\n    border-color: #c6f7d0;\r\n    color: #67c23a;\r\n  }\r\n\r\n  &.el-tag--danger {\r\n    background-color: #fef0f0;\r\n    border-color: #fbc4c4;\r\n    color: #f56c6c;\r\n  }\r\n}\r\n\r\n// 操作按钮样式\r\n.el-button--text {\r\n  padding: 0;\r\n  margin-right: 10px;\r\n\r\n  &:last-child {\r\n    margin-right: 0;\r\n  }\r\n}\r\n\r\n// 搜索表单样式\r\n.el-form--inline .el-form-item {\r\n  margin-right: 10px;\r\n}\r\n\r\n// 定时任务抽屉样式\r\n.timed-task-drawer {\r\n  .drawer-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    width: 100%;\r\n\r\n    .drawer-title {\r\n      font-size: 16px;\r\n      font-weight: 500;\r\n    }\r\n\r\n    .add-task-btn {\r\n      margin-left: auto;\r\n    }\r\n  }\r\n\r\n  .drawer-content {\r\n    padding: 20px;\r\n    height: calc(100vh - 120px);\r\n    overflow-y: auto;\r\n\r\n    .empty-state {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      justify-content: center;\r\n      height: 100%;\r\n      text-align: center;\r\n\r\n      .empty-content {\r\n        .empty-icon {\r\n          margin-bottom: 20px;\r\n        }\r\n\r\n        .empty-text {\r\n          font-size: 14px;\r\n          color: #999;\r\n          margin-bottom: 20px;\r\n        }\r\n\r\n        .create-btn {\r\n          padding: 10px 20px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .task-list {\r\n      .task-items {\r\n        .task-item {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          padding: 16px;\r\n          border: 1px solid #e8e8e8;\r\n          border-radius: 8px;\r\n          margin-bottom: 12px;\r\n          background: #fff;\r\n\r\n          &:hover {\r\n            border-color: #409eff;\r\n            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\r\n          }\r\n\r\n          .task-info {\r\n            flex: 1;\r\n\r\n            .task-header {\r\n              display: flex;\r\n              justify-content: space-between;\r\n              align-items: center;\r\n              margin-bottom: 8px;\r\n\r\n              .task-name {\r\n                font-size: 14px;\r\n                font-weight: 500;\r\n                color: #333;\r\n              }\r\n\r\n              .task-status {\r\n                padding: 2px 8px;\r\n                border-radius: 4px;\r\n                font-size: 12px;\r\n\r\n                &.status-running {\r\n                  background: #f0f9ff;\r\n                  color: #1890ff;\r\n                }\r\n\r\n                &.status-pending {\r\n                  background: #f6f6f6;\r\n                  color: #666;\r\n                }\r\n              }\r\n            }\r\n\r\n            .task-schedule {\r\n              display: flex;\r\n              align-items: center;\r\n              font-size: 12px;\r\n              color: #666;\r\n\r\n              i {\r\n                margin-right: 4px;\r\n              }\r\n            }\r\n          }\r\n\r\n          .task-actions {\r\n            display: flex;\r\n            gap: 8px;\r\n\r\n            .el-button {\r\n              padding: 4px;\r\n              min-width: auto;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 创建任务弹窗样式\r\n.create-task-dialog {\r\n  .task-form {\r\n    .task-requirement-section,\r\n    .execute-time-section {\r\n      margin-bottom: 24px;\r\n\r\n      .section-label {\r\n        font-size: 14px;\r\n        font-weight: 500;\r\n        margin-bottom: 16px;\r\n        color: #333;\r\n\r\n        .required {\r\n          color: #f56c6c;\r\n          margin-left: 4px;\r\n        }\r\n      }\r\n\r\n      .form-group {\r\n        margin-bottom: 16px;\r\n\r\n        .input-label {\r\n          font-size: 13px;\r\n          color: #666;\r\n          margin-bottom: 8px;\r\n        }\r\n\r\n        .task-name-input,\r\n        .task-description-input {\r\n          width: 100%;\r\n        }\r\n      }\r\n\r\n      .time-selector {\r\n        display: flex;\r\n        gap: 12px;\r\n\r\n        .frequency-select {\r\n          width: 120px;\r\n        }\r\n\r\n        .datetime-picker,\r\n        .time-picker {\r\n          flex: 1;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .dialog-footer {\r\n    text-align: right;\r\n\r\n    .el-button {\r\n      margin-left: 8px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}