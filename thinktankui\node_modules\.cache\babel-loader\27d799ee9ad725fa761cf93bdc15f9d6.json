{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\analyze-record\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\analyze-record\\index.vue", "mtime": 1753421184867}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\babel.config.js", "mtime": 1750933247176}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753065270161}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753065271554}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753065270161}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753065272328}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_opinionAnalysis", "require", "_ruoyi", "_default", "exports", "default", "name", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "recordList", "date<PERSON><PERSON><PERSON>", "queryParams", "page_num", "page_size", "requirementName", "entityKeyword", "analysisStatus", "logDialogVisible", "logLoading", "logList", "reportDialogVisible", "reportLoading", "reportData", "pushDialogVisible", "pushLoading", "pushForm", "push_url", "push_content", "pushRules", "required", "message", "trigger", "schedulePushDialogVisible", "schedulePushLoading", "schedulePushForm", "push_time", "frequency", "schedulePushRules", "currentRecord", "timedTaskDialogVisible", "timedTaskList", "createTaskDialogVisible", "editingTaskIndex", "taskForm", "requirement_id", "description", "execute_time", "execute_date_time", "requirementList", "taskPreviewDialog", "visible", "taskData", "created", "getList", "methods", "paginationParams", "_this", "page", "limit", "params", "trim", "task_name", "task_description", "undefined", "statusMap", "status", "length", "create_time_start", "create_time_end", "console", "log", "getAnalysisTaskList", "then", "response", "requirements", "records", "rows", "Array", "isArray", "filter", "item", "id", "map", "parseInt", "isNaN", "mappedItem", "taskName", "taskDescription", "specificRequirement", "createTime", "create_time", "mapTaskStatus", "reportOssUrl", "report_oss_url", "catch", "error", "$modal", "msgError", "taskStatus", "mapAnalysisStatus", "onRequirementNameInput", "value", "onEntityKeywordInput", "onAnalysisStatusChange", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectionChange", "selection", "handleViewLogs", "row", "_this2", "getAnalysisProgress", "taskId", "handleViewReport", "_this3", "setTimeout", "totalArticles", "Math", "floor", "random", "sentiment", "positive", "neutral", "negative", "handleReport", "window", "open", "msg", "handlePushNow", "msgWarning", "concat", "handleSchedulePush", "submitPush", "_this4", "$refs", "validate", "valid", "validatePushUrl", "pushData", "target_url", "report_data", "dataSources", "pushTime", "Date", "toISOString", "analysis_results", "summary", "reportUrl", "push_type", "pushReport", "success", "msgSuccess", "report_url", "submitSchedulePush", "_this5", "createTimedTask", "handleDelete", "_this6", "names", "Event", "includes", "join", "confirm", "deleteAnalysisTask", "batchDeleteAnalysisTasks", "currentPageRecords", "deletedCount", "detail", "details", "d", "JSON", "stringify", "handleExport", "_this7", "requirement_name", "entity_keyword", "analysis_status", "exportRequirements", "blob", "Blob", "type", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "slice", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleExportReport", "fullUrl", "getStatusTagType", "getStatusText", "getLogLevelTagType", "level", "levelMap", "handleTimedPush", "loadTimedTaskList", "closeTimedTaskDialog", "handleCreateTimedTask", "resetTaskForm", "loadOpinionTaskList", "handleAddTimedTask", "_this8", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "tasks", "_t", "w", "_context", "n", "p", "v", "task", "keywords", "specific_requirement", "$message", "a", "_this9", "_callee2", "_t2", "_context2", "getTimedTaskList", "requirementId", "executeTime", "executeDateTime", "execute_date", "pushUrl", "closeCreateTaskDialog", "saveAndRunTask", "_this0", "_callee3", "_response", "_t3", "_context3", "validateTaskForm", "buildTaskData", "updateTaskStatus", "updateTimedTask", "saveTaskPlan", "_this1", "_callee4", "_response2", "_t4", "_context4", "warning", "now", "dateTime", "getHours", "toString", "padStart", "getMinutes", "task_type", "split", "deleteTask", "index", "_this10", "_callee5", "_t5", "_context5", "$confirm", "confirmButtonText", "cancelButtonText", "deleteTimedTask", "editTask", "toggleTaskStatus", "_this11", "_callee6", "newStatus", "_t6", "_context6", "info", "previewTask", "getTaskScheduleText", "frequencyText", "getFrequencyText", "frequencyMap", "modifyPlan", "parseTime"], "sources": ["src/views/analyze-record/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 搜索区域 -->\r\n    <el-form v-show=\"showSearch\" ref=\"queryForm\" :model=\"queryParams\" size=\"small\" :inline=\"true\" label-width=\"68px\">\r\n\r\n      <el-form-item label=\"需求名称\" prop=\"requirementName\">\r\n        <el-input\r\n          id=\"requirementName\"\r\n          v-model=\"queryParams.requirementName\"\r\n          placeholder=\"请输入需求名称\"\r\n          clearable\r\n          @input=\"onRequirementNameInput\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"关键词\" prop=\"entityKeyword\">\r\n        <el-input\r\n          id=\"entityKeyword\"\r\n          v-model=\"queryParams.entityKeyword\"\r\n          placeholder=\"请输入实体关键词\"\r\n          clearable\r\n          @input=\"onEntityKeywordInput\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"分析状态\" prop=\"analysisStatus\">\r\n        <el-select\r\n          id=\"analysisStatus\"\r\n          v-model=\"queryParams.analysisStatus\"\r\n          placeholder=\"请选择分析状态\"\r\n          clearable\r\n          @change=\"onAnalysisStatusChange\"\r\n        >\r\n          <el-option label=\"未开始\" value=\"0\" />\r\n          <el-option label=\"分析中\" value=\"1\" />\r\n          <el-option label=\"已完成\" value=\"2\" />\r\n          <el-option label=\"失败\" value=\"3\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"创建时间\">\r\n        <el-date-picker\r\n          id=\"create_time_range\"\r\n          v-model=\"dateRange\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 操作按钮区域 -->\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          v-hasPermi=\"['opinion:record:export']\"\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-time\"\r\n          size=\"mini\"\r\n          @click=\"handleTimedPush\"\r\n        >定时推送</el-button>\r\n      </el-col>\r\n      <right-toolbar :show-search.sync=\"showSearch\" @queryTable=\"getList\" />\r\n    </el-row>\r\n\r\n    <!-- 数据表格 -->\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"recordList\"\r\n      :default-sort=\"{prop: 'createTime', order: 'descending'}\"\r\n      @selection-change=\"handleSelectionChange\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"ID\" align=\"center\" prop=\"id\" width=\"80\" />\r\n      <el-table-column label=\"需求名称\" align=\"center\" prop=\"requirementName\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"关键词\" align=\"center\" prop=\"entityKeyword\" width=\"120\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"具体需求\" align=\"center\" prop=\"specificRequirement\" width=\"200\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"生成状态\" align=\"center\" prop=\"status\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            :type=\"getStatusTagType(scope.row.status)\"\r\n            size=\"mini\"\r\n          >\r\n            {{ getStatusText(scope.row.status) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"380\">\r\n        <template slot-scope=\"scope\">\r\n\r\n          <el-button\r\n            v-if=\"scope.row.status === 2\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-notebook-1\"\r\n            @click=\"handleReport(scope.row)\"\r\n          >报告</el-button>\r\n          <el-button\r\n            v-if=\"scope.row.status === 2\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-s-promotion\"\r\n            @click=\"handlePushNow(scope.row)\"\r\n          >立即推送</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <!-- 分页 -->\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.page_num\"\r\n      :limit.sync=\"queryParams.page_size\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 分析日志对话框 -->\r\n    <el-dialog title=\"分析日志\" :visible.sync=\"logDialogVisible\" width=\"1000px\" append-to-body>\r\n      <el-table\r\n        v-loading=\"logLoading\"\r\n        :data=\"logList\"\r\n        height=\"400\"\r\n        :default-sort=\"{prop: 'createTime', order: 'descending'}\"\r\n      >\r\n        <el-table-column label=\"时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"级别\" align=\"center\" prop=\"logLevel\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag\r\n              :type=\"getLogLevelTagType(scope.row.logLevel)\"\r\n              size=\"mini\"\r\n            >\r\n              {{ scope.row.logLevel.toUpperCase() }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"步骤\" align=\"center\" prop=\"stepName\" width=\"120\" />\r\n        <el-table-column label=\"进度\" align=\"center\" prop=\"progressPercentage\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ scope.row.progressPercentage }}%</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"日志内容\" align=\"center\" prop=\"logMessage\" :show-overflow-tooltip=\"true\" />\r\n      </el-table>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"logDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 查看报告对话框 -->\r\n    <el-dialog title=\"分析报告\" :visible.sync=\"reportDialogVisible\" width=\"1200px\" append-to-body>\r\n      <div v-loading=\"reportLoading\" class=\"report-content\">\r\n        <div v-if=\"reportData\">\r\n          <el-descriptions title=\"基本信息\" :column=\"2\" border>\r\n            <el-descriptions-item label=\"需求名称\">{{ reportData.requirementName }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"关键词\">{{ reportData.entityKeyword }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"具体需求\" :span=\"2\">{{ reportData.specificRequirement }}</el-descriptions-item>\r\n          </el-descriptions>\r\n\r\n          <el-divider content-position=\"left\">分析结果</el-divider>\r\n          <div class=\"analysis-results\">\r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"8\">\r\n                <el-card shadow=\"never\">\r\n                  <div slot=\"header\">\r\n                    <span>文章总数</span>\r\n                  </div>\r\n                  <div class=\"statistic-value\">{{ reportData.totalArticles || 0 }}</div>\r\n                </el-card>\r\n              </el-col>\r\n              <el-col :span=\"8\">\r\n                <el-card shadow=\"never\">\r\n                  <div slot=\"header\">\r\n                    <span>正面情感</span>\r\n                  </div>\r\n                  <div class=\"statistic-value positive\">{{ reportData.sentiment?.positive || 0 }}</div>\r\n                </el-card>\r\n              </el-col>\r\n              <el-col :span=\"8\">\r\n                <el-card shadow=\"never\">\r\n                  <div slot=\"header\">\r\n                    <span>负面情感</span>\r\n                  </div>\r\n                  <div class=\"statistic-value negative\">{{ reportData.sentiment?.negative || 0 }}</div>\r\n                </el-card>\r\n              </el-col>\r\n            </el-row>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"handleExportReport\">导出报告</el-button>\r\n        <el-button @click=\"reportDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 立即推送对话框 -->\r\n    <el-dialog title=\"立即推送\" :visible.sync=\"pushDialogVisible\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"pushForm\" :model=\"pushForm\" :rules=\"pushRules\" label-width=\"100px\">\r\n        <el-form-item label=\"推送地址\" prop=\"push_url\">\r\n          <el-input v-model=\"pushForm.push_url\" placeholder=\"请输入推送地址\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"推送内容\" prop=\"push_content\">\r\n          <el-input\r\n            v-model=\"pushForm.push_content\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n            placeholder=\"请输入推送内容\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"pushDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" :loading=\"pushLoading\" @click=\"submitPush\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 定时推送对话框 -->\r\n    <el-dialog title=\"定时推送\" :visible.sync=\"schedulePushDialogVisible\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"schedulePushForm\" :model=\"schedulePushForm\" :rules=\"schedulePushRules\" label-width=\"100px\">\r\n        <el-form-item label=\"推送地址\" prop=\"push_url\">\r\n          <el-input v-model=\"schedulePushForm.push_url\" placeholder=\"请输入推送地址\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"推送时间\" prop=\"push_time\">\r\n          <el-date-picker\r\n            v-model=\"schedulePushForm.push_time\"\r\n            type=\"datetime\"\r\n            placeholder=\"选择推送时间\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            style=\"width: 100%\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"推送频率\" prop=\"frequency\">\r\n          <el-select v-model=\"schedulePushForm.frequency\" placeholder=\"请选择推送频率\">\r\n            <el-option label=\"仅一次\" value=\"once\" />\r\n            <el-option label=\"每日\" value=\"daily\" />\r\n            <el-option label=\"每周\" value=\"weekly\" />\r\n            <el-option label=\"每月\" value=\"monthly\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"推送内容\" prop=\"push_content\">\r\n          <el-input\r\n            v-model=\"schedulePushForm.push_content\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n            placeholder=\"请输入推送内容\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"schedulePushDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" :loading=\"schedulePushLoading\" @click=\"submitSchedulePush\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 定时任务抽屉 -->\r\n    <el-drawer\r\n      title=\"定时任务\"\r\n      :visible.sync=\"timedTaskDialogVisible\"\r\n      direction=\"rtl\"\r\n      size=\"600px\"\r\n      :before-close=\"closeTimedTaskDialog\"\r\n      custom-class=\"timed-task-drawer\"\r\n    >\r\n      <!-- 抽屉头部右侧按钮 -->\r\n      <div slot=\"title\" class=\"drawer-header\">\r\n        <span class=\"drawer-title\">定时任务</span>\r\n        <el-button\r\n          type=\"primary\"\r\n          size=\"mini\"\r\n          icon=\"el-icon-plus\"\r\n          class=\"add-task-btn\"\r\n          @click=\"handleAddTimedTask\"\r\n        >\r\n          定时任务\r\n        </el-button>\r\n      </div>\r\n\r\n      <!-- 抽屉内容 -->\r\n      <div class=\"drawer-content\">\r\n        <!-- 空状态 -->\r\n        <div v-if=\"timedTaskList.length === 0\" class=\"empty-state\">\r\n          <div class=\"empty-content\">\r\n            <!-- 空状态图标 -->\r\n            <div class=\"empty-icon\">\r\n              <svg width=\"120\" height=\"120\" viewBox=\"0 0 120 120\" fill=\"none\">\r\n                <!-- 文件夹图标 -->\r\n                <path d=\"M20 30h25l5-10h50v70H20V30z\" fill=\"#f0f0f0\" stroke=\"#d0d0d0\" stroke-width=\"2\" />\r\n                <path d=\"M25 35h70v50H25V35z\" fill=\"#fafafa\" stroke=\"#e0e0e0\" stroke-width=\"1\" />\r\n                <!-- 文档图标 -->\r\n                <rect x=\"35\" y=\"45\" width=\"30\" height=\"25\" fill=\"#ffffff\" stroke=\"#d0d0d0\" stroke-width=\"1\" rx=\"2\" />\r\n                <rect x=\"70\" y=\"50\" width=\"20\" height=\"15\" fill=\"#ffffff\" stroke=\"#d0d0d0\" stroke-width=\"1\" rx=\"2\" />\r\n                <!-- 装饰线条 -->\r\n                <line x1=\"40\" y1=\"52\" x2=\"60\" y2=\"52\" stroke=\"#e0e0e0\" stroke-width=\"1\" />\r\n                <line x1=\"40\" y1=\"57\" x2=\"55\" y2=\"57\" stroke=\"#e0e0e0\" stroke-width=\"1\" />\r\n                <line x1=\"40\" y1=\"62\" x2=\"58\" y2=\"62\" stroke=\"#e0e0e0\" stroke-width=\"1\" />\r\n                <line x1=\"75\" y1=\"55\" x2=\"85\" y2=\"55\" stroke=\"#e0e0e0\" stroke-width=\"1\" />\r\n                <line x1=\"75\" y1=\"60\" x2=\"82\" y2=\"60\" stroke=\"#e0e0e0\" stroke-width=\"1\" />\r\n              </svg>\r\n            </div>\r\n            <p class=\"empty-text\">暂无定时任务</p>\r\n            <el-button type=\"primary\" class=\"create-btn\" @click=\"handleCreateTimedTask\">\r\n              去创建\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 任务列表 -->\r\n        <div v-else class=\"task-list\">\r\n          <div v-if=\"timedTaskList.length === 0\" class=\"empty-task-list\">\r\n            <div class=\"empty-icon\">📅</div>\r\n            <div class=\"empty-text\">暂无定时任务</div>\r\n            <el-button type=\"primary\" size=\"small\" class=\"add-task-btn\" @click=\"handleAddTimedTask\">\r\n              添加任务\r\n            </el-button>\r\n          </div>\r\n          <div v-else class=\"task-items\">\r\n            <div v-for=\"(task, index) in timedTaskList\" :key=\"index\" class=\"task-item\">\r\n              <div class=\"task-info\">\r\n                <div class=\"task-header\">\r\n                  <div class=\"task-name\">{{ task.name }}</div>\r\n                  <div class=\"task-status\" :class=\"{ 'status-running': task.status === 'running', 'status-pending': task.status === 'pending' }\">\r\n                    {{ task.status === 'running' ? '运行中' : '待运行' }}\r\n                  </div>\r\n                </div>\r\n                <!-- 任务描述已隐藏 -->\r\n                <div class=\"task-schedule\">\r\n                  <i class=\"el-icon-time\" />\r\n                  <span>{{ getTaskScheduleText(task) }}</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"task-actions\">\r\n                <el-button type=\"text\" size=\"mini\" title=\"预览任务详情\" @click.stop=\"previewTask(index)\">\r\n                  <i class=\"el-icon-view\" />\r\n                </el-button>\r\n                <el-button type=\"text\" size=\"mini\" :title=\"task.status === 'running' ? '暂停任务' : '启动任务'\" @click.stop=\"toggleTaskStatus(index)\">\r\n                  <i :class=\"task.status === 'running' ? 'el-icon-video-pause' : 'el-icon-video-play'\" />\r\n                </el-button>\r\n                <el-button type=\"text\" size=\"mini\" title=\"编辑任务\" style=\"margin-right: 5px;\" @click.stop=\"editTask(index)\">\r\n                  <i class=\"el-icon-edit\" />\r\n                </el-button>\r\n                <el-button type=\"text\" size=\"mini\" title=\"删除任务\" style=\"color: #f56c6c;\" @click.stop=\"deleteTask(index)\">\r\n                  <i class=\"el-icon-delete\" />\r\n                </el-button>\r\n                <!-- 测试按钮 -->\r\n                <el-button type=\"text\" size=\"mini\" title=\"测试点击\" style=\"color: #409eff;\" @click.stop=\"testClick(index)\">\r\n                  <i class=\"el-icon-info\" />\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 任务预览对话框 -->\r\n      <el-dialog\r\n        title=\"任务详情\"\r\n        :visible.sync=\"taskPreviewDialog.visible\"\r\n        width=\"600px\"\r\n        append-to-body\r\n      >\r\n        <div v-if=\"taskPreviewDialog.taskData\" class=\"task-preview-content\">\r\n          <el-descriptions title=\"基本信息\" :column=\"2\" border>\r\n            <el-descriptions-item label=\"任务名称\">{{ taskPreviewDialog.taskData.name }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"任务状态\">\r\n              <el-tag :type=\"taskPreviewDialog.taskData.status === 'running' ? 'success' : 'info'\">\r\n                {{ taskPreviewDialog.taskData.status === 'running' ? '运行中' : '待运行' }}\r\n              </el-tag>\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"执行频率\">{{ getFrequencyText(taskPreviewDialog.taskData.frequency) }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"执行时间\">{{ taskPreviewDialog.taskData.executeTime }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"推送地址\" :span=\"2\">{{ taskPreviewDialog.taskData.pushUrl || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"任务描述\" :span=\"2\">{{ taskPreviewDialog.taskData.description || '无描述' }}</el-descriptions-item>\r\n          </el-descriptions>\r\n        </div>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button @click=\"taskPreviewDialog.visible = false\">关 闭</el-button>\r\n        </div>\r\n      </el-dialog>\r\n\r\n      <!-- 创建/编辑任务弹窗 -->\r\n      <el-dialog\r\n        :title=\"editingTaskIndex === -1 ? '创建定时任务' : '编辑定时任务'\"\r\n        :visible.sync=\"createTaskDialogVisible\"\r\n        width=\"500px\"\r\n        :before-close=\"closeCreateTaskDialog\"\r\n        :append-to-body=\"true\"\r\n        class=\"create-task-dialog\"\r\n      >\r\n        <div class=\"task-form\">\r\n          <!-- 任务需求 -->\r\n          <div class=\"task-requirement-section\">\r\n            <div class=\"section-label\">\r\n              任务需求\r\n              <span class=\"required\">*</span>\r\n            </div>\r\n            <div class=\"form-group\">\r\n              <div class=\"input-label\">关联任务</div>\r\n              <el-select\r\n                v-model=\"taskForm.requirement_id\"\r\n                placeholder=\"请选择要关联的舆情分析任务\"\r\n                class=\"task-name-input\"\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"task in requirementList\"\r\n                  :key=\"task.id\"\r\n                  :label=\"task.requirementName\"\r\n                  :value=\"task.id\"\r\n                >\r\n                  <span style=\"float: left\">{{ task.requirementName }}</span>\r\n                  <span style=\"float: right; color: #8492a6; font-size: 13px\">ID: {{ task.id }}</span>\r\n                </el-option>\r\n              </el-select>\r\n            </div>\r\n            <div class=\"form-group\">\r\n              <div class=\"input-label\">任务名称</div>\r\n              <el-input\r\n                v-model=\"taskForm.name\"\r\n                placeholder=\"请输入任务名称\"\r\n                class=\"task-name-input\"\r\n              />\r\n            </div>\r\n            <div class=\"form-group\">\r\n              <div class=\"input-label\">任务描述</div>\r\n              <el-input\r\n                v-model=\"taskForm.description\"\r\n                type=\"textarea\"\r\n                :rows=\"3\"\r\n                placeholder=\"请描述具体的任务需求，例如：基于当前分析需求自动生成AI新闻总结\"\r\n                class=\"task-description-input\"\r\n              />\r\n              <div class=\"form-group\">\r\n                <div class=\"input-label\">推送地址</div>\r\n                <el-input\r\n                  v-model=\"taskForm.push_url\"\r\n                  placeholder=\"例如：https://www.baidu.com\"\r\n                  class=\"task-name-input\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 执行时间 -->\r\n          <div class=\"execute-time-section\">\r\n            <div class=\"section-label\">执行时间</div>\r\n            <div class=\"time-selector\">\r\n              <el-select v-model=\"taskForm.frequency\" placeholder=\"选择频率\" class=\"frequency-select\">\r\n                <el-option label=\"仅一次\" value=\"once\" />\r\n                <el-option label=\"每天\" value=\"daily\" />\r\n                <el-option label=\"每周\" value=\"weekly\" />\r\n                <el-option label=\"每月\" value=\"monthly\" />\r\n              </el-select>\r\n              <!-- 一次性任务：选择具体日期时间 -->\r\n              <el-date-picker\r\n                v-if=\"taskForm.frequency === 'once'\"\r\n                v-model=\"taskForm.execute_date_time\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择执行日期和时间\"\r\n                format=\"yyyy-MM-dd HH:mm\"\r\n                value-format=\"yyyy-MM-dd HH:mm\"\r\n                class=\"datetime-picker\"\r\n                :picker-options=\"{\r\n                  disabledDate(time) {\r\n                    return time.getTime() < Date.now() - 8.64e7\r\n                  }\r\n                }\"\r\n              />\r\n              <!-- 周期性任务：选择时间 -->\r\n              <el-time-picker\r\n                v-else\r\n                v-model=\"taskForm.execute_time\"\r\n                format=\"HH:mm\"\r\n                value-format=\"HH:mm\"\r\n                placeholder=\"选择时间\"\r\n                class=\"time-picker\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 底部按钮 -->\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button class=\"modify-btn\" @click=\"modifyPlan\">修改计划</el-button>\r\n          <el-button type=\"primary\" class=\"run-btn\" @click=\"saveAndRunTask\">保存并运行</el-button>\r\n          <el-button type=\"success\" class=\"save-btn\" @click=\"saveTaskPlan\">保存计划</el-button>\r\n        </div>\r\n      </el-dialog>\r\n    </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  getAnalysisTaskList,\r\n  deleteAnalysisTask,\r\n  batchDeleteAnalysisTasks,\r\n  exportRequirements,\r\n  getAnalysisProgress,\r\n  pushReport,\r\n  createTimedTask,\r\n  getTimedTaskList,\r\n  getTimedTaskDetail,\r\n  updateTimedTask,\r\n  updateTaskStatus,\r\n  deleteTimedTask\r\n} from '@/api/opinion-analysis'\r\nimport { parseTime } from '@/utils/ruoyi'\r\n\r\nexport default {\r\n  name: 'AnalyzeRecord',\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 分析记录表格数据\r\n      recordList: [],\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        page_num: 1,\r\n        page_size: 10,\r\n        requirementName: null,\r\n        entityKeyword: null,\r\n        analysisStatus: null\r\n      },\r\n      // 分析日志对话框\r\n      logDialogVisible: false,\r\n      logLoading: false,\r\n      logList: [],\r\n      // 查看报告对话框\r\n      reportDialogVisible: false,\r\n      reportLoading: false,\r\n      reportData: null,\r\n      // 立即推送对话框\r\n      pushDialogVisible: false,\r\n      pushLoading: false,\r\n      pushForm: {\r\n        push_url: '',\r\n        push_content: ''\r\n      },\r\n      pushRules: {\r\n        push_url: [\r\n          { required: true, message: '推送地址不能为空', trigger: 'blur' }\r\n        ],\r\n        push_content: [\r\n          { required: true, message: '推送内容不能为空', trigger: 'blur' }\r\n        ]\r\n      },\r\n      // 定时推送对话框\r\n      schedulePushDialogVisible: false,\r\n      schedulePushLoading: false,\r\n      schedulePushForm: {\r\n        push_url: '',\r\n        push_time: '',\r\n        frequency: 'once',\r\n        push_content: ''\r\n      },\r\n      schedulePushRules: {\r\n        push_url: [\r\n          { required: true, message: '推送地址不能为空', trigger: 'blur' }\r\n        ],\r\n        push_time: [\r\n          { required: true, message: '推送时间不能为空', trigger: 'change' }\r\n        ],\r\n        push_content: [\r\n          { required: true, message: '推送内容不能为空', trigger: 'blur' }\r\n        ]\r\n      },\r\n      // 当前操作的记录\r\n      currentRecord: null,\r\n      // 定时任务相关数据\r\n      timedTaskDialogVisible: false, // 定时任务抽屉显示状态\r\n      timedTaskList: [], // 定时任务列表\r\n      createTaskDialogVisible: false, // 创建任务弹窗显示状态\r\n      editingTaskIndex: -1, // 当前编辑的任务索引，-1表示新建任务\r\n      taskForm: {\r\n        requirement_id: '', // 需求ID\r\n        name: '', // 任务名称\r\n        description: '',\r\n        execute_time: '16:00',\r\n        execute_date_time: '', // 一次性任务的执行日期时间\r\n        frequency: 'daily',\r\n        push_url: '' // 推送地址\r\n      },\r\n      requirementList: [], // 需求列表\r\n      // 任务预览弹窗状态\r\n      taskPreviewDialog: {\r\n        visible: false,\r\n        taskData: null,\r\n        loading: false\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    // 只重置日期范围，保留其他查询参数\r\n    this.dateRange = []\r\n    this.getList()\r\n    // 注释掉页面加载时的定时任务列表加载，改为点击定时推送按钮时才加载\r\n    // this.loadTimedTaskList()\r\n  },\r\n  methods: {\r\n    /** 查询分析记录列表 */\r\n    getList(paginationParams) {\r\n      this.loading = true\r\n\r\n      // 处理分页参数\r\n      if (paginationParams) {\r\n        this.queryParams.page_num = paginationParams.page\r\n        this.queryParams.page_size = paginationParams.limit\r\n      }\r\n\r\n      const params = {\r\n        page_num: this.queryParams.page_num,\r\n        page_size: this.queryParams.page_size\r\n      }\r\n\r\n      // 添加搜索条件（正确映射到任务表字段）\r\n      if (this.queryParams.requirementName && this.queryParams.requirementName.trim() !== '') {\r\n        params.task_name = this.queryParams.requirementName.trim() // 映射到任务名称\r\n      }\r\n      if (this.queryParams.entityKeyword && this.queryParams.entityKeyword.trim() !== '') {\r\n        params.task_description = this.queryParams.entityKeyword.trim() // 映射到任务描述（包含关键词）\r\n      }\r\n      if (this.queryParams.analysisStatus !== null && this.queryParams.analysisStatus !== undefined && this.queryParams.analysisStatus !== '') {\r\n        // 将前端状态映射回任务状态\r\n        const statusMap = { 0: 'pending', 1: 'running', 2: 'completed', 3: 'failed' }\r\n        params.status = statusMap[this.queryParams.analysisStatus] || 'pending'\r\n      }\r\n\r\n      // 添加时间范围\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.create_time_start = this.dateRange[0]\r\n        params.create_time_end = this.dateRange[1]\r\n        console.log('🔍 添加时间范围筛选:', this.dateRange)\r\n      }\r\n\r\n      // 调用真实API获取任务列表数据\r\n      console.log('🔍 发送API请求，参数:', params)\r\n      getAnalysisTaskList(params).then(response => {\r\n        console.log('🔍 API响应:', response)\r\n        // 处理响应数据，将数据库字段映射到前端显示字段\r\n\r\n        // 适配新的响应格式：使用 model_content 后，PageResponseModel 会直接合并到响应根级别\r\n        let requirements = []\r\n        if (response.records) {\r\n          // 新格式：model_content 使 PageResponseModel 直接合并到响应根级别\r\n          requirements = response.records\r\n        } else if (response.data && response.data.records) {\r\n          // 备用格式：PageResponseModel 在 data 字段中\r\n          requirements = response.data.records\r\n        } else if (response.data && response.data.rows) {\r\n          // 旧的分页数据格式\r\n          requirements = response.data.rows\r\n        } else if (response.data && Array.isArray(response.data)) {\r\n          // 直接数组格式\r\n          requirements = response.data\r\n        } else if (response.rows) {\r\n          // 兼容旧格式\r\n          requirements = response.rows\r\n        } else {\r\n          requirements = []\r\n        }\r\n        this.recordList = requirements\r\n          .filter(item => item.id != null && item.id !== undefined && item.id !== '') // 过滤掉无效id的数据\r\n          .map(item => {\r\n            // 确保ID是有效的整数\r\n            const id = parseInt(item.id)\r\n            if (isNaN(id)) {\r\n              return null\r\n            }\r\n\r\n            const mappedItem = {\r\n              id: id, // 确保id是整数\r\n              requirementName: item.task_name || item.taskName || '', // 任务名称\r\n              entityKeyword: item.task_description || item.taskDescription || '', // 任务描述作为关键词显示\r\n              specificRequirement: item.task_description || item.taskDescription || '', // 任务描述\r\n              createTime: item.create_time || item.createTime || '',\r\n              // 将任务状态映射到前端的status字段\r\n              // pending-待执行->0待生成, running-执行中->1生成中, completed-已完成->2已生成, failed-失败->-1生成失败\r\n              status: this.mapTaskStatus(item.status || 'pending'),\r\n              // 添加报告OSS URL字段\r\n              reportOssUrl: item.report_oss_url || item.reportOssUrl || null\r\n            }\r\n            return mappedItem\r\n          })\r\n          .filter(item => item !== null) // 过滤掉映射失败的项\r\n\r\n        // 适配新的总记录数格式：使用 model_content 后，total 会直接在响应根级别\r\n        if (response.total !== undefined) {\r\n          this.total = response.total\r\n        } else if (response.data && response.data.total !== undefined) {\r\n          this.total = response.data.total\r\n        } else {\r\n          this.total = 0\r\n        }\r\n\r\n        this.loading = false\r\n      }).catch(error => {\r\n        this.$modal.msgError('获取分析记录列表失败: ' + (error.message || '未知错误'))\r\n        this.loading = false\r\n      })\r\n    },\r\n\r\n    /** 映射任务状态 */\r\n    mapTaskStatus(taskStatus) {\r\n      // 数据库task status: pending-待执行，running-执行中，completed-已完成，failed-失败，paused-暂停\r\n      // 前端status: 0-待生成，1-生成中，2-已生成，-1-生成失败\r\n      const statusMap = {\r\n        'pending': 0, // 待执行 -> 待生成\r\n        'running': 1, // 执行中 -> 生成中\r\n        'completed': 2, // 已完成 -> 已生成\r\n        'failed': -1, // 失败 -> 生成失败\r\n        'paused': 0 // 暂停 -> 待生成\r\n      }\r\n      return statusMap[taskStatus] !== undefined ? statusMap[taskStatus] : 0\r\n    },\r\n\r\n    /** 映射分析状态（保留兼容性） */\r\n    mapAnalysisStatus(analysisStatus) {\r\n      // 数据库analysis_status: 0-未开始，1-分析中，2-已完成，3-失败\r\n      // 前端status: 0-待生成，1-生成中，2-已生成，-1-生成失败\r\n      const statusMap = {\r\n        0: 0, // 未开始 -> 待生成\r\n        1: 1, // 分析中 -> 生成中\r\n        2: 2, // 已完成 -> 已生成\r\n        3: -1 // 失败 -> 生成失败\r\n      }\r\n      return statusMap[analysisStatus] !== undefined ? statusMap[analysisStatus] : 0\r\n    },\r\n\r\n    /** 需求名称输入监听 */\r\n    onRequirementNameInput(value) {\r\n      console.log('🔍 需求名称输入变化:', value)\r\n      // 可以添加防抖逻辑，避免频繁查询\r\n      // this.debounceSearch()\r\n    },\r\n\r\n    /** 关键词输入监听 */\r\n    onEntityKeywordInput(value) {\r\n      console.log('🔍 关键词输入变化:', value)\r\n      // 可以添加防抖逻辑，避免频繁查询\r\n      // this.debounceSearch()\r\n    },\r\n\r\n    /** 分析状态变化监听 */\r\n    onAnalysisStatusChange(value) {\r\n      console.log('🔍 分析状态变化:', value)\r\n      // 状态变化时立即搜索\r\n      this.handleQuery()\r\n    },\r\n\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      // 重置到第一页\r\n      this.queryParams.page_num = 1\r\n      // 添加搜索日志\r\n      console.log('🔍 执行搜索，查询参数:', this.queryParams)\r\n      this.getList()\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = []\r\n      // 手动重置查询参数\r\n      this.queryParams = {\r\n        page_num: 1,\r\n        page_size: 10,\r\n        requirementName: null,\r\n        entityKeyword: null,\r\n        analysisStatus: null\r\n      }\r\n      // 重置表单\r\n      this.resetForm('queryForm')\r\n      // 添加重置日志\r\n      console.log('🔄 重置搜索条件，查询参数:', this.queryParams)\r\n      this.handleQuery()\r\n    },\r\n\r\n    /** 多选框选中数据 */\r\n    handleSelectionChange(selection) {\r\n      // 过滤掉id为null或undefined的项，并确保id是整数\r\n      this.ids = selection\r\n        .filter(item => item.id != null && item.id !== undefined)\r\n        .map(item => parseInt(item.id))\r\n\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n\r\n    /** 查看分析日志 */\r\n    handleViewLogs(row) {\r\n      this.currentRecord = row\r\n      this.logDialogVisible = true\r\n      this.logLoading = true\r\n\r\n      // 获取分析进度日志\r\n      getAnalysisProgress(row.taskId || row.id).then(response => {\r\n        this.logList = response.data || []\r\n        this.logLoading = false\r\n      }).catch(() => {\r\n        this.logLoading = false\r\n      })\r\n    },\r\n\r\n    /** 查看报告 */\r\n    handleViewReport(row) {\r\n      this.currentRecord = row\r\n      this.reportDialogVisible = true\r\n      this.reportLoading = true\r\n\r\n      // 模拟获取报告数据\r\n      setTimeout(() => {\r\n        this.reportData = {\r\n          requirementName: row.requirementName,\r\n          entityKeyword: row.entityKeyword,\r\n          specificRequirement: row.specificRequirement,\r\n          totalArticles: Math.floor(Math.random() * 1000) + 100,\r\n          sentiment: {\r\n            positive: Math.floor(Math.random() * 50) + 20,\r\n            neutral: Math.floor(Math.random() * 30) + 10,\r\n            negative: Math.floor(Math.random() * 20) + 5\r\n          }\r\n        }\r\n        this.reportLoading = false\r\n      }, 1000)\r\n    },\r\n\r\n    /** 报告 */\r\n    handleReport(row) {\r\n      // 检查是否有报告OSS URL\r\n      if (row.reportOssUrl) {\r\n        // 如果有OSS URL，直接在新窗口打开\r\n        window.open(row.reportOssUrl, '_blank')\r\n      } else {\r\n        // 如果没有OSS URL，显示暂无报告提示\r\n        this.$modal.msg('暂无报告')\r\n      }\r\n    },\r\n\r\n    /** 立即推送 */\r\n    handlePushNow(row) {\r\n      // 检查是否有报告OSS URL - 参照舆情分析页面的OSS检查逻辑\r\n      if (!row.reportOssUrl) {\r\n        this.$modal.msgWarning('报告尚未生成或上传到OSS，请等待报告生成完成后再推送')\r\n        return\r\n      }\r\n\r\n      // 检查任务状态是否为已完成\r\n      if (row.status !== 2) {\r\n        this.$modal.msgWarning('任务尚未完成，请等待分析完成后再推送')\r\n        return\r\n      }\r\n\r\n      this.currentRecord = row\r\n      this.pushForm = {\r\n        push_url: '',\r\n        push_content: `分析报告：${row.requirementName} - 立即推送`\r\n      }\r\n      this.pushDialogVisible = true\r\n    },\r\n\r\n    /** 定时推送 */\r\n    handleSchedulePush(row) {\r\n      this.currentRecord = row\r\n      this.schedulePushForm = {\r\n        push_url: '',\r\n        push_time: '',\r\n        frequency: 'once',\r\n        push_content: `分析报告：${row.requirementName}`\r\n      }\r\n      this.schedulePushDialogVisible = true\r\n    },\r\n\r\n    /** 提交立即推送 */\r\n    submitPush() {\r\n      this.$refs['pushForm'].validate(valid => {\r\n        if (valid) {\r\n          // 参照舆情分析页面的OSS检查逻辑 - 验证推送前的必要条件\r\n          if (!this.currentRecord.reportOssUrl) {\r\n            this.$modal.msgError('报告尚未上传到OSS，无法推送。请等待报告生成完成后再试。')\r\n            return\r\n          }\r\n\r\n          // 检查任务状态\r\n          if (this.currentRecord.status !== 2) {\r\n            this.$modal.msgError('任务尚未完成，无法推送。请等待分析完成后再试。')\r\n            return\r\n          }\r\n\r\n          // 验证推送URL格式（参照源文件的URL验证逻辑）\r\n          if (!this.validatePushUrl(this.pushForm.push_url)) {\r\n            return\r\n          }\r\n\r\n          this.pushLoading = true\r\n\r\n          // 构建推送数据 - 参照舆情分析页面的数据结构\r\n          const pushData = {\r\n            target_url: this.pushForm.push_url,\r\n            report_data: {\r\n              requirementName: this.currentRecord.requirementName,\r\n              entityKeyword: this.currentRecord.entityKeyword,\r\n              specificRequirement: this.currentRecord.specificRequirement,\r\n              reportOssUrl: this.currentRecord.reportOssUrl, // 确保有OSS URL\r\n              totalArticles: 0, // 后端会从实际数据中获取\r\n              sentiment: {}, // 后端会从实际数据中获取\r\n              dataSources: 0, // 后端会从实际数据中获取\r\n              taskId: this.currentRecord.id,\r\n              createTime: this.currentRecord.createTime,\r\n              status: this.currentRecord.status,\r\n              pushTime: new Date().toISOString() // 添加推送时间戳\r\n            },\r\n            analysis_results: {\r\n              summary: this.pushForm.push_content,\r\n              reportUrl: this.currentRecord.reportOssUrl // 确保有报告URL\r\n            },\r\n            requirement_id: this.currentRecord.id,\r\n            push_type: 'immediate'\r\n          }\r\n\r\n          pushReport(pushData).then(response => {\r\n            // 检查响应并显示详细信息\r\n            if (response && response.success) {\r\n              this.$modal.msgSuccess('推送成功')\r\n\r\n              // 如果后端生成了新的报告URL，可以更新本地记录\r\n              if (response.data && response.data.report_url && !this.currentRecord.reportOssUrl) {\r\n                this.currentRecord.reportOssUrl = response.data.report_url\r\n                console.log('更新本地报告URL:', response.data.report_url)\r\n              }\r\n            } else {\r\n              this.$modal.msgSuccess('推送完成')\r\n            }\r\n\r\n            this.pushDialogVisible = false\r\n            this.pushLoading = false\r\n          }).catch(error => {\r\n            console.error('推送失败:', error)\r\n            if (error.response && error.response.data && error.response.data.msg) {\r\n              this.$modal.msgError('推送失败: ' + error.response.data.msg)\r\n            } else {\r\n              this.$modal.msgError('推送失败，请重试')\r\n            }\r\n            this.pushLoading = false\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    /** 提交定时推送 */\r\n    submitSchedulePush() {\r\n      this.$refs['schedulePushForm'].validate(valid => {\r\n        if (valid) {\r\n          this.schedulePushLoading = true\r\n          const taskData = {\r\n            requirement_id: this.currentRecord.id,\r\n            push_url: this.schedulePushForm.push_url,\r\n            push_time: this.schedulePushForm.push_time,\r\n            frequency: this.schedulePushForm.frequency,\r\n            push_content: this.schedulePushForm.push_content\r\n          }\r\n\r\n          createTimedTask(taskData).then(response => {\r\n            this.$modal.msgSuccess('定时推送任务创建成功')\r\n            this.schedulePushDialogVisible = false\r\n            this.schedulePushLoading = false\r\n          }).catch(() => {\r\n            this.schedulePushLoading = false\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      let ids, names\r\n\r\n      // 判断是否为批量删除（无参数或参数为事件对象）\r\n      if (!row || row instanceof Event) {\r\n        // 批量删除\r\n        if (!this.ids || this.ids.length === 0) {\r\n          this.$modal.msgError('请先选择要删除的记录')\r\n          return\r\n        }\r\n        // 过滤并验证IDs\r\n        ids = this.ids.filter(id => id != null && !isNaN(id)).map(id => parseInt(id))\r\n        if (ids.length === 0) {\r\n          this.$modal.msgError('选中的记录ID无效，无法删除')\r\n          return\r\n        }\r\n        names = this.recordList.filter(item => ids.includes(item.id)).map(item => item.requirementName).join('、')\r\n      } else {\r\n        // 单个删除\r\n        if (!row.id || row.id == null) {\r\n          this.$modal.msgError('记录ID无效，无法删除')\r\n          return\r\n        }\r\n        ids = [parseInt(row.id)]\r\n        names = row.requirementName || '未知记录'\r\n      }\r\n      this.$modal.confirm(`是否确认删除分析记录\"${names}\"？`).then(() => {\r\n        // 根据IDs数量判断是单个删除还是批量删除\r\n        if (ids.length === 1) {\r\n          // 单个删除\r\n          return deleteAnalysisTask(ids[0])\r\n        } else {\r\n          // 批量删除\r\n          return batchDeleteAnalysisTasks(ids)\r\n        }\r\n      }).then(response => {\r\n        console.log('🗑️ 删除操作成功，响应:', response)\r\n\r\n        // 检查是否需要重置分页（物理删除后需要重新计算分页）\r\n        const currentPageRecords = this.recordList.length\r\n        const deletedCount = ids.length\r\n\r\n        // 如果删除后当前页可能没有记录了，且不是第一页，则回到上一页\r\n        if (currentPageRecords <= deletedCount && this.queryParams.page_num > 1) {\r\n          this.queryParams.page_num = this.queryParams.page_num - 1\r\n          console.log('🔄 删除后回到上一页:', this.queryParams.page_num)\r\n        }\r\n\r\n        // 重置选中状态\r\n        this.ids = []\r\n        this.single = true\r\n        this.multiple = true\r\n\r\n        // 物理删除成功后，直接重新获取数据以确保数据一致性\r\n        this.getList()\r\n\r\n        this.$modal.msgSuccess('删除成功')\r\n      }).catch(error => {\r\n        if (error.response && error.response.data) {\r\n          if (error.response.data.msg) {\r\n            this.$modal.msgError(error.response.data.msg)\r\n          } else if (error.response.data.message) {\r\n            this.$modal.msgError(error.response.data.message)\r\n          } else if (error.response.data.detail) {\r\n            // 处理FastAPI的验证错误\r\n            if (Array.isArray(error.response.data.detail)) {\r\n              const details = error.response.data.detail.map(d => d.msg || d.message || JSON.stringify(d)).join(', ')\r\n              this.$modal.msgError(`删除失败: ${details}`)\r\n            } else {\r\n              this.$modal.msgError(`删除失败: ${error.response.data.detail}`)\r\n            }\r\n          } else {\r\n            this.$modal.msgError(`删除失败: ${JSON.stringify(error.response.data)}`)\r\n          }\r\n        } else if (error.message) {\r\n          this.$modal.msgError(`删除失败: ${error.message}`)\r\n        } else {\r\n          this.$modal.msgError('删除失败，请重试')\r\n        }\r\n      })\r\n    },\r\n\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.$modal.confirm('是否确认导出所有分析记录数据项？').then(() => {\r\n        const params = {}\r\n\r\n        // 添加搜索条件（使用后端期望的下划线命名）\r\n        if (this.queryParams.requirementName) {\r\n          params.requirement_name = this.queryParams.requirementName\r\n        }\r\n        if (this.queryParams.entityKeyword) {\r\n          params.entity_keyword = this.queryParams.entityKeyword\r\n        }\r\n        if (this.queryParams.analysisStatus !== null && this.queryParams.analysisStatus !== undefined && this.queryParams.analysisStatus !== '') {\r\n          params.analysis_status = this.queryParams.analysisStatus\r\n        }\r\n\r\n        // 添加时间范围\r\n        if (this.dateRange && this.dateRange.length === 2) {\r\n          params.create_time_start = this.dateRange[0]\r\n          params.create_time_end = this.dateRange[1]\r\n        }\r\n\r\n        return exportRequirements(params)\r\n      }).then(response => {\r\n        // 处理文件下载\r\n        const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })\r\n        const url = window.URL.createObjectURL(blob)\r\n        const link = document.createElement('a')\r\n        link.href = url\r\n        link.download = `分析记录_${new Date().toISOString().slice(0, 10)}.xlsx`\r\n        document.body.appendChild(link)\r\n        link.click()\r\n        document.body.removeChild(link)\r\n        window.URL.revokeObjectURL(url)\r\n\r\n        this.$modal.msgSuccess('导出成功')\r\n      }).catch(error => {\r\n        if (error.response && error.response.data && error.response.data.msg) {\r\n          this.$modal.msgError(error.response.data.msg)\r\n        } else {\r\n          this.$modal.msgError('导出失败，请重试')\r\n        }\r\n      })\r\n    },\r\n\r\n    /** 导出报告 */\r\n    handleExportReport() {\r\n      this.$modal.msgSuccess('报告导出成功')\r\n    },\r\n\r\n    /** 验证推送URL格式（参照舆情分析页面的验证逻辑） */\r\n    validatePushUrl(url) {\r\n      if (!url || !url.trim()) {\r\n        this.$modal.msgError('请输入推送目标URL地址')\r\n        return false\r\n      }\r\n\r\n      let fullUrl = url.trim()\r\n\r\n      // 如果没有协议前缀，自动添加https://\r\n      if (!fullUrl.includes('://')) {\r\n        fullUrl = 'https://' + fullUrl\r\n      }\r\n\r\n      // 基本URL格式验证\r\n      try {\r\n        new URL(fullUrl)\r\n        console.log('URL验证通过:', fullUrl)\r\n        return fullUrl\r\n      } catch (error) {\r\n        // 宽松验证：如果包含://且长度合理，则认为可能是有效URL\r\n        if (fullUrl.includes('://') && fullUrl.length > 10) {\r\n          console.log('使用宽松验证通过URL:', fullUrl)\r\n          this.$modal.msgWarning('URL格式可能不正确，但仍将尝试推送。如果推送失败，请检查URL格式')\r\n          return fullUrl\r\n        }\r\n\r\n        this.$modal.msgError('URL格式不正确，请检查后重试')\r\n        return false\r\n      }\r\n    },\r\n\r\n    /** 获取状态标签类型 */\r\n    getStatusTagType(status) {\r\n      const statusMap = {\r\n        0: 'info', // 待生成 - 灰色\r\n        1: 'warning', // 生成中 - 橙色\r\n        2: 'success', // 已生成 - 绿色\r\n        '-1': 'danger' // 生成失败 - 红色\r\n      }\r\n      return statusMap[status] || 'info'\r\n    },\r\n\r\n    /** 获取状态文本 */\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        0: '待生成',\r\n        1: '生成中',\r\n        2: '已生成',\r\n        '-1': '生成失败'\r\n      }\r\n      return statusMap[status] || '未知'\r\n    },\r\n\r\n    /** 获取日志级别标签类型 */\r\n    getLogLevelTagType(level) {\r\n      const levelMap = {\r\n        'info': 'info',\r\n        'success': 'success',\r\n        'warning': 'warning',\r\n        'error': 'danger'\r\n      }\r\n      return levelMap[level] || 'info'\r\n    },\r\n\r\n    // ==================== 定时任务相关方法 ====================\r\n\r\n    // 处理定时推送按钮点击\r\n    handleTimedPush() {\r\n      this.timedTaskDialogVisible = true\r\n      // 打开定时推送弹窗时加载定时任务列表\r\n      this.loadTimedTaskList()\r\n    },\r\n\r\n    // 关闭定时任务弹窗\r\n    closeTimedTaskDialog() {\r\n      this.timedTaskDialogVisible = false\r\n    },\r\n\r\n    // 处理创建定时任务\r\n    handleCreateTimedTask() {\r\n      this.resetTaskForm()\r\n      this.editingTaskIndex = -1 // 设置为新建模式\r\n      this.loadOpinionTaskList() // 加载舆情任务列表\r\n      this.createTaskDialogVisible = true\r\n    },\r\n\r\n    // 处理添加定时任务按钮\r\n    handleAddTimedTask() {\r\n      this.resetTaskForm()\r\n      this.editingTaskIndex = -1 // 设置为新建模式\r\n      this.loadOpinionTaskList() // 加载舆情任务列表\r\n      this.createTaskDialogVisible = true\r\n    },\r\n\r\n    // 加载舆情任务列表（用于定时推送绑定）\r\n    async loadOpinionTaskList() {\r\n      try {\r\n        const response = await getAnalysisTaskList({\r\n          page_num: 1,\r\n          page_size: 100\r\n        })\r\n\r\n        if (response.success) {\r\n          // 处理分页数据，从 opinion_task 表获取数据\r\n          let tasks = []\r\n\r\n          if (response.data && response.data.rows) {\r\n            tasks = response.data.rows\r\n          } else if (Array.isArray(response.data)) {\r\n            tasks = response.data\r\n          } else if (response.rows) {\r\n            tasks = response.rows\r\n          }\r\n\r\n          // 映射任务数据到需求列表格式\r\n          this.requirementList = tasks.map(task => ({\r\n            id: task.id,\r\n            requirementName: task.requirementName || task.requirement_name || `任务${task.id}`,\r\n            taskId: task.id,\r\n            keywords: task.keywords || task.entity_keyword,\r\n            specificRequirement: task.specificRequirement || task.specific_requirement,\r\n            createTime: task.createTime || task.create_time\r\n          }))\r\n        } else {\r\n          this.$message.error(response.msg || '获取舆情任务列表失败')\r\n          this.requirementList = []\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('加载舆情任务列表失败')\r\n        this.requirementList = []\r\n      }\r\n    },\r\n\r\n    // 加载定时任务列表\r\n    async loadTimedTaskList() {\r\n      try {\r\n        const response = await getTimedTaskList()\r\n\r\n        if (response.success) {\r\n          // 处理分页数据 - 后端返回的是PageResponseModel格式\r\n\r\n          if (response.data && response.data.rows) {\r\n            this.timedTaskList = response.data.rows.map(task => ({\r\n              id: task.id,\r\n              requirementId: task.requirementId,\r\n              name: task.taskName,\r\n              description: task.taskDescription,\r\n              executeTime: task.executeTime,\r\n              executeDateTime: task.frequency === 'once' && task.execute_date ? `${task.execute_date} ${task.executeTime}` : '',\r\n              frequency: task.frequency,\r\n              status: task.status === 'running' ? 'running' : 'pending',\r\n              pushUrl: task.pushUrl\r\n            }))\r\n          } else if (response.data && response.data.records && Array.isArray(response.data.records)) {\r\n            this.timedTaskList = response.data.records.map(task => ({\r\n              id: task.id,\r\n              requirementId: task.requirementId,\r\n              name: task.taskName,\r\n              description: task.taskDescription,\r\n              executeTime: task.executeTime,\r\n              executeDateTime: task.frequency === 'once' && task.execute_date ? `${task.execute_date} ${task.executeTime}` : '',\r\n              frequency: task.frequency,\r\n              status: task.status === 'running' ? 'running' : 'pending',\r\n              pushUrl: task.pushUrl\r\n            }))\r\n          } else if (Array.isArray(response.data)) {\r\n            this.timedTaskList = response.data.map(task => ({\r\n              id: task.id,\r\n              requirementId: task.requirementId,\r\n              name: task.taskName,\r\n              description: task.taskDescription,\r\n              executeTime: task.executeTime,\r\n              executeDateTime: task.frequency === 'once' && task.execute_date ? `${task.execute_date} ${task.executeTime}` : '',\r\n              frequency: task.frequency,\r\n              status: task.status === 'running' ? 'running' : 'pending',\r\n              pushUrl: task.pushUrl\r\n            }))\r\n          } else if (response.rows && Array.isArray(response.rows)) {\r\n            this.timedTaskList = response.rows.map(task => ({\r\n              id: task.id,\r\n              requirementId: task.requirementId,\r\n              name: task.taskName,\r\n              description: task.taskDescription,\r\n              executeTime: task.executeTime,\r\n              executeDateTime: task.frequency === 'once' && task.execute_date ? `${task.execute_date} ${task.executeTime}` : '',\r\n              frequency: task.frequency,\r\n              status: task.status === 'running' ? 'running' : 'pending',\r\n              pushUrl: task.pushUrl\r\n            }))\r\n          } else if (response.records && Array.isArray(response.records)) {\r\n            this.timedTaskList = response.records.map(task => ({\r\n              id: task.id,\r\n              requirementId: task.requirementId,\r\n              name: task.taskName,\r\n              description: task.taskDescription,\r\n              executeTime: task.executeTime,\r\n              executeDateTime: task.frequency === 'once' && task.execute_date ? `${task.execute_date} ${task.executeTime}` : '',\r\n              frequency: task.frequency,\r\n              status: task.status === 'running' ? 'running' : 'pending',\r\n              pushUrl: task.pushUrl\r\n            }))\r\n          } else {\r\n            this.timedTaskList = []\r\n          }\r\n        } else {\r\n          this.$message.error(response.msg || '获取定时任务列表失败')\r\n          this.timedTaskList = []\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('加载定时任务列表失败')\r\n        this.timedTaskList = []\r\n      }\r\n    },\r\n\r\n    // 关闭创建任务弹窗\r\n    closeCreateTaskDialog() {\r\n      this.createTaskDialogVisible = false\r\n      this.resetTaskForm()\r\n      this.editingTaskIndex = -1 // 重置编辑状态\r\n    },\r\n\r\n    // 重置任务表单\r\n    resetTaskForm() {\r\n      this.taskForm = {\r\n        requirement_id: '',\r\n        name: '',\r\n        description: '',\r\n        execute_time: '16:00',\r\n        execute_date_time: '',\r\n        frequency: 'daily',\r\n        push_url: ''\r\n      }\r\n    },\r\n\r\n    // 保存并运行任务\r\n    async saveAndRunTask() {\r\n      if (!this.validateTaskForm()) {\r\n        return\r\n      }\r\n\r\n      try {\r\n        const taskData = this.buildTaskData()\r\n\r\n        if (this.editingTaskIndex === -1) {\r\n          // 新建任务\r\n          const response = await createTimedTask(taskData)\r\n          if (response.success) {\r\n            this.$message.success('任务已保存并开始运行')\r\n            // 重新加载任务列表\r\n            await this.loadTimedTaskList()\r\n            // 更新任务状态为运行中\r\n            if (response.data && response.data.id) {\r\n              await updateTaskStatus(response.data.id, 'running')\r\n              await this.loadTimedTaskList()\r\n            }\r\n          } else {\r\n            this.$message.error('创建任务失败：' + response.msg)\r\n            return\r\n          }\r\n        } else {\r\n          // 编辑任务\r\n          const task = this.timedTaskList[this.editingTaskIndex]\r\n          const response = await updateTimedTask(task.id, taskData)\r\n\r\n          if (response.success) {\r\n            // 更新任务状态为运行中\r\n            await updateTaskStatus(task.id, 'running')\r\n            this.$message.success('任务已更新并开始运行')\r\n            // 重新加载任务列表\r\n            await this.loadTimedTaskList()\r\n          } else {\r\n            this.$message.error('更新任务失败：' + response.msg)\r\n            return\r\n          }\r\n        }\r\n\r\n        this.createTaskDialogVisible = false\r\n        this.resetTaskForm()\r\n        this.editingTaskIndex = -1 // 重置编辑状态\r\n      } catch (error) {\r\n        this.$message.error('保存任务失败，请重试')\r\n      }\r\n    },\r\n\r\n    // 保存任务计划\r\n    async saveTaskPlan() {\r\n      if (!this.validateTaskForm()) {\r\n        return\r\n      }\r\n\r\n      try {\r\n        const taskData = this.buildTaskData()\r\n\r\n        if (this.editingTaskIndex === -1) {\r\n          // 新建任务\r\n          const response = await createTimedTask(taskData)\r\n          if (response.success) {\r\n            this.$message.success('任务计划已保存')\r\n            // 重新加载任务列表\r\n            await this.loadTimedTaskList()\r\n          } else {\r\n            this.$message.error('保存任务计划失败：' + response.msg)\r\n            return\r\n          }\r\n        } else {\r\n          // 编辑任务\r\n          const task = this.timedTaskList[this.editingTaskIndex]\r\n          const response = await updateTimedTask(task.id, taskData)\r\n\r\n          if (response.success) {\r\n            this.$message.success('任务计划已更新')\r\n            // 重新加载任务列表\r\n            await this.loadTimedTaskList()\r\n          } else {\r\n            this.$message.error('更新任务计划失败：' + response.msg)\r\n            return\r\n          }\r\n        }\r\n\r\n        this.createTaskDialogVisible = false\r\n        this.resetTaskForm()\r\n        this.editingTaskIndex = -1 // 重置编辑状态\r\n      } catch (error) {\r\n        this.$message.error('保存任务计划失败，请重试')\r\n      }\r\n    },\r\n\r\n    // 验证任务表单\r\n    validateTaskForm() {\r\n      if (!this.taskForm.requirement_id) {\r\n        this.$message.warning('请选择要关联的舆情分析任务')\r\n        return false\r\n      }\r\n\r\n      if (!this.taskForm.name || !this.taskForm.name.trim()) {\r\n        this.$message.warning('请输入任务名称')\r\n        return false\r\n      }\r\n\r\n      if (!this.taskForm.description || !this.taskForm.description.trim()) {\r\n        this.$message.warning('请输入任务描述')\r\n        return false\r\n      }\r\n\r\n      // 验证执行时间\r\n      if (this.taskForm.frequency === 'once') {\r\n        if (!this.taskForm.execute_date_time) {\r\n          this.$message.warning('请选择执行日期和时间')\r\n          return false\r\n        }\r\n        // 检查是否是未来时间\r\n        const executeTime = new Date(this.taskForm.execute_date_time)\r\n        const now = new Date()\r\n        if (executeTime <= now) {\r\n          this.$message.warning('执行时间必须是未来时间')\r\n          return false\r\n        }\r\n      } else {\r\n        if (!this.taskForm.execute_time) {\r\n          this.$message.warning('请选择执行时间')\r\n          return false\r\n        }\r\n      }\r\n\r\n      return true\r\n    },\r\n\r\n    // 构建任务数据\r\n    buildTaskData() {\r\n      // 处理执行时间：对于一次性任务，只提取时间部分；对于周期性任务，直接使用时间\r\n      let executeTime\r\n      if (this.taskForm.frequency === 'once') {\r\n        // 一次性任务：从完整日期时间中提取时间部分 (HH:MM)\r\n        if (this.taskForm.execute_date_time) {\r\n          const dateTime = new Date(this.taskForm.execute_date_time)\r\n          executeTime = `${dateTime.getHours().toString().padStart(2, '0')}:${dateTime.getMinutes().toString().padStart(2, '0')}`\r\n        } else {\r\n          executeTime = '09:00' // 默认时间\r\n        }\r\n      } else {\r\n        // 周期性任务：直接使用时间\r\n        executeTime = this.taskForm.execute_time || '09:00'\r\n      }\r\n\r\n      const taskData = {\r\n        requirement_id: this.taskForm.requirement_id,\r\n        task_name: this.taskForm.name,\r\n        task_type: 'scheduled',\r\n        frequency: this.taskForm.frequency, // 使用 frequency 而不是 schedule_type\r\n        execute_time: executeTime, // 只存储时间部分 (HH:MM)\r\n        task_description: this.taskForm.description,\r\n        push_url: this.taskForm.push_url || ''\r\n      }\r\n\r\n      // 对于一次性任务，可能需要额外的日期信息\r\n      if (this.taskForm.frequency === 'once' && this.taskForm.execute_date_time) {\r\n        taskData['execute_date'] = this.taskForm.execute_date_time.split(' ')[0] // 提取日期部分 (YYYY-MM-DD)\r\n      }\r\n\r\n      return taskData\r\n    },\r\n\r\n    // 删除任务\r\n    async deleteTask(index) {\r\n      try {\r\n        const task = this.timedTaskList[index]\r\n\r\n        if (!task) {\r\n          this.$message.error('任务数据不存在')\r\n          return\r\n        }\r\n\r\n        await this.$confirm(`确定要删除任务「${task.name}」吗？`, '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        })\r\n\r\n        const response = await deleteTimedTask(task.id)\r\n\r\n        if (response.success) {\r\n          this.$message.success('删除成功')\r\n          // 重新加载任务列表\r\n          await this.loadTimedTaskList()\r\n        } else {\r\n          this.$message.error('删除失败：' + (response.msg || response.message || '未知错误'))\r\n        }\r\n      } catch (error) {\r\n        if (error === 'cancel') {\r\n          return\r\n        }\r\n        this.$message.error('删除任务失败，请重试')\r\n      }\r\n    },\r\n\r\n    // 编辑任务\r\n    editTask(index) {\r\n      const task = this.timedTaskList[index]\r\n\r\n      if (!task) {\r\n        this.$message.error('任务数据不存在')\r\n        return\r\n      }\r\n\r\n      this.editingTaskIndex = index\r\n      this.taskForm = {\r\n        requirement_id: task.requirementId || '',\r\n        name: task.name || '',\r\n        description: task.description || '',\r\n        execute_time: task.executeTime || '16:00',\r\n        execute_date_time: task.executeDateTime || '',\r\n        frequency: task.frequency || 'daily',\r\n        push_url: task.pushUrl || ''\r\n      }\r\n\r\n      this.loadOpinionTaskList() // 加载舆情任务列表\r\n      this.createTaskDialogVisible = true\r\n    },\r\n\r\n    // 切换任务状态\r\n    async toggleTaskStatus(index) {\r\n      try {\r\n        const task = this.timedTaskList[index]\r\n        const newStatus = task.status === 'running' ? 'pending' : 'running'\r\n\r\n        const response = await updateTaskStatus(task.id, newStatus)\r\n\r\n        if (response.success) {\r\n          if (newStatus === 'running') {\r\n            this.$message.success(`任务「${task.name}」已启动`)\r\n          } else {\r\n            this.$message.info(`任务「${task.name}」已暂停`)\r\n          }\r\n          // 重新加载任务列表\r\n          await this.loadTimedTaskList()\r\n        } else {\r\n          this.$message.error('状态更新失败：' + response.msg)\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('状态更新失败，请重试')\r\n      }\r\n    },\r\n\r\n    // 预览任务\r\n    previewTask(index) {\r\n      const task = this.timedTaskList[index]\r\n      if (!task) {\r\n        this.$message.error('任务数据不存在')\r\n        return\r\n      }\r\n      this.taskPreviewDialog.taskData = task\r\n      this.taskPreviewDialog.visible = true\r\n    },\r\n\r\n    // 获取任务计划文本\r\n    getTaskScheduleText(task) {\r\n      if (!task) return ''\r\n\r\n      if (task.frequency === 'once') {\r\n        // 一次性任务显示具体执行时间\r\n        return `仅一次 ${task.executeDateTime || task.executeTime}`\r\n      } else {\r\n        // 周期性任务显示频率和时间\r\n        const frequencyText = this.getFrequencyText(task.frequency)\r\n        return `${frequencyText} ${task.executeTime}`\r\n      }\r\n    },\r\n\r\n    // 获取频率文本\r\n    getFrequencyText(frequency) {\r\n      const frequencyMap = {\r\n        'once': '仅一次',\r\n        'daily': '每天',\r\n        'weekly': '每周',\r\n        'monthly': '每月'\r\n      }\r\n      return frequencyMap[frequency] || frequency\r\n    },\r\n\r\n    // 修改计划\r\n    modifyPlan() {\r\n      // 这里可以添加修改计划的逻辑\r\n      this.$message.info('修改计划功能待实现')\r\n    },\r\n\r\n    /** 时间格式化 */\r\n    parseTime\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n.mb8 {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.report-content {\r\n  min-height: 400px;\r\n\r\n  .analysis-results {\r\n    margin-top: 20px;\r\n\r\n    .statistic-value {\r\n      font-size: 24px;\r\n      font-weight: bold;\r\n      text-align: center;\r\n      padding: 20px 0;\r\n\r\n      &.positive {\r\n        color: #67c23a;\r\n      }\r\n\r\n      &.negative {\r\n        color: #f56c6c;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.el-table .cell {\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.small-padding .cell {\r\n  padding-left: 5px;\r\n  padding-right: 5px;\r\n}\r\n\r\n.fixed-width .el-button--mini {\r\n  margin-right: 5px;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n\r\n// 状态标签样式\r\n.el-tag {\r\n  &.el-tag--info {\r\n    background-color: #f4f4f5;\r\n    border-color: #e9e9eb;\r\n    color: #909399;\r\n  }\r\n\r\n  &.el-tag--warning {\r\n    background-color: #fdf6ec;\r\n    border-color: #f5dab1;\r\n    color: #e6a23c;\r\n  }\r\n\r\n  &.el-tag--success {\r\n    background-color: #f0f9ff;\r\n    border-color: #c6f7d0;\r\n    color: #67c23a;\r\n  }\r\n\r\n  &.el-tag--danger {\r\n    background-color: #fef0f0;\r\n    border-color: #fbc4c4;\r\n    color: #f56c6c;\r\n  }\r\n}\r\n\r\n// 操作按钮样式\r\n.el-button--text {\r\n  padding: 0;\r\n  margin-right: 10px;\r\n\r\n  &:last-child {\r\n    margin-right: 0;\r\n  }\r\n}\r\n\r\n// 搜索表单样式\r\n.el-form--inline .el-form-item {\r\n  margin-right: 10px;\r\n}\r\n\r\n// 定时任务抽屉样式\r\n.timed-task-drawer {\r\n  .drawer-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    width: 100%;\r\n\r\n    .drawer-title {\r\n      font-size: 16px;\r\n      font-weight: 500;\r\n    }\r\n\r\n    .add-task-btn {\r\n      margin-left: auto;\r\n    }\r\n  }\r\n\r\n  .drawer-content {\r\n    padding: 20px;\r\n    height: calc(100vh - 120px);\r\n    overflow-y: auto;\r\n\r\n    .empty-state {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      justify-content: center;\r\n      height: 100%;\r\n      text-align: center;\r\n\r\n      .empty-content {\r\n        .empty-icon {\r\n          margin-bottom: 20px;\r\n        }\r\n\r\n        .empty-text {\r\n          font-size: 14px;\r\n          color: #999;\r\n          margin-bottom: 20px;\r\n        }\r\n\r\n        .create-btn {\r\n          padding: 10px 20px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .task-list {\r\n      .task-items {\r\n        .task-item {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          padding: 16px;\r\n          border: 1px solid #e8e8e8;\r\n          border-radius: 8px;\r\n          margin-bottom: 12px;\r\n          background: #fff;\r\n\r\n          &:hover {\r\n            border-color: #409eff;\r\n            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\r\n          }\r\n\r\n          .task-info {\r\n            flex: 1;\r\n\r\n            .task-header {\r\n              display: flex;\r\n              justify-content: space-between;\r\n              align-items: center;\r\n              margin-bottom: 8px;\r\n\r\n              .task-name {\r\n                font-size: 14px;\r\n                font-weight: 500;\r\n                color: #333;\r\n              }\r\n\r\n              .task-status {\r\n                padding: 2px 8px;\r\n                border-radius: 4px;\r\n                font-size: 12px;\r\n\r\n                &.status-running {\r\n                  background: #f0f9ff;\r\n                  color: #1890ff;\r\n                }\r\n\r\n                &.status-pending {\r\n                  background: #f6f6f6;\r\n                  color: #666;\r\n                }\r\n              }\r\n            }\r\n\r\n            .task-schedule {\r\n              display: flex;\r\n              align-items: center;\r\n              font-size: 12px;\r\n              color: #666;\r\n\r\n              i {\r\n                margin-right: 4px;\r\n              }\r\n            }\r\n          }\r\n\r\n          .task-actions {\r\n            display: flex;\r\n            gap: 8px;\r\n\r\n            .el-button {\r\n              padding: 4px;\r\n              min-width: auto;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 创建任务弹窗样式\r\n.create-task-dialog {\r\n  .task-form {\r\n    .task-requirement-section,\r\n    .execute-time-section {\r\n      margin-bottom: 24px;\r\n\r\n      .section-label {\r\n        font-size: 14px;\r\n        font-weight: 500;\r\n        margin-bottom: 16px;\r\n        color: #333;\r\n\r\n        .required {\r\n          color: #f56c6c;\r\n          margin-left: 4px;\r\n        }\r\n      }\r\n\r\n      .form-group {\r\n        margin-bottom: 16px;\r\n\r\n        .input-label {\r\n          font-size: 13px;\r\n          color: #666;\r\n          margin-bottom: 8px;\r\n        }\r\n\r\n        .task-name-input,\r\n        .task-description-input {\r\n          width: 100%;\r\n        }\r\n      }\r\n\r\n      .time-selector {\r\n        display: flex;\r\n        gap: 12px;\r\n\r\n        .frequency-select {\r\n          width: 120px;\r\n        }\r\n\r\n        .datetime-picker,\r\n        .time-picker {\r\n          flex: 1;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .dialog-footer {\r\n    text-align: right;\r\n\r\n    .el-button {\r\n      margin-left: 8px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAshBA,IAAAA,gBAAA,GAAAC,OAAA;AAcA,IAAAC,MAAA,GAAAD,OAAA;AAAA,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,UAAA;MACA;MACAC,SAAA;MACA;MACAC,WAAA;QACAC,QAAA;QACAC,SAAA;QACAC,eAAA;QACAC,aAAA;QACAC,cAAA;MACA;MACA;MACAC,gBAAA;MACAC,UAAA;MACAC,OAAA;MACA;MACAC,mBAAA;MACAC,aAAA;MACAC,UAAA;MACA;MACAC,iBAAA;MACAC,WAAA;MACAC,QAAA;QACAC,QAAA;QACAC,YAAA;MACA;MACAC,SAAA;QACAF,QAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAJ,YAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAC,yBAAA;MACAC,mBAAA;MACAC,gBAAA;QACAR,QAAA;QACAS,SAAA;QACAC,SAAA;QACAT,YAAA;MACA;MACAU,iBAAA;QACAX,QAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAI,SAAA,GACA;UAAAN,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAJ,YAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAO,aAAA;MACA;MACAC,sBAAA;MAAA;MACAC,aAAA;MAAA;MACAC,uBAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,QAAA;QACAC,cAAA;QAAA;QACA3C,IAAA;QAAA;QACA4C,WAAA;QACAC,YAAA;QACAC,iBAAA;QAAA;QACAX,SAAA;QACAV,QAAA;MACA;MACAsB,eAAA;MAAA;MACA;MACAC,iBAAA;QACAC,OAAA;QACAC,QAAA;QACAhD,OAAA;MACA;IACA;EACA;EACAiD,OAAA,WAAAA,QAAA;IACA;IACA,KAAA1C,SAAA;IACA,KAAA2C,OAAA;IACA;IACA;EACA;EACAC,OAAA;IACA,eACAD,OAAA,WAAAA,QAAAE,gBAAA;MAAA,IAAAC,KAAA;MACA,KAAArD,OAAA;;MAEA;MACA,IAAAoD,gBAAA;QACA,KAAA5C,WAAA,CAAAC,QAAA,GAAA2C,gBAAA,CAAAE,IAAA;QACA,KAAA9C,WAAA,CAAAE,SAAA,GAAA0C,gBAAA,CAAAG,KAAA;MACA;MAEA,IAAAC,MAAA;QACA/C,QAAA,OAAAD,WAAA,CAAAC,QAAA;QACAC,SAAA,OAAAF,WAAA,CAAAE;MACA;;MAEA;MACA,SAAAF,WAAA,CAAAG,eAAA,SAAAH,WAAA,CAAAG,eAAA,CAAA8C,IAAA;QACAD,MAAA,CAAAE,SAAA,QAAAlD,WAAA,CAAAG,eAAA,CAAA8C,IAAA;MACA;MACA,SAAAjD,WAAA,CAAAI,aAAA,SAAAJ,WAAA,CAAAI,aAAA,CAAA6C,IAAA;QACAD,MAAA,CAAAG,gBAAA,QAAAnD,WAAA,CAAAI,aAAA,CAAA6C,IAAA;MACA;MACA,SAAAjD,WAAA,CAAAK,cAAA,kBAAAL,WAAA,CAAAK,cAAA,KAAA+C,SAAA,SAAApD,WAAA,CAAAK,cAAA;QACA;QACA,IAAAgD,SAAA;UAAA;UAAA;UAAA;UAAA;QAAA;QACAL,MAAA,CAAAM,MAAA,GAAAD,SAAA,MAAArD,WAAA,CAAAK,cAAA;MACA;;MAEA;MACA,SAAAN,SAAA,SAAAA,SAAA,CAAAwD,MAAA;QACAP,MAAA,CAAAQ,iBAAA,QAAAzD,SAAA;QACAiD,MAAA,CAAAS,eAAA,QAAA1D,SAAA;QACA2D,OAAA,CAAAC,GAAA,sBAAA5D,SAAA;MACA;;MAEA;MACA2D,OAAA,CAAAC,GAAA,mBAAAX,MAAA;MACA,IAAAY,oCAAA,EAAAZ,MAAA,EAAAa,IAAA,WAAAC,QAAA;QACAJ,OAAA,CAAAC,GAAA,cAAAG,QAAA;QACA;;QAEA;QACA,IAAAC,YAAA;QACA,IAAAD,QAAA,CAAAE,OAAA;UACA;UACAD,YAAA,GAAAD,QAAA,CAAAE,OAAA;QACA,WAAAF,QAAA,CAAAvE,IAAA,IAAAuE,QAAA,CAAAvE,IAAA,CAAAyE,OAAA;UACA;UACAD,YAAA,GAAAD,QAAA,CAAAvE,IAAA,CAAAyE,OAAA;QACA,WAAAF,QAAA,CAAAvE,IAAA,IAAAuE,QAAA,CAAAvE,IAAA,CAAA0E,IAAA;UACA;UACAF,YAAA,GAAAD,QAAA,CAAAvE,IAAA,CAAA0E,IAAA;QACA,WAAAH,QAAA,CAAAvE,IAAA,IAAA2E,KAAA,CAAAC,OAAA,CAAAL,QAAA,CAAAvE,IAAA;UACA;UACAwE,YAAA,GAAAD,QAAA,CAAAvE,IAAA;QACA,WAAAuE,QAAA,CAAAG,IAAA;UACA;UACAF,YAAA,GAAAD,QAAA,CAAAG,IAAA;QACA;UACAF,YAAA;QACA;QACAlB,KAAA,CAAA/C,UAAA,GAAAiE,YAAA,CACAK,MAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAC,EAAA,YAAAD,IAAA,CAAAC,EAAA,KAAAlB,SAAA,IAAAiB,IAAA,CAAAC,EAAA;QAAA;QAAA,CACAC,GAAA,WAAAF,IAAA;UACA;UACA,IAAAC,EAAA,GAAAE,QAAA,CAAAH,IAAA,CAAAC,EAAA;UACA,IAAAG,KAAA,CAAAH,EAAA;YACA;UACA;UAEA,IAAAI,UAAA;YACAJ,EAAA,EAAAA,EAAA;YAAA;YACAnE,eAAA,EAAAkE,IAAA,CAAAnB,SAAA,IAAAmB,IAAA,CAAAM,QAAA;YAAA;YACAvE,aAAA,EAAAiE,IAAA,CAAAlB,gBAAA,IAAAkB,IAAA,CAAAO,eAAA;YAAA;YACAC,mBAAA,EAAAR,IAAA,CAAAlB,gBAAA,IAAAkB,IAAA,CAAAO,eAAA;YAAA;YACAE,UAAA,EAAAT,IAAA,CAAAU,WAAA,IAAAV,IAAA,CAAAS,UAAA;YACA;YACA;YACAxB,MAAA,EAAAT,KAAA,CAAAmC,aAAA,CAAAX,IAAA,CAAAf,MAAA;YACA;YACA2B,YAAA,EAAAZ,IAAA,CAAAa,cAAA,IAAAb,IAAA,CAAAY,YAAA;UACA;UACA,OAAAP,UAAA;QACA,GACAN,MAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA;QAAA;;QAEA;QACA,IAAAP,QAAA,CAAAjE,KAAA,KAAAuD,SAAA;UACAP,KAAA,CAAAhD,KAAA,GAAAiE,QAAA,CAAAjE,KAAA;QACA,WAAAiE,QAAA,CAAAvE,IAAA,IAAAuE,QAAA,CAAAvE,IAAA,CAAAM,KAAA,KAAAuD,SAAA;UACAP,KAAA,CAAAhD,KAAA,GAAAiE,QAAA,CAAAvE,IAAA,CAAAM,KAAA;QACA;UACAgD,KAAA,CAAAhD,KAAA;QACA;QAEAgD,KAAA,CAAArD,OAAA;MACA,GAAA2F,KAAA,WAAAC,KAAA;QACAvC,KAAA,CAAAwC,MAAA,CAAAC,QAAA,mBAAAF,KAAA,CAAAjE,OAAA;QACA0B,KAAA,CAAArD,OAAA;MACA;IACA;IAEA,aACAwF,aAAA,WAAAA,cAAAO,UAAA;MACA;MACA;MACA,IAAAlC,SAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,SAAA,CAAAkC,UAAA,MAAAnC,SAAA,GAAAC,SAAA,CAAAkC,UAAA;IACA;IAEA,oBACAC,iBAAA,WAAAA,kBAAAnF,cAAA;MACA;MACA;MACA,IAAAgD,SAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,SAAA,CAAAhD,cAAA,MAAA+C,SAAA,GAAAC,SAAA,CAAAhD,cAAA;IACA;IAEA,eACAoF,sBAAA,WAAAA,uBAAAC,KAAA;MACAhC,OAAA,CAAAC,GAAA,iBAAA+B,KAAA;MACA;MACA;IACA;IAEA,cACAC,oBAAA,WAAAA,qBAAAD,KAAA;MACAhC,OAAA,CAAAC,GAAA,gBAAA+B,KAAA;MACA;MACA;IACA;IAEA,eACAE,sBAAA,WAAAA,uBAAAF,KAAA;MACAhC,OAAA,CAAAC,GAAA,eAAA+B,KAAA;MACA;MACA,KAAAG,WAAA;IACA;IAEA,aACAA,WAAA,WAAAA,YAAA;MACA;MACA,KAAA7F,WAAA,CAAAC,QAAA;MACA;MACAyD,OAAA,CAAAC,GAAA,uBAAA3D,WAAA;MACA,KAAA0C,OAAA;IACA;IAEA,aACAoD,UAAA,WAAAA,WAAA;MACA,KAAA/F,SAAA;MACA;MACA,KAAAC,WAAA;QACAC,QAAA;QACAC,SAAA;QACAC,eAAA;QACAC,aAAA;QACAC,cAAA;MACA;MACA;MACA,KAAA0F,SAAA;MACA;MACArC,OAAA,CAAAC,GAAA,yBAAA3D,WAAA;MACA,KAAA6F,WAAA;IACA;IAEA,cACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA;MACA,KAAAxG,GAAA,GAAAwG,SAAA,CACA7B,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,EAAA,YAAAD,IAAA,CAAAC,EAAA,KAAAlB,SAAA;MAAA,GACAmB,GAAA,WAAAF,IAAA;QAAA,OAAAG,QAAA,CAAAH,IAAA,CAAAC,EAAA;MAAA;MAEA,KAAA5E,MAAA,GAAAuG,SAAA,CAAA1C,MAAA;MACA,KAAA5D,QAAA,IAAAsG,SAAA,CAAA1C,MAAA;IACA;IAEA,aACA2C,cAAA,WAAAA,eAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAzE,aAAA,GAAAwE,GAAA;MACA,KAAA7F,gBAAA;MACA,KAAAC,UAAA;;MAEA;MACA,IAAA8F,oCAAA,EAAAF,GAAA,CAAAG,MAAA,IAAAH,GAAA,CAAA7B,EAAA,EAAAT,IAAA,WAAAC,QAAA;QACAsC,MAAA,CAAA5F,OAAA,GAAAsD,QAAA,CAAAvE,IAAA;QACA6G,MAAA,CAAA7F,UAAA;MACA,GAAA4E,KAAA;QACAiB,MAAA,CAAA7F,UAAA;MACA;IACA;IAEA,WACAgG,gBAAA,WAAAA,iBAAAJ,GAAA;MAAA,IAAAK,MAAA;MACA,KAAA7E,aAAA,GAAAwE,GAAA;MACA,KAAA1F,mBAAA;MACA,KAAAC,aAAA;;MAEA;MACA+F,UAAA;QACAD,MAAA,CAAA7F,UAAA;UACAR,eAAA,EAAAgG,GAAA,CAAAhG,eAAA;UACAC,aAAA,EAAA+F,GAAA,CAAA/F,aAAA;UACAyE,mBAAA,EAAAsB,GAAA,CAAAtB,mBAAA;UACA6B,aAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;UACAC,SAAA;YACAC,QAAA,EAAAJ,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;YACAG,OAAA,EAAAL,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;YACAI,QAAA,EAAAN,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;UACA;QACA;QACAL,MAAA,CAAA9F,aAAA;MACA;IACA;IAEA,SACAwG,YAAA,WAAAA,aAAAf,GAAA;MACA;MACA,IAAAA,GAAA,CAAAlB,YAAA;QACA;QACAkC,MAAA,CAAAC,IAAA,CAAAjB,GAAA,CAAAlB,YAAA;MACA;QACA;QACA,KAAAI,MAAA,CAAAgC,GAAA;MACA;IACA;IAEA,WACAC,aAAA,WAAAA,cAAAnB,GAAA;MACA;MACA,KAAAA,GAAA,CAAAlB,YAAA;QACA,KAAAI,MAAA,CAAAkC,UAAA;QACA;MACA;;MAEA;MACA,IAAApB,GAAA,CAAA7C,MAAA;QACA,KAAA+B,MAAA,CAAAkC,UAAA;QACA;MACA;MAEA,KAAA5F,aAAA,GAAAwE,GAAA;MACA,KAAArF,QAAA;QACAC,QAAA;QACAC,YAAA,mCAAAwG,MAAA,CAAArB,GAAA,CAAAhG,eAAA;MACA;MACA,KAAAS,iBAAA;IACA;IAEA,WACA6G,kBAAA,WAAAA,mBAAAtB,GAAA;MACA,KAAAxE,aAAA,GAAAwE,GAAA;MACA,KAAA5E,gBAAA;QACAR,QAAA;QACAS,SAAA;QACAC,SAAA;QACAT,YAAA,mCAAAwG,MAAA,CAAArB,GAAA,CAAAhG,eAAA;MACA;MACA,KAAAkB,yBAAA;IACA;IAEA,aACAqG,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,aAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,KAAAH,MAAA,CAAAhG,aAAA,CAAAsD,YAAA;YACA0C,MAAA,CAAAtC,MAAA,CAAAC,QAAA;YACA;UACA;;UAEA;UACA,IAAAqC,MAAA,CAAAhG,aAAA,CAAA2B,MAAA;YACAqE,MAAA,CAAAtC,MAAA,CAAAC,QAAA;YACA;UACA;;UAEA;UACA,KAAAqC,MAAA,CAAAI,eAAA,CAAAJ,MAAA,CAAA7G,QAAA,CAAAC,QAAA;YACA;UACA;UAEA4G,MAAA,CAAA9G,WAAA;;UAEA;UACA,IAAAmH,QAAA;YACAC,UAAA,EAAAN,MAAA,CAAA7G,QAAA,CAAAC,QAAA;YACAmH,WAAA;cACA/H,eAAA,EAAAwH,MAAA,CAAAhG,aAAA,CAAAxB,eAAA;cACAC,aAAA,EAAAuH,MAAA,CAAAhG,aAAA,CAAAvB,aAAA;cACAyE,mBAAA,EAAA8C,MAAA,CAAAhG,aAAA,CAAAkD,mBAAA;cACAI,YAAA,EAAA0C,MAAA,CAAAhG,aAAA,CAAAsD,YAAA;cAAA;cACAyB,aAAA;cAAA;cACAI,SAAA;cAAA;cACAqB,WAAA;cAAA;cACA7B,MAAA,EAAAqB,MAAA,CAAAhG,aAAA,CAAA2C,EAAA;cACAQ,UAAA,EAAA6C,MAAA,CAAAhG,aAAA,CAAAmD,UAAA;cACAxB,MAAA,EAAAqE,MAAA,CAAAhG,aAAA,CAAA2B,MAAA;cACA8E,QAAA,MAAAC,IAAA,GAAAC,WAAA;YACA;YACAC,gBAAA;cACAC,OAAA,EAAAb,MAAA,CAAA7G,QAAA,CAAAE,YAAA;cACAyH,SAAA,EAAAd,MAAA,CAAAhG,aAAA,CAAAsD,YAAA;YACA;YACAhD,cAAA,EAAA0F,MAAA,CAAAhG,aAAA,CAAA2C,EAAA;YACAoE,SAAA;UACA;UAEA,IAAAC,2BAAA,EAAAX,QAAA,EAAAnE,IAAA,WAAAC,QAAA;YACA;YACA,IAAAA,QAAA,IAAAA,QAAA,CAAA8E,OAAA;cACAjB,MAAA,CAAAtC,MAAA,CAAAwD,UAAA;;cAEA;cACA,IAAA/E,QAAA,CAAAvE,IAAA,IAAAuE,QAAA,CAAAvE,IAAA,CAAAuJ,UAAA,KAAAnB,MAAA,CAAAhG,aAAA,CAAAsD,YAAA;gBACA0C,MAAA,CAAAhG,aAAA,CAAAsD,YAAA,GAAAnB,QAAA,CAAAvE,IAAA,CAAAuJ,UAAA;gBACApF,OAAA,CAAAC,GAAA,eAAAG,QAAA,CAAAvE,IAAA,CAAAuJ,UAAA;cACA;YACA;cACAnB,MAAA,CAAAtC,MAAA,CAAAwD,UAAA;YACA;YAEAlB,MAAA,CAAA/G,iBAAA;YACA+G,MAAA,CAAA9G,WAAA;UACA,GAAAsE,KAAA,WAAAC,KAAA;YACA1B,OAAA,CAAA0B,KAAA,UAAAA,KAAA;YACA,IAAAA,KAAA,CAAAtB,QAAA,IAAAsB,KAAA,CAAAtB,QAAA,CAAAvE,IAAA,IAAA6F,KAAA,CAAAtB,QAAA,CAAAvE,IAAA,CAAA8H,GAAA;cACAM,MAAA,CAAAtC,MAAA,CAAAC,QAAA,YAAAF,KAAA,CAAAtB,QAAA,CAAAvE,IAAA,CAAA8H,GAAA;YACA;cACAM,MAAA,CAAAtC,MAAA,CAAAC,QAAA;YACA;YACAqC,MAAA,CAAA9G,WAAA;UACA;QACA;MACA;IACA;IAEA,aACAkI,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,MAAA;MACA,KAAApB,KAAA,qBAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAkB,MAAA,CAAA1H,mBAAA;UACA,IAAAkB,QAAA;YACAP,cAAA,EAAA+G,MAAA,CAAArH,aAAA,CAAA2C,EAAA;YACAvD,QAAA,EAAAiI,MAAA,CAAAzH,gBAAA,CAAAR,QAAA;YACAS,SAAA,EAAAwH,MAAA,CAAAzH,gBAAA,CAAAC,SAAA;YACAC,SAAA,EAAAuH,MAAA,CAAAzH,gBAAA,CAAAE,SAAA;YACAT,YAAA,EAAAgI,MAAA,CAAAzH,gBAAA,CAAAP;UACA;UAEA,IAAAiI,gCAAA,EAAAzG,QAAA,EAAAqB,IAAA,WAAAC,QAAA;YACAkF,MAAA,CAAA3D,MAAA,CAAAwD,UAAA;YACAG,MAAA,CAAA3H,yBAAA;YACA2H,MAAA,CAAA1H,mBAAA;UACA,GAAA6D,KAAA;YACA6D,MAAA,CAAA1H,mBAAA;UACA;QACA;MACA;IACA;IAEA,aACA4H,YAAA,WAAAA,aAAA/C,GAAA;MAAA,IAAAgD,MAAA;MACA,IAAA1J,GAAA,EAAA2J,KAAA;;MAEA;MACA,KAAAjD,GAAA,IAAAA,GAAA,YAAAkD,KAAA;QACA;QACA,UAAA5J,GAAA,SAAAA,GAAA,CAAA8D,MAAA;UACA,KAAA8B,MAAA,CAAAC,QAAA;UACA;QACA;QACA;QACA7F,GAAA,QAAAA,GAAA,CAAA2E,MAAA,WAAAE,EAAA;UAAA,OAAAA,EAAA,aAAAG,KAAA,CAAAH,EAAA;QAAA,GAAAC,GAAA,WAAAD,EAAA;UAAA,OAAAE,QAAA,CAAAF,EAAA;QAAA;QACA,IAAA7E,GAAA,CAAA8D,MAAA;UACA,KAAA8B,MAAA,CAAAC,QAAA;UACA;QACA;QACA8D,KAAA,QAAAtJ,UAAA,CAAAsE,MAAA,WAAAC,IAAA;UAAA,OAAA5E,GAAA,CAAA6J,QAAA,CAAAjF,IAAA,CAAAC,EAAA;QAAA,GAAAC,GAAA,WAAAF,IAAA;UAAA,OAAAA,IAAA,CAAAlE,eAAA;QAAA,GAAAoJ,IAAA;MACA;QACA;QACA,KAAApD,GAAA,CAAA7B,EAAA,IAAA6B,GAAA,CAAA7B,EAAA;UACA,KAAAe,MAAA,CAAAC,QAAA;UACA;QACA;QACA7F,GAAA,IAAA+E,QAAA,CAAA2B,GAAA,CAAA7B,EAAA;QACA8E,KAAA,GAAAjD,GAAA,CAAAhG,eAAA;MACA;MACA,KAAAkF,MAAA,CAAAmE,OAAA,kEAAAhC,MAAA,CAAA4B,KAAA,eAAAvF,IAAA;QACA;QACA,IAAApE,GAAA,CAAA8D,MAAA;UACA;UACA,WAAAkG,mCAAA,EAAAhK,GAAA;QACA;UACA;UACA,WAAAiK,yCAAA,EAAAjK,GAAA;QACA;MACA,GAAAoE,IAAA,WAAAC,QAAA;QACAJ,OAAA,CAAAC,GAAA,mBAAAG,QAAA;;QAEA;QACA,IAAA6F,kBAAA,GAAAR,MAAA,CAAArJ,UAAA,CAAAyD,MAAA;QACA,IAAAqG,YAAA,GAAAnK,GAAA,CAAA8D,MAAA;;QAEA;QACA,IAAAoG,kBAAA,IAAAC,YAAA,IAAAT,MAAA,CAAAnJ,WAAA,CAAAC,QAAA;UACAkJ,MAAA,CAAAnJ,WAAA,CAAAC,QAAA,GAAAkJ,MAAA,CAAAnJ,WAAA,CAAAC,QAAA;UACAyD,OAAA,CAAAC,GAAA,iBAAAwF,MAAA,CAAAnJ,WAAA,CAAAC,QAAA;QACA;;QAEA;QACAkJ,MAAA,CAAA1J,GAAA;QACA0J,MAAA,CAAAzJ,MAAA;QACAyJ,MAAA,CAAAxJ,QAAA;;QAEA;QACAwJ,MAAA,CAAAzG,OAAA;QAEAyG,MAAA,CAAA9D,MAAA,CAAAwD,UAAA;MACA,GAAA1D,KAAA,WAAAC,KAAA;QACA,IAAAA,KAAA,CAAAtB,QAAA,IAAAsB,KAAA,CAAAtB,QAAA,CAAAvE,IAAA;UACA,IAAA6F,KAAA,CAAAtB,QAAA,CAAAvE,IAAA,CAAA8H,GAAA;YACA8B,MAAA,CAAA9D,MAAA,CAAAC,QAAA,CAAAF,KAAA,CAAAtB,QAAA,CAAAvE,IAAA,CAAA8H,GAAA;UACA,WAAAjC,KAAA,CAAAtB,QAAA,CAAAvE,IAAA,CAAA4B,OAAA;YACAgI,MAAA,CAAA9D,MAAA,CAAAC,QAAA,CAAAF,KAAA,CAAAtB,QAAA,CAAAvE,IAAA,CAAA4B,OAAA;UACA,WAAAiE,KAAA,CAAAtB,QAAA,CAAAvE,IAAA,CAAAsK,MAAA;YACA;YACA,IAAA3F,KAAA,CAAAC,OAAA,CAAAiB,KAAA,CAAAtB,QAAA,CAAAvE,IAAA,CAAAsK,MAAA;cACA,IAAAC,OAAA,GAAA1E,KAAA,CAAAtB,QAAA,CAAAvE,IAAA,CAAAsK,MAAA,CAAAtF,GAAA,WAAAwF,CAAA;gBAAA,OAAAA,CAAA,CAAA1C,GAAA,IAAA0C,CAAA,CAAA5I,OAAA,IAAA6I,IAAA,CAAAC,SAAA,CAAAF,CAAA;cAAA,GAAAR,IAAA;cACAJ,MAAA,CAAA9D,MAAA,CAAAC,QAAA,8BAAAkC,MAAA,CAAAsC,OAAA;YACA;cACAX,MAAA,CAAA9D,MAAA,CAAAC,QAAA,8BAAAkC,MAAA,CAAApC,KAAA,CAAAtB,QAAA,CAAAvE,IAAA,CAAAsK,MAAA;YACA;UACA;YACAV,MAAA,CAAA9D,MAAA,CAAAC,QAAA,8BAAAkC,MAAA,CAAAwC,IAAA,CAAAC,SAAA,CAAA7E,KAAA,CAAAtB,QAAA,CAAAvE,IAAA;UACA;QACA,WAAA6F,KAAA,CAAAjE,OAAA;UACAgI,MAAA,CAAA9D,MAAA,CAAAC,QAAA,8BAAAkC,MAAA,CAAApC,KAAA,CAAAjE,OAAA;QACA;UACAgI,MAAA,CAAA9D,MAAA,CAAAC,QAAA;QACA;MACA;IACA;IAEA,aACA4E,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAA9E,MAAA,CAAAmE,OAAA,qBAAA3F,IAAA;QACA,IAAAb,MAAA;;QAEA;QACA,IAAAmH,MAAA,CAAAnK,WAAA,CAAAG,eAAA;UACA6C,MAAA,CAAAoH,gBAAA,GAAAD,MAAA,CAAAnK,WAAA,CAAAG,eAAA;QACA;QACA,IAAAgK,MAAA,CAAAnK,WAAA,CAAAI,aAAA;UACA4C,MAAA,CAAAqH,cAAA,GAAAF,MAAA,CAAAnK,WAAA,CAAAI,aAAA;QACA;QACA,IAAA+J,MAAA,CAAAnK,WAAA,CAAAK,cAAA,aAAA8J,MAAA,CAAAnK,WAAA,CAAAK,cAAA,KAAA+C,SAAA,IAAA+G,MAAA,CAAAnK,WAAA,CAAAK,cAAA;UACA2C,MAAA,CAAAsH,eAAA,GAAAH,MAAA,CAAAnK,WAAA,CAAAK,cAAA;QACA;;QAEA;QACA,IAAA8J,MAAA,CAAApK,SAAA,IAAAoK,MAAA,CAAApK,SAAA,CAAAwD,MAAA;UACAP,MAAA,CAAAQ,iBAAA,GAAA2G,MAAA,CAAApK,SAAA;UACAiD,MAAA,CAAAS,eAAA,GAAA0G,MAAA,CAAApK,SAAA;QACA;QAEA,WAAAwK,mCAAA,EAAAvH,MAAA;MACA,GAAAa,IAAA,WAAAC,QAAA;QACA;QACA,IAAA0G,IAAA,OAAAC,IAAA,EAAA3G,QAAA;UAAA4G,IAAA;QAAA;QACA,IAAAC,GAAA,GAAAxD,MAAA,CAAAyD,GAAA,CAAAC,eAAA,CAAAL,IAAA;QACA,IAAAM,IAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,IAAA,CAAAG,IAAA,GAAAN,GAAA;QACAG,IAAA,CAAAI,QAAA,+BAAA1D,MAAA,KAAAa,IAAA,GAAAC,WAAA,GAAA6C,KAAA;QACAJ,QAAA,CAAAK,IAAA,CAAAC,WAAA,CAAAP,IAAA;QACAA,IAAA,CAAAQ,KAAA;QACAP,QAAA,CAAAK,IAAA,CAAAG,WAAA,CAAAT,IAAA;QACA3D,MAAA,CAAAyD,GAAA,CAAAY,eAAA,CAAAb,GAAA;QAEAR,MAAA,CAAA9E,MAAA,CAAAwD,UAAA;MACA,GAAA1D,KAAA,WAAAC,KAAA;QACA,IAAAA,KAAA,CAAAtB,QAAA,IAAAsB,KAAA,CAAAtB,QAAA,CAAAvE,IAAA,IAAA6F,KAAA,CAAAtB,QAAA,CAAAvE,IAAA,CAAA8H,GAAA;UACA8C,MAAA,CAAA9E,MAAA,CAAAC,QAAA,CAAAF,KAAA,CAAAtB,QAAA,CAAAvE,IAAA,CAAA8H,GAAA;QACA;UACA8C,MAAA,CAAA9E,MAAA,CAAAC,QAAA;QACA;MACA;IACA;IAEA,WACAmG,kBAAA,WAAAA,mBAAA;MACA,KAAApG,MAAA,CAAAwD,UAAA;IACA;IAEA,+BACAd,eAAA,WAAAA,gBAAA4C,GAAA;MACA,KAAAA,GAAA,KAAAA,GAAA,CAAA1H,IAAA;QACA,KAAAoC,MAAA,CAAAC,QAAA;QACA;MACA;MAEA,IAAAoG,OAAA,GAAAf,GAAA,CAAA1H,IAAA;;MAEA;MACA,KAAAyI,OAAA,CAAApC,QAAA;QACAoC,OAAA,gBAAAA,OAAA;MACA;;MAEA;MACA;QACA,IAAAd,GAAA,CAAAc,OAAA;QACAhI,OAAA,CAAAC,GAAA,aAAA+H,OAAA;QACA,OAAAA,OAAA;MACA,SAAAtG,KAAA;QACA;QACA,IAAAsG,OAAA,CAAApC,QAAA,WAAAoC,OAAA,CAAAnI,MAAA;UACAG,OAAA,CAAAC,GAAA,iBAAA+H,OAAA;UACA,KAAArG,MAAA,CAAAkC,UAAA;UACA,OAAAmE,OAAA;QACA;QAEA,KAAArG,MAAA,CAAAC,QAAA;QACA;MACA;IACA;IAEA,eACAqG,gBAAA,WAAAA,iBAAArI,MAAA;MACA,IAAAD,SAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,SAAA,CAAAC,MAAA;IACA;IAEA,aACAsI,aAAA,WAAAA,cAAAtI,MAAA;MACA,IAAAD,SAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAC,MAAA;IACA;IAEA,iBACAuI,kBAAA,WAAAA,mBAAAC,KAAA;MACA,IAAAC,QAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,QAAA,CAAAD,KAAA;IACA;IAEA;IAEA;IACAE,eAAA,WAAAA,gBAAA;MACA,KAAApK,sBAAA;MACA;MACA,KAAAqK,iBAAA;IACA;IAEA;IACAC,oBAAA,WAAAA,qBAAA;MACA,KAAAtK,sBAAA;IACA;IAEA;IACAuK,qBAAA,WAAAA,sBAAA;MACA,KAAAC,aAAA;MACA,KAAArK,gBAAA;MACA,KAAAsK,mBAAA;MACA,KAAAvK,uBAAA;IACA;IAEA;IACAwK,kBAAA,WAAAA,mBAAA;MACA,KAAAF,aAAA;MACA,KAAArK,gBAAA;MACA,KAAAsK,mBAAA;MACA,KAAAvK,uBAAA;IACA;IAEA;IACAuK,mBAAA,WAAAA,oBAAA;MAAA,IAAAE,MAAA;MAAA,WAAAC,kBAAA,CAAAnN,OAAA,mBAAAoN,aAAA,CAAApN,OAAA,IAAAqN,CAAA,UAAAC,QAAA;QAAA,IAAA7I,QAAA,EAAA8I,KAAA,EAAAC,EAAA;QAAA,WAAAJ,aAAA,CAAApN,OAAA,IAAAyN,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAF,QAAA,CAAAC,CAAA;cAAA,OAEA,IAAApJ,oCAAA;gBACA3D,QAAA;gBACAC,SAAA;cACA;YAAA;cAHA4D,QAAA,GAAAiJ,QAAA,CAAAG,CAAA;cAKA,IAAApJ,QAAA,CAAA8E,OAAA;gBACA;gBACAgE,KAAA;gBAEA,IAAA9I,QAAA,CAAAvE,IAAA,IAAAuE,QAAA,CAAAvE,IAAA,CAAA0E,IAAA;kBACA2I,KAAA,GAAA9I,QAAA,CAAAvE,IAAA,CAAA0E,IAAA;gBACA,WAAAC,KAAA,CAAAC,OAAA,CAAAL,QAAA,CAAAvE,IAAA;kBACAqN,KAAA,GAAA9I,QAAA,CAAAvE,IAAA;gBACA,WAAAuE,QAAA,CAAAG,IAAA;kBACA2I,KAAA,GAAA9I,QAAA,CAAAG,IAAA;gBACA;;gBAEA;gBACAsI,MAAA,CAAAlK,eAAA,GAAAuK,KAAA,CAAArI,GAAA,WAAA4I,IAAA;kBAAA;oBACA7I,EAAA,EAAA6I,IAAA,CAAA7I,EAAA;oBACAnE,eAAA,EAAAgN,IAAA,CAAAhN,eAAA,IAAAgN,IAAA,CAAA/C,gBAAA,mBAAA5C,MAAA,CAAA2F,IAAA,CAAA7I,EAAA;oBACAgC,MAAA,EAAA6G,IAAA,CAAA7I,EAAA;oBACA8I,QAAA,EAAAD,IAAA,CAAAC,QAAA,IAAAD,IAAA,CAAA9C,cAAA;oBACAxF,mBAAA,EAAAsI,IAAA,CAAAtI,mBAAA,IAAAsI,IAAA,CAAAE,oBAAA;oBACAvI,UAAA,EAAAqI,IAAA,CAAArI,UAAA,IAAAqI,IAAA,CAAApI;kBACA;gBAAA;cACA;gBACAwH,MAAA,CAAAe,QAAA,CAAAlI,KAAA,CAAAtB,QAAA,CAAAuD,GAAA;gBACAkF,MAAA,CAAAlK,eAAA;cACA;cAAA0K,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAJ,EAAA,GAAAE,QAAA,CAAAG,CAAA;cAEAX,MAAA,CAAAe,QAAA,CAAAlI,KAAA;cACAmH,MAAA,CAAAlK,eAAA;YAAA;cAAA,OAAA0K,QAAA,CAAAQ,CAAA;UAAA;QAAA,GAAAZ,OAAA;MAAA;IAEA;IAEA;IACAV,iBAAA,WAAAA,kBAAA;MAAA,IAAAuB,MAAA;MAAA,WAAAhB,kBAAA,CAAAnN,OAAA,mBAAAoN,aAAA,CAAApN,OAAA,IAAAqN,CAAA,UAAAe,SAAA;QAAA,IAAA3J,QAAA,EAAA4J,GAAA;QAAA,WAAAjB,aAAA,CAAApN,OAAA,IAAAyN,CAAA,WAAAa,SAAA;UAAA,kBAAAA,SAAA,CAAAX,CAAA;YAAA;cAAAW,SAAA,CAAAV,CAAA;cAAAU,SAAA,CAAAX,CAAA;cAAA,OAEA,IAAAY,iCAAA;YAAA;cAAA9J,QAAA,GAAA6J,SAAA,CAAAT,CAAA;cAEA,IAAApJ,QAAA,CAAA8E,OAAA;gBACA;;gBAEA,IAAA9E,QAAA,CAAAvE,IAAA,IAAAuE,QAAA,CAAAvE,IAAA,CAAA0E,IAAA;kBACAuJ,MAAA,CAAA3L,aAAA,GAAAiC,QAAA,CAAAvE,IAAA,CAAA0E,IAAA,CAAAM,GAAA,WAAA4I,IAAA;oBAAA;sBACA7I,EAAA,EAAA6I,IAAA,CAAA7I,EAAA;sBACAuJ,aAAA,EAAAV,IAAA,CAAAU,aAAA;sBACAvO,IAAA,EAAA6N,IAAA,CAAAxI,QAAA;sBACAzC,WAAA,EAAAiL,IAAA,CAAAvI,eAAA;sBACAkJ,WAAA,EAAAX,IAAA,CAAAW,WAAA;sBACAC,eAAA,EAAAZ,IAAA,CAAA1L,SAAA,eAAA0L,IAAA,CAAAa,YAAA,MAAAxG,MAAA,CAAA2F,IAAA,CAAAa,YAAA,OAAAxG,MAAA,CAAA2F,IAAA,CAAAW,WAAA;sBACArM,SAAA,EAAA0L,IAAA,CAAA1L,SAAA;sBACA6B,MAAA,EAAA6J,IAAA,CAAA7J,MAAA;sBACA2K,OAAA,EAAAd,IAAA,CAAAc;oBACA;kBAAA;gBACA,WAAAnK,QAAA,CAAAvE,IAAA,IAAAuE,QAAA,CAAAvE,IAAA,CAAAyE,OAAA,IAAAE,KAAA,CAAAC,OAAA,CAAAL,QAAA,CAAAvE,IAAA,CAAAyE,OAAA;kBACAwJ,MAAA,CAAA3L,aAAA,GAAAiC,QAAA,CAAAvE,IAAA,CAAAyE,OAAA,CAAAO,GAAA,WAAA4I,IAAA;oBAAA;sBACA7I,EAAA,EAAA6I,IAAA,CAAA7I,EAAA;sBACAuJ,aAAA,EAAAV,IAAA,CAAAU,aAAA;sBACAvO,IAAA,EAAA6N,IAAA,CAAAxI,QAAA;sBACAzC,WAAA,EAAAiL,IAAA,CAAAvI,eAAA;sBACAkJ,WAAA,EAAAX,IAAA,CAAAW,WAAA;sBACAC,eAAA,EAAAZ,IAAA,CAAA1L,SAAA,eAAA0L,IAAA,CAAAa,YAAA,MAAAxG,MAAA,CAAA2F,IAAA,CAAAa,YAAA,OAAAxG,MAAA,CAAA2F,IAAA,CAAAW,WAAA;sBACArM,SAAA,EAAA0L,IAAA,CAAA1L,SAAA;sBACA6B,MAAA,EAAA6J,IAAA,CAAA7J,MAAA;sBACA2K,OAAA,EAAAd,IAAA,CAAAc;oBACA;kBAAA;gBACA,WAAA/J,KAAA,CAAAC,OAAA,CAAAL,QAAA,CAAAvE,IAAA;kBACAiO,MAAA,CAAA3L,aAAA,GAAAiC,QAAA,CAAAvE,IAAA,CAAAgF,GAAA,WAAA4I,IAAA;oBAAA;sBACA7I,EAAA,EAAA6I,IAAA,CAAA7I,EAAA;sBACAuJ,aAAA,EAAAV,IAAA,CAAAU,aAAA;sBACAvO,IAAA,EAAA6N,IAAA,CAAAxI,QAAA;sBACAzC,WAAA,EAAAiL,IAAA,CAAAvI,eAAA;sBACAkJ,WAAA,EAAAX,IAAA,CAAAW,WAAA;sBACAC,eAAA,EAAAZ,IAAA,CAAA1L,SAAA,eAAA0L,IAAA,CAAAa,YAAA,MAAAxG,MAAA,CAAA2F,IAAA,CAAAa,YAAA,OAAAxG,MAAA,CAAA2F,IAAA,CAAAW,WAAA;sBACArM,SAAA,EAAA0L,IAAA,CAAA1L,SAAA;sBACA6B,MAAA,EAAA6J,IAAA,CAAA7J,MAAA;sBACA2K,OAAA,EAAAd,IAAA,CAAAc;oBACA;kBAAA;gBACA,WAAAnK,QAAA,CAAAG,IAAA,IAAAC,KAAA,CAAAC,OAAA,CAAAL,QAAA,CAAAG,IAAA;kBACAuJ,MAAA,CAAA3L,aAAA,GAAAiC,QAAA,CAAAG,IAAA,CAAAM,GAAA,WAAA4I,IAAA;oBAAA;sBACA7I,EAAA,EAAA6I,IAAA,CAAA7I,EAAA;sBACAuJ,aAAA,EAAAV,IAAA,CAAAU,aAAA;sBACAvO,IAAA,EAAA6N,IAAA,CAAAxI,QAAA;sBACAzC,WAAA,EAAAiL,IAAA,CAAAvI,eAAA;sBACAkJ,WAAA,EAAAX,IAAA,CAAAW,WAAA;sBACAC,eAAA,EAAAZ,IAAA,CAAA1L,SAAA,eAAA0L,IAAA,CAAAa,YAAA,MAAAxG,MAAA,CAAA2F,IAAA,CAAAa,YAAA,OAAAxG,MAAA,CAAA2F,IAAA,CAAAW,WAAA;sBACArM,SAAA,EAAA0L,IAAA,CAAA1L,SAAA;sBACA6B,MAAA,EAAA6J,IAAA,CAAA7J,MAAA;sBACA2K,OAAA,EAAAd,IAAA,CAAAc;oBACA;kBAAA;gBACA,WAAAnK,QAAA,CAAAE,OAAA,IAAAE,KAAA,CAAAC,OAAA,CAAAL,QAAA,CAAAE,OAAA;kBACAwJ,MAAA,CAAA3L,aAAA,GAAAiC,QAAA,CAAAE,OAAA,CAAAO,GAAA,WAAA4I,IAAA;oBAAA;sBACA7I,EAAA,EAAA6I,IAAA,CAAA7I,EAAA;sBACAuJ,aAAA,EAAAV,IAAA,CAAAU,aAAA;sBACAvO,IAAA,EAAA6N,IAAA,CAAAxI,QAAA;sBACAzC,WAAA,EAAAiL,IAAA,CAAAvI,eAAA;sBACAkJ,WAAA,EAAAX,IAAA,CAAAW,WAAA;sBACAC,eAAA,EAAAZ,IAAA,CAAA1L,SAAA,eAAA0L,IAAA,CAAAa,YAAA,MAAAxG,MAAA,CAAA2F,IAAA,CAAAa,YAAA,OAAAxG,MAAA,CAAA2F,IAAA,CAAAW,WAAA;sBACArM,SAAA,EAAA0L,IAAA,CAAA1L,SAAA;sBACA6B,MAAA,EAAA6J,IAAA,CAAA7J,MAAA;sBACA2K,OAAA,EAAAd,IAAA,CAAAc;oBACA;kBAAA;gBACA;kBACAT,MAAA,CAAA3L,aAAA;gBACA;cACA;gBACA2L,MAAA,CAAAF,QAAA,CAAAlI,KAAA,CAAAtB,QAAA,CAAAuD,GAAA;gBACAmG,MAAA,CAAA3L,aAAA;cACA;cAAA8L,SAAA,CAAAX,CAAA;cAAA;YAAA;cAAAW,SAAA,CAAAV,CAAA;cAAAS,GAAA,GAAAC,SAAA,CAAAT,CAAA;cAEAM,MAAA,CAAAF,QAAA,CAAAlI,KAAA;cACAoI,MAAA,CAAA3L,aAAA;YAAA;cAAA,OAAA8L,SAAA,CAAAJ,CAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IAEA;IAEA;IACAS,qBAAA,WAAAA,sBAAA;MACA,KAAApM,uBAAA;MACA,KAAAsK,aAAA;MACA,KAAArK,gBAAA;IACA;IAEA;IACAqK,aAAA,WAAAA,cAAA;MACA,KAAApK,QAAA;QACAC,cAAA;QACA3C,IAAA;QACA4C,WAAA;QACAC,YAAA;QACAC,iBAAA;QACAX,SAAA;QACAV,QAAA;MACA;IACA;IAEA;IACAoN,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MAAA,WAAA5B,kBAAA,CAAAnN,OAAA,mBAAAoN,aAAA,CAAApN,OAAA,IAAAqN,CAAA,UAAA2B,SAAA;QAAA,IAAA7L,QAAA,EAAAsB,QAAA,EAAAqJ,IAAA,EAAAmB,SAAA,EAAAC,GAAA;QAAA,WAAA9B,aAAA,CAAApN,OAAA,IAAAyN,CAAA,WAAA0B,SAAA;UAAA,kBAAAA,SAAA,CAAAxB,CAAA;YAAA;cAAA,IACAoB,MAAA,CAAAK,gBAAA;gBAAAD,SAAA,CAAAxB,CAAA;gBAAA;cAAA;cAAA,OAAAwB,SAAA,CAAAjB,CAAA;YAAA;cAAAiB,SAAA,CAAAvB,CAAA;cAKAzK,QAAA,GAAA4L,MAAA,CAAAM,aAAA;cAAA,MAEAN,MAAA,CAAArM,gBAAA;gBAAAyM,SAAA,CAAAxB,CAAA;gBAAA;cAAA;cAAAwB,SAAA,CAAAxB,CAAA;cAAA,OAEA,IAAA/D,gCAAA,EAAAzG,QAAA;YAAA;cAAAsB,QAAA,GAAA0K,SAAA,CAAAtB,CAAA;cAAA,KACApJ,QAAA,CAAA8E,OAAA;gBAAA4F,SAAA,CAAAxB,CAAA;gBAAA;cAAA;cACAoB,MAAA,CAAAd,QAAA,CAAA1E,OAAA;cACA;cAAA4F,SAAA,CAAAxB,CAAA;cAAA,OACAoB,MAAA,CAAAnC,iBAAA;YAAA;cAAA,MAEAnI,QAAA,CAAAvE,IAAA,IAAAuE,QAAA,CAAAvE,IAAA,CAAA+E,EAAA;gBAAAkK,SAAA,CAAAxB,CAAA;gBAAA;cAAA;cAAAwB,SAAA,CAAAxB,CAAA;cAAA,OACA,IAAA2B,iCAAA,EAAA7K,QAAA,CAAAvE,IAAA,CAAA+E,EAAA;YAAA;cAAAkK,SAAA,CAAAxB,CAAA;cAAA,OACAoB,MAAA,CAAAnC,iBAAA;YAAA;cAAAuC,SAAA,CAAAxB,CAAA;cAAA;YAAA;cAGAoB,MAAA,CAAAd,QAAA,CAAAlI,KAAA,aAAAtB,QAAA,CAAAuD,GAAA;cAAA,OAAAmH,SAAA,CAAAjB,CAAA;YAAA;cAAAiB,SAAA,CAAAxB,CAAA;cAAA;YAAA;cAIA;cACAG,IAAA,GAAAiB,MAAA,CAAAvM,aAAA,CAAAuM,MAAA,CAAArM,gBAAA;cAAAyM,SAAA,CAAAxB,CAAA;cAAA,OACA,IAAA4B,gCAAA,EAAAzB,IAAA,CAAA7I,EAAA,EAAA9B,QAAA;YAAA;cAAAsB,SAAA,GAAA0K,SAAA,CAAAtB,CAAA;cAAA,KAEApJ,SAAA,CAAA8E,OAAA;gBAAA4F,SAAA,CAAAxB,CAAA;gBAAA;cAAA;cAAAwB,SAAA,CAAAxB,CAAA;cAAA,OAEA,IAAA2B,iCAAA,EAAAxB,IAAA,CAAA7I,EAAA;YAAA;cACA8J,MAAA,CAAAd,QAAA,CAAA1E,OAAA;cACA;cAAA4F,SAAA,CAAAxB,CAAA;cAAA,OACAoB,MAAA,CAAAnC,iBAAA;YAAA;cAAAuC,SAAA,CAAAxB,CAAA;cAAA;YAAA;cAEAoB,MAAA,CAAAd,QAAA,CAAAlI,KAAA,aAAAtB,SAAA,CAAAuD,GAAA;cAAA,OAAAmH,SAAA,CAAAjB,CAAA;YAAA;cAKAa,MAAA,CAAAtM,uBAAA;cACAsM,MAAA,CAAAhC,aAAA;cACAgC,MAAA,CAAArM,gBAAA;cAAAyM,SAAA,CAAAxB,CAAA;cAAA;YAAA;cAAAwB,SAAA,CAAAvB,CAAA;cAAAsB,GAAA,GAAAC,SAAA,CAAAtB,CAAA;cAEAkB,MAAA,CAAAd,QAAA,CAAAlI,KAAA;YAAA;cAAA,OAAAoJ,SAAA,CAAAjB,CAAA;UAAA;QAAA,GAAAc,QAAA;MAAA;IAEA;IAEA;IACAQ,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MAAA,WAAAtC,kBAAA,CAAAnN,OAAA,mBAAAoN,aAAA,CAAApN,OAAA,IAAAqN,CAAA,UAAAqC,SAAA;QAAA,IAAAvM,QAAA,EAAAsB,QAAA,EAAAqJ,IAAA,EAAA6B,UAAA,EAAAC,GAAA;QAAA,WAAAxC,aAAA,CAAApN,OAAA,IAAAyN,CAAA,WAAAoC,SAAA;UAAA,kBAAAA,SAAA,CAAAlC,CAAA;YAAA;cAAA,IACA8B,MAAA,CAAAL,gBAAA;gBAAAS,SAAA,CAAAlC,CAAA;gBAAA;cAAA;cAAA,OAAAkC,SAAA,CAAA3B,CAAA;YAAA;cAAA2B,SAAA,CAAAjC,CAAA;cAKAzK,QAAA,GAAAsM,MAAA,CAAAJ,aAAA;cAAA,MAEAI,MAAA,CAAA/M,gBAAA;gBAAAmN,SAAA,CAAAlC,CAAA;gBAAA;cAAA;cAAAkC,SAAA,CAAAlC,CAAA;cAAA,OAEA,IAAA/D,gCAAA,EAAAzG,QAAA;YAAA;cAAAsB,QAAA,GAAAoL,SAAA,CAAAhC,CAAA;cAAA,KACApJ,QAAA,CAAA8E,OAAA;gBAAAsG,SAAA,CAAAlC,CAAA;gBAAA;cAAA;cACA8B,MAAA,CAAAxB,QAAA,CAAA1E,OAAA;cACA;cAAAsG,SAAA,CAAAlC,CAAA;cAAA,OACA8B,MAAA,CAAA7C,iBAAA;YAAA;cAAAiD,SAAA,CAAAlC,CAAA;cAAA;YAAA;cAEA8B,MAAA,CAAAxB,QAAA,CAAAlI,KAAA,eAAAtB,QAAA,CAAAuD,GAAA;cAAA,OAAA6H,SAAA,CAAA3B,CAAA;YAAA;cAAA2B,SAAA,CAAAlC,CAAA;cAAA;YAAA;cAIA;cACAG,IAAA,GAAA2B,MAAA,CAAAjN,aAAA,CAAAiN,MAAA,CAAA/M,gBAAA;cAAAmN,SAAA,CAAAlC,CAAA;cAAA,OACA,IAAA4B,gCAAA,EAAAzB,IAAA,CAAA7I,EAAA,EAAA9B,QAAA;YAAA;cAAAsB,UAAA,GAAAoL,SAAA,CAAAhC,CAAA;cAAA,KAEApJ,UAAA,CAAA8E,OAAA;gBAAAsG,SAAA,CAAAlC,CAAA;gBAAA;cAAA;cACA8B,MAAA,CAAAxB,QAAA,CAAA1E,OAAA;cACA;cAAAsG,SAAA,CAAAlC,CAAA;cAAA,OACA8B,MAAA,CAAA7C,iBAAA;YAAA;cAAAiD,SAAA,CAAAlC,CAAA;cAAA;YAAA;cAEA8B,MAAA,CAAAxB,QAAA,CAAAlI,KAAA,eAAAtB,UAAA,CAAAuD,GAAA;cAAA,OAAA6H,SAAA,CAAA3B,CAAA;YAAA;cAKAuB,MAAA,CAAAhN,uBAAA;cACAgN,MAAA,CAAA1C,aAAA;cACA0C,MAAA,CAAA/M,gBAAA;cAAAmN,SAAA,CAAAlC,CAAA;cAAA;YAAA;cAAAkC,SAAA,CAAAjC,CAAA;cAAAgC,GAAA,GAAAC,SAAA,CAAAhC,CAAA;cAEA4B,MAAA,CAAAxB,QAAA,CAAAlI,KAAA;YAAA;cAAA,OAAA8J,SAAA,CAAA3B,CAAA;UAAA;QAAA,GAAAwB,QAAA;MAAA;IAEA;IAEA;IACAN,gBAAA,WAAAA,iBAAA;MACA,UAAAzM,QAAA,CAAAC,cAAA;QACA,KAAAqL,QAAA,CAAA6B,OAAA;QACA;MACA;MAEA,UAAAnN,QAAA,CAAA1C,IAAA,UAAA0C,QAAA,CAAA1C,IAAA,CAAA2D,IAAA;QACA,KAAAqK,QAAA,CAAA6B,OAAA;QACA;MACA;MAEA,UAAAnN,QAAA,CAAAE,WAAA,UAAAF,QAAA,CAAAE,WAAA,CAAAe,IAAA;QACA,KAAAqK,QAAA,CAAA6B,OAAA;QACA;MACA;;MAEA;MACA,SAAAnN,QAAA,CAAAP,SAAA;QACA,UAAAO,QAAA,CAAAI,iBAAA;UACA,KAAAkL,QAAA,CAAA6B,OAAA;UACA;QACA;QACA;QACA,IAAArB,WAAA,OAAAzF,IAAA,MAAArG,QAAA,CAAAI,iBAAA;QACA,IAAAgN,GAAA,OAAA/G,IAAA;QACA,IAAAyF,WAAA,IAAAsB,GAAA;UACA,KAAA9B,QAAA,CAAA6B,OAAA;UACA;QACA;MACA;QACA,UAAAnN,QAAA,CAAAG,YAAA;UACA,KAAAmL,QAAA,CAAA6B,OAAA;UACA;QACA;MACA;MAEA;IACA;IAEA;IACAT,aAAA,WAAAA,cAAA;MACA;MACA,IAAAZ,WAAA;MACA,SAAA9L,QAAA,CAAAP,SAAA;QACA;QACA,SAAAO,QAAA,CAAAI,iBAAA;UACA,IAAAiN,QAAA,OAAAhH,IAAA,MAAArG,QAAA,CAAAI,iBAAA;UACA0L,WAAA,MAAAtG,MAAA,CAAA6H,QAAA,CAAAC,QAAA,GAAAC,QAAA,GAAAC,QAAA,eAAAhI,MAAA,CAAA6H,QAAA,CAAAI,UAAA,GAAAF,QAAA,GAAAC,QAAA;QACA;UACA1B,WAAA;QACA;MACA;QACA;QACAA,WAAA,QAAA9L,QAAA,CAAAG,YAAA;MACA;MAEA,IAAAK,QAAA;QACAP,cAAA,OAAAD,QAAA,CAAAC,cAAA;QACAiB,SAAA,OAAAlB,QAAA,CAAA1C,IAAA;QACAoQ,SAAA;QACAjO,SAAA,OAAAO,QAAA,CAAAP,SAAA;QAAA;QACAU,YAAA,EAAA2L,WAAA;QAAA;QACA3K,gBAAA,OAAAnB,QAAA,CAAAE,WAAA;QACAnB,QAAA,OAAAiB,QAAA,CAAAjB,QAAA;MACA;;MAEA;MACA,SAAAiB,QAAA,CAAAP,SAAA,oBAAAO,QAAA,CAAAI,iBAAA;QACAI,QAAA,wBAAAR,QAAA,CAAAI,iBAAA,CAAAuN,KAAA;MACA;MAEA,OAAAnN,QAAA;IACA;IAEA;IACAoN,UAAA,WAAAA,WAAAC,KAAA;MAAA,IAAAC,OAAA;MAAA,WAAAtD,kBAAA,CAAAnN,OAAA,mBAAAoN,aAAA,CAAApN,OAAA,IAAAqN,CAAA,UAAAqD,SAAA;QAAA,IAAA5C,IAAA,EAAArJ,QAAA,EAAAkM,GAAA;QAAA,WAAAvD,aAAA,CAAApN,OAAA,IAAAyN,CAAA,WAAAmD,SAAA;UAAA,kBAAAA,SAAA,CAAAjD,CAAA;YAAA;cAAAiD,SAAA,CAAAhD,CAAA;cAEAE,IAAA,GAAA2C,OAAA,CAAAjO,aAAA,CAAAgO,KAAA;cAAA,IAEA1C,IAAA;gBAAA8C,SAAA,CAAAjD,CAAA;gBAAA;cAAA;cACA8C,OAAA,CAAAxC,QAAA,CAAAlI,KAAA;cAAA,OAAA6K,SAAA,CAAA1C,CAAA;YAAA;cAAA0C,SAAA,CAAAjD,CAAA;cAAA,OAIA8C,OAAA,CAAAI,QAAA,oDAAA1I,MAAA,CAAA2F,IAAA,CAAA7N,IAAA;gBACA6Q,iBAAA;gBACAC,gBAAA;gBACA1F,IAAA;cACA;YAAA;cAAAuF,SAAA,CAAAjD,CAAA;cAAA,OAEA,IAAAqD,gCAAA,EAAAlD,IAAA,CAAA7I,EAAA;YAAA;cAAAR,QAAA,GAAAmM,SAAA,CAAA/C,CAAA;cAAA,KAEApJ,QAAA,CAAA8E,OAAA;gBAAAqH,SAAA,CAAAjD,CAAA;gBAAA;cAAA;cACA8C,OAAA,CAAAxC,QAAA,CAAA1E,OAAA;cACA;cAAAqH,SAAA,CAAAjD,CAAA;cAAA,OACA8C,OAAA,CAAA7D,iBAAA;YAAA;cAAAgE,SAAA,CAAAjD,CAAA;cAAA;YAAA;cAEA8C,OAAA,CAAAxC,QAAA,CAAAlI,KAAA,YAAAtB,QAAA,CAAAuD,GAAA,IAAAvD,QAAA,CAAA3C,OAAA;YAAA;cAAA8O,SAAA,CAAAjD,CAAA;cAAA;YAAA;cAAAiD,SAAA,CAAAhD,CAAA;cAAA+C,GAAA,GAAAC,SAAA,CAAA/C,CAAA;cAAA,MAGA8C,GAAA;gBAAAC,SAAA,CAAAjD,CAAA;gBAAA;cAAA;cAAA,OAAAiD,SAAA,CAAA1C,CAAA;YAAA;cAGAuC,OAAA,CAAAxC,QAAA,CAAAlI,KAAA;YAAA;cAAA,OAAA6K,SAAA,CAAA1C,CAAA;UAAA;QAAA,GAAAwC,QAAA;MAAA;IAEA;IAEA;IACAO,QAAA,WAAAA,SAAAT,KAAA;MACA,IAAA1C,IAAA,QAAAtL,aAAA,CAAAgO,KAAA;MAEA,KAAA1C,IAAA;QACA,KAAAG,QAAA,CAAAlI,KAAA;QACA;MACA;MAEA,KAAArD,gBAAA,GAAA8N,KAAA;MACA,KAAA7N,QAAA;QACAC,cAAA,EAAAkL,IAAA,CAAAU,aAAA;QACAvO,IAAA,EAAA6N,IAAA,CAAA7N,IAAA;QACA4C,WAAA,EAAAiL,IAAA,CAAAjL,WAAA;QACAC,YAAA,EAAAgL,IAAA,CAAAW,WAAA;QACA1L,iBAAA,EAAA+K,IAAA,CAAAY,eAAA;QACAtM,SAAA,EAAA0L,IAAA,CAAA1L,SAAA;QACAV,QAAA,EAAAoM,IAAA,CAAAc,OAAA;MACA;MAEA,KAAA5B,mBAAA;MACA,KAAAvK,uBAAA;IACA;IAEA;IACAyO,gBAAA,WAAAA,iBAAAV,KAAA;MAAA,IAAAW,OAAA;MAAA,WAAAhE,kBAAA,CAAAnN,OAAA,mBAAAoN,aAAA,CAAApN,OAAA,IAAAqN,CAAA,UAAA+D,SAAA;QAAA,IAAAtD,IAAA,EAAAuD,SAAA,EAAA5M,QAAA,EAAA6M,GAAA;QAAA,WAAAlE,aAAA,CAAApN,OAAA,IAAAyN,CAAA,WAAA8D,SAAA;UAAA,kBAAAA,SAAA,CAAA5D,CAAA;YAAA;cAAA4D,SAAA,CAAA3D,CAAA;cAEAE,IAAA,GAAAqD,OAAA,CAAA3O,aAAA,CAAAgO,KAAA;cACAa,SAAA,GAAAvD,IAAA,CAAA7J,MAAA;cAAAsN,SAAA,CAAA5D,CAAA;cAAA,OAEA,IAAA2B,iCAAA,EAAAxB,IAAA,CAAA7I,EAAA,EAAAoM,SAAA;YAAA;cAAA5M,QAAA,GAAA8M,SAAA,CAAA1D,CAAA;cAAA,KAEApJ,QAAA,CAAA8E,OAAA;gBAAAgI,SAAA,CAAA5D,CAAA;gBAAA;cAAA;cACA,IAAA0D,SAAA;gBACAF,OAAA,CAAAlD,QAAA,CAAA1E,OAAA,sBAAApB,MAAA,CAAA2F,IAAA,CAAA7N,IAAA;cACA;gBACAkR,OAAA,CAAAlD,QAAA,CAAAuD,IAAA,sBAAArJ,MAAA,CAAA2F,IAAA,CAAA7N,IAAA;cACA;cACA;cAAAsR,SAAA,CAAA5D,CAAA;cAAA,OACAwD,OAAA,CAAAvE,iBAAA;YAAA;cAAA2E,SAAA,CAAA5D,CAAA;cAAA;YAAA;cAEAwD,OAAA,CAAAlD,QAAA,CAAAlI,KAAA,aAAAtB,QAAA,CAAAuD,GAAA;YAAA;cAAAuJ,SAAA,CAAA5D,CAAA;cAAA;YAAA;cAAA4D,SAAA,CAAA3D,CAAA;cAAA0D,GAAA,GAAAC,SAAA,CAAA1D,CAAA;cAGAsD,OAAA,CAAAlD,QAAA,CAAAlI,KAAA;YAAA;cAAA,OAAAwL,SAAA,CAAArD,CAAA;UAAA;QAAA,GAAAkD,QAAA;MAAA;IAEA;IAEA;IACAK,WAAA,WAAAA,YAAAjB,KAAA;MACA,IAAA1C,IAAA,QAAAtL,aAAA,CAAAgO,KAAA;MACA,KAAA1C,IAAA;QACA,KAAAG,QAAA,CAAAlI,KAAA;QACA;MACA;MACA,KAAA9C,iBAAA,CAAAE,QAAA,GAAA2K,IAAA;MACA,KAAA7K,iBAAA,CAAAC,OAAA;IACA;IAEA;IACAwO,mBAAA,WAAAA,oBAAA5D,IAAA;MACA,KAAAA,IAAA;MAEA,IAAAA,IAAA,CAAA1L,SAAA;QACA;QACA,6BAAA+F,MAAA,CAAA2F,IAAA,CAAAY,eAAA,IAAAZ,IAAA,CAAAW,WAAA;MACA;QACA;QACA,IAAAkD,aAAA,QAAAC,gBAAA,CAAA9D,IAAA,CAAA1L,SAAA;QACA,UAAA+F,MAAA,CAAAwJ,aAAA,OAAAxJ,MAAA,CAAA2F,IAAA,CAAAW,WAAA;MACA;IACA;IAEA;IACAmD,gBAAA,WAAAA,iBAAAxP,SAAA;MACA,IAAAyP,YAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,YAAA,CAAAzP,SAAA,KAAAA,SAAA;IACA;IAEA;IACA0P,UAAA,WAAAA,WAAA;MACA;MACA,KAAA7D,QAAA,CAAAuD,IAAA;IACA;IAEA;IACAO,SAAA,EAAAA;EACA;AACA", "ignoreList": []}]}