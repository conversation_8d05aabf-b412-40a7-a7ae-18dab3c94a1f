{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\analyze-record\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\analyze-record\\index.vue", "mtime": 1753341129010}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\babel.config.js", "mtime": 1750933247176}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753065270161}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753065271554}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753065270161}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753065272328}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9Vc2Vycy93eGwvRGVza3RvcC90aGluL3RoaW5rdGFua3VpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5kZWZhdWx0ID0gdm9pZCAwOwp2YXIgX3JlZ2VuZXJhdG9yMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQzovVXNlcnMvd3hsL0Rlc2t0b3AvdGhpbi90aGlua3Rhbmt1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9yZWdlbmVyYXRvci5qcyIpKTsKdmFyIF9hc3luY1RvR2VuZXJhdG9yMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQzovVXNlcnMvd3hsL0Rlc2t0b3AvdGhpbi90aGlua3Rhbmt1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9hc3luY1RvR2VuZXJhdG9yLmpzIikpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3ltYm9sLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zeW1ib2wuZGVzY3JpcHRpb24uanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmNvbmNhdC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmlsdGVyLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5pbmNsdWRlcy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuam9pbi5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkubWFwLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5zbGljZS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuZnVuY3Rpb24ubmFtZS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuanNvbi5zdHJpbmdpZnkuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC50by1zdHJpbmcuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5pbmNsdWRlcy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLml0ZXJhdG9yLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcucGFkLXN0YXJ0LmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcudHJpbS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuZmlsdGVyLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IubWFwLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLml0ZXJhdG9yLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy93ZWIudXJsLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy93ZWIudXJsLnRvLWpzb24uanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL3dlYi51cmwtc2VhcmNoLXBhcmFtcy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvd2ViLnVybC1zZWFyY2gtcGFyYW1zLmRlbGV0ZS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvd2ViLnVybC1zZWFyY2gtcGFyYW1zLmhhcy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvd2ViLnVybC1zZWFyY2gtcGFyYW1zLnNpemUuanMiKTsKdmFyIF9vcGluaW9uQW5hbHlzaXMgPSByZXF1aXJlKCJAL2FwaS9vcGluaW9uLWFuYWx5c2lzIik7CnZhciBfcnVveWkgPSByZXF1aXJlKCJAL3V0aWxzL3J1b3lpIik7CnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAnQW5hbHl6ZVJlY29yZCcsCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDliIbmnpDorrDlvZXooajmoLzmlbDmja4KICAgICAgcmVjb3JkTGlzdDogW10sCiAgICAgIC8vIOaXpeacn+iMg+WbtAogICAgICBkYXRlUmFuZ2U6IFtdLAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlX251bTogMSwKICAgICAgICBwYWdlX3NpemU6IDEwLAogICAgICAgIHJlcXVpcmVtZW50TmFtZTogbnVsbCwKICAgICAgICBlbnRpdHlLZXl3b3JkOiBudWxsLAogICAgICAgIGFuYWx5c2lzU3RhdHVzOiBudWxsCiAgICAgIH0sCiAgICAgIC8vIOWIhuaekOaXpeW/l+WvueivneahhgogICAgICBsb2dEaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgbG9nTG9hZGluZzogZmFsc2UsCiAgICAgIGxvZ0xpc3Q6IFtdLAogICAgICAvLyDmn6XnnIvmiqXlkYrlr7nor53moYYKICAgICAgcmVwb3J0RGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIHJlcG9ydExvYWRpbmc6IGZhbHNlLAogICAgICByZXBvcnREYXRhOiBudWxsLAogICAgICAvLyDnq4vljbPmjqjpgIHlr7nor53moYYKICAgICAgcHVzaERpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBwdXNoTG9hZGluZzogZmFsc2UsCiAgICAgIHB1c2hGb3JtOiB7CiAgICAgICAgcHVzaF91cmw6ICcnLAogICAgICAgIHB1c2hfY29udGVudDogJycKICAgICAgfSwKICAgICAgcHVzaFJ1bGVzOiB7CiAgICAgICAgcHVzaF91cmw6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICfmjqjpgIHlnLDlnYDkuI3og73kuLrnqbonLAogICAgICAgICAgdHJpZ2dlcjogJ2JsdXInCiAgICAgICAgfV0sCiAgICAgICAgcHVzaF9jb250ZW50OiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn5o6o6YCB5YaF5a655LiN6IO95Li656m6JywKICAgICAgICAgIHRyaWdnZXI6ICdibHVyJwogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIC8vIOWumuaXtuaOqOmAgeWvueivneahhgogICAgICBzY2hlZHVsZVB1c2hEaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgc2NoZWR1bGVQdXNoTG9hZGluZzogZmFsc2UsCiAgICAgIHNjaGVkdWxlUHVzaEZvcm06IHsKICAgICAgICBwdXNoX3VybDogJycsCiAgICAgICAgcHVzaF90aW1lOiAnJywKICAgICAgICBmcmVxdWVuY3k6ICdvbmNlJywKICAgICAgICBwdXNoX2NvbnRlbnQ6ICcnCiAgICAgIH0sCiAgICAgIHNjaGVkdWxlUHVzaFJ1bGVzOiB7CiAgICAgICAgcHVzaF91cmw6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICfmjqjpgIHlnLDlnYDkuI3og73kuLrnqbonLAogICAgICAgICAgdHJpZ2dlcjogJ2JsdXInCiAgICAgICAgfV0sCiAgICAgICAgcHVzaF90aW1lOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn5o6o6YCB5pe26Ze05LiN6IO95Li656m6JywKICAgICAgICAgIHRyaWdnZXI6ICdjaGFuZ2UnCiAgICAgICAgfV0sCiAgICAgICAgcHVzaF9jb250ZW50OiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn5o6o6YCB5YaF5a655LiN6IO95Li656m6JywKICAgICAgICAgIHRyaWdnZXI6ICdibHVyJwogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIC8vIOW9k+WJjeaTjeS9nOeahOiusOW9lQogICAgICBjdXJyZW50UmVjb3JkOiBudWxsLAogICAgICAvLyDlrprml7bku7vliqHnm7jlhbPmlbDmja4KICAgICAgdGltZWRUYXNrRGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIC8vIOWumuaXtuS7u+WKoeaKveWxieaYvuekuueKtuaAgQogICAgICB0aW1lZFRhc2tMaXN0OiBbXSwKICAgICAgLy8g5a6a5pe25Lu75Yqh5YiX6KGoCiAgICAgIGNyZWF0ZVRhc2tEaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgLy8g5Yib5bu65Lu75Yqh5by556qX5pi+56S654q25oCBCiAgICAgIGVkaXRpbmdUYXNrSW5kZXg6IC0xLAogICAgICAvLyDlvZPliY3nvJbovpHnmoTku7vliqHntKLlvJXvvIwtMeihqOekuuaWsOW7uuS7u+WKoQogICAgICB0YXNrRm9ybTogewogICAgICAgIHJlcXVpcmVtZW50X2lkOiAnJywKICAgICAgICAvLyDpnIDmsYJJRAogICAgICAgIG5hbWU6ICcnLAogICAgICAgIC8vIOS7u+WKoeWQjeensAogICAgICAgIGRlc2NyaXB0aW9uOiAnJywKICAgICAgICBleGVjdXRlX3RpbWU6ICcxNjowMCcsCiAgICAgICAgZXhlY3V0ZV9kYXRlX3RpbWU6ICcnLAogICAgICAgIC8vIOS4gOasoeaAp+S7u+WKoeeahOaJp+ihjOaXpeacn+aXtumXtAogICAgICAgIGZyZXF1ZW5jeTogJ2RhaWx5JywKICAgICAgICBwdXNoX3VybDogJycgLy8g5o6o6YCB5Zyw5Z2ACiAgICAgIH0sCiAgICAgIHJlcXVpcmVtZW50TGlzdDogW10sCiAgICAgIC8vIOmcgOaxguWIl+ihqAogICAgICAvLyDku7vliqHpooTop4jlvLnnqpfnirbmgIEKICAgICAgdGFza1ByZXZpZXdEaWFsb2c6IHsKICAgICAgICB2aXNpYmxlOiBmYWxzZSwKICAgICAgICB0YXNrRGF0YTogbnVsbCwKICAgICAgICBsb2FkaW5nOiBmYWxzZQogICAgICB9CiAgICB9OwogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIC8vIOWPqumHjee9ruaXpeacn+iMg+WbtO+8jOS/neeVmeWFtuS7luafpeivouWPguaVsAogICAgdGhpcy5kYXRlUmFuZ2UgPSBbXTsKICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgLy8g5rOo6YeK5o6J6aG16Z2i5Yqg6L295pe255qE5a6a5pe25Lu75Yqh5YiX6KGo5Yqg6L2977yM5pS55Li654K55Ye75a6a5pe25o6o6YCB5oyJ6ZKu5pe25omN5Yqg6L29CiAgICAvLyB0aGlzLmxvYWRUaW1lZFRhc2tMaXN0KCkKICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDmn6Xor6LliIbmnpDorrDlvZXliJfooaggKi9nZXRMaXN0OiBmdW5jdGlvbiBnZXRMaXN0KHBhZ2luYXRpb25QYXJhbXMpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKCiAgICAgIC8vIOWkhOeQhuWIhumhteWPguaVsAogICAgICBpZiAocGFnaW5hdGlvblBhcmFtcykgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZV9udW0gPSBwYWdpbmF0aW9uUGFyYW1zLnBhZ2U7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlX3NpemUgPSBwYWdpbmF0aW9uUGFyYW1zLmxpbWl0OwogICAgICB9CiAgICAgIHZhciBwYXJhbXMgPSB7CiAgICAgICAgcGFnZV9udW06IHRoaXMucXVlcnlQYXJhbXMucGFnZV9udW0sCiAgICAgICAgcGFnZV9zaXplOiB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2Vfc2l6ZQogICAgICB9OwoKICAgICAgLy8g5re75Yqg5pCc57Si5p2h5Lu277yI5q2j56Gu5pig5bCE5Yiw5Lu75Yqh6KGo5a2X5q6177yJCiAgICAgIGlmICh0aGlzLnF1ZXJ5UGFyYW1zLnJlcXVpcmVtZW50TmFtZSAmJiB0aGlzLnF1ZXJ5UGFyYW1zLnJlcXVpcmVtZW50TmFtZS50cmltKCkgIT09ICcnKSB7CiAgICAgICAgcGFyYW1zLnRhc2tfbmFtZSA9IHRoaXMucXVlcnlQYXJhbXMucmVxdWlyZW1lbnROYW1lLnRyaW0oKTsgLy8g5pig5bCE5Yiw5Lu75Yqh5ZCN56ewCiAgICAgIH0KICAgICAgaWYgKHRoaXMucXVlcnlQYXJhbXMuZW50aXR5S2V5d29yZCAmJiB0aGlzLnF1ZXJ5UGFyYW1zLmVudGl0eUtleXdvcmQudHJpbSgpICE9PSAnJykgewogICAgICAgIHBhcmFtcy50YXNrX2Rlc2NyaXB0aW9uID0gdGhpcy5xdWVyeVBhcmFtcy5lbnRpdHlLZXl3b3JkLnRyaW0oKTsgLy8g5pig5bCE5Yiw5Lu75Yqh5o+P6L+w77yI5YyF5ZCr5YWz6ZSu6K+N77yJCiAgICAgIH0KICAgICAgaWYgKHRoaXMucXVlcnlQYXJhbXMuYW5hbHlzaXNTdGF0dXMgIT09IG51bGwgJiYgdGhpcy5xdWVyeVBhcmFtcy5hbmFseXNpc1N0YXR1cyAhPT0gdW5kZWZpbmVkICYmIHRoaXMucXVlcnlQYXJhbXMuYW5hbHlzaXNTdGF0dXMgIT09ICcnKSB7CiAgICAgICAgLy8g5bCG5YmN56uv54q25oCB5pig5bCE5Zue5Lu75Yqh54q25oCBCiAgICAgICAgdmFyIHN0YXR1c01hcCA9IHsKICAgICAgICAgIDA6ICdwZW5kaW5nJywKICAgICAgICAgIDE6ICdydW5uaW5nJywKICAgICAgICAgIDI6ICdjb21wbGV0ZWQnLAogICAgICAgICAgMzogJ2ZhaWxlZCcKICAgICAgICB9OwogICAgICAgIHBhcmFtcy5zdGF0dXMgPSBzdGF0dXNNYXBbdGhpcy5xdWVyeVBhcmFtcy5hbmFseXNpc1N0YXR1c10gfHwgJ3BlbmRpbmcnOwogICAgICB9CgogICAgICAvLyDmt7vliqDml7bpl7TojIPlm7QKICAgICAgaWYgKHRoaXMuZGF0ZVJhbmdlICYmIHRoaXMuZGF0ZVJhbmdlLmxlbmd0aCA9PT0gMikgewogICAgICAgIHBhcmFtcy5jcmVhdGVfdGltZV9zdGFydCA9IHRoaXMuZGF0ZVJhbmdlWzBdOwogICAgICAgIHBhcmFtcy5jcmVhdGVfdGltZV9lbmQgPSB0aGlzLmRhdGVSYW5nZVsxXTsKICAgICAgICBjb25zb2xlLmxvZygn8J+UjSDmt7vliqDml7bpl7TojIPlm7TnrZvpgIk6JywgdGhpcy5kYXRlUmFuZ2UpOwogICAgICB9CgogICAgICAvLyDosIPnlKjnnJ/lrp5BUEnojrflj5bku7vliqHliJfooajmlbDmja4KICAgICAgY29uc29sZS5sb2coJ/CflI0g5Y+R6YCBQVBJ6K+35rGC77yM5Y+C5pWwOicsIHBhcmFtcyk7CiAgICAgICgwLCBfb3BpbmlvbkFuYWx5c2lzLmdldEFuYWx5c2lzVGFza0xpc3QpKHBhcmFtcykudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBBUEnlk43lupQ6JywgcmVzcG9uc2UpOwogICAgICAgIC8vIOWkhOeQhuWTjeW6lOaVsOaNru+8jOWwhuaVsOaNruW6k+Wtl+auteaYoOWwhOWIsOWJjeerr+aYvuekuuWtl+autQoKICAgICAgICAvLyDpgILphY3mlrDnmoTlk43lupTmoLzlvI/vvJrkvb/nlKggbW9kZWxfY29udGVudCDlkI7vvIxQYWdlUmVzcG9uc2VNb2RlbCDkvJrnm7TmjqXlkIjlubbliLDlk43lupTmoLnnuqfliKsKICAgICAgICB2YXIgcmVxdWlyZW1lbnRzID0gW107CiAgICAgICAgaWYgKHJlc3BvbnNlLnJlY29yZHMpIHsKICAgICAgICAgIC8vIOaWsOagvOW8j++8mm1vZGVsX2NvbnRlbnQg5L2/IFBhZ2VSZXNwb25zZU1vZGVsIOebtOaOpeWQiOW5tuWIsOWTjeW6lOaguee6p+WIqwogICAgICAgICAgcmVxdWlyZW1lbnRzID0gcmVzcG9uc2UucmVjb3JkczsKICAgICAgICB9IGVsc2UgaWYgKHJlc3BvbnNlLmRhdGEgJiYgcmVzcG9uc2UuZGF0YS5yZWNvcmRzKSB7CiAgICAgICAgICAvLyDlpIfnlKjmoLzlvI/vvJpQYWdlUmVzcG9uc2VNb2RlbCDlnKggZGF0YSDlrZfmrrXkuK0KICAgICAgICAgIHJlcXVpcmVtZW50cyA9IHJlc3BvbnNlLmRhdGEucmVjb3JkczsKICAgICAgICB9IGVsc2UgaWYgKHJlc3BvbnNlLmRhdGEgJiYgcmVzcG9uc2UuZGF0YS5yb3dzKSB7CiAgICAgICAgICAvLyDml6fnmoTliIbpobXmlbDmja7moLzlvI8KICAgICAgICAgIHJlcXVpcmVtZW50cyA9IHJlc3BvbnNlLmRhdGEucm93czsKICAgICAgICB9IGVsc2UgaWYgKHJlc3BvbnNlLmRhdGEgJiYgQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhKSkgewogICAgICAgICAgLy8g55u05o6l5pWw57uE5qC85byPCiAgICAgICAgICByZXF1aXJlbWVudHMgPSByZXNwb25zZS5kYXRhOwogICAgICAgIH0gZWxzZSBpZiAocmVzcG9uc2Uucm93cykgewogICAgICAgICAgLy8g5YW85a655pen5qC85byPCiAgICAgICAgICByZXF1aXJlbWVudHMgPSByZXNwb25zZS5yb3dzOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICByZXF1aXJlbWVudHMgPSBbXTsKICAgICAgICB9CiAgICAgICAgX3RoaXMucmVjb3JkTGlzdCA9IHJlcXVpcmVtZW50cy5maWx0ZXIoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgIHJldHVybiBpdGVtLmlkICE9IG51bGwgJiYgaXRlbS5pZCAhPT0gdW5kZWZpbmVkICYmIGl0ZW0uaWQgIT09ICcnOwogICAgICAgIH0pIC8vIOi/h+a7pOaOieaXoOaViGlk55qE5pWw5o2uCiAgICAgICAgLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgLy8g56Gu5L+dSUTmmK/mnInmlYjnmoTmlbTmlbAKICAgICAgICAgIHZhciBpZCA9IHBhcnNlSW50KGl0ZW0uaWQpOwogICAgICAgICAgaWYgKGlzTmFOKGlkKSkgewogICAgICAgICAgICByZXR1cm4gbnVsbDsKICAgICAgICAgIH0KICAgICAgICAgIHZhciBtYXBwZWRJdGVtID0gewogICAgICAgICAgICBpZDogaWQsCiAgICAgICAgICAgIC8vIOehruS/nWlk5piv5pW05pWwCiAgICAgICAgICAgIHJlcXVpcmVtZW50TmFtZTogaXRlbS50YXNrX25hbWUgfHwgaXRlbS50YXNrTmFtZSB8fCAnJywKICAgICAgICAgICAgLy8g5Lu75Yqh5ZCN56ewCiAgICAgICAgICAgIGVudGl0eUtleXdvcmQ6IGl0ZW0udGFza19kZXNjcmlwdGlvbiB8fCBpdGVtLnRhc2tEZXNjcmlwdGlvbiB8fCAnJywKICAgICAgICAgICAgLy8g5Lu75Yqh5o+P6L+w5L2c5Li65YWz6ZSu6K+N5pi+56S6CiAgICAgICAgICAgIHNwZWNpZmljUmVxdWlyZW1lbnQ6IGl0ZW0udGFza19kZXNjcmlwdGlvbiB8fCBpdGVtLnRhc2tEZXNjcmlwdGlvbiB8fCAnJywKICAgICAgICAgICAgLy8g5Lu75Yqh5o+P6L+wCiAgICAgICAgICAgIGNyZWF0ZVRpbWU6IGl0ZW0uY3JlYXRlX3RpbWUgfHwgaXRlbS5jcmVhdGVUaW1lIHx8ICcnLAogICAgICAgICAgICAvLyDlsIbku7vliqHnirbmgIHmmKDlsITliLDliY3nq6/nmoRzdGF0dXPlrZfmrrUKICAgICAgICAgICAgLy8gcGVuZGluZy3lvoXmiafooYwtPjDlvoXnlJ/miJAsIHJ1bm5pbmct5omn6KGM5LitLT4x55Sf5oiQ5LitLCBjb21wbGV0ZWQt5bey5a6M5oiQLT4y5bey55Sf5oiQLCBmYWlsZWQt5aSx6LSlLT4tMeeUn+aIkOWksei0pQogICAgICAgICAgICBzdGF0dXM6IF90aGlzLm1hcFRhc2tTdGF0dXMoaXRlbS5zdGF0dXMgfHwgJ3BlbmRpbmcnKSwKICAgICAgICAgICAgLy8g5re75Yqg5oql5ZGKT1NTIFVSTOWtl+autQogICAgICAgICAgICByZXBvcnRPc3NVcmw6IGl0ZW0ucmVwb3J0X29zc191cmwgfHwgaXRlbS5yZXBvcnRPc3NVcmwgfHwgbnVsbAogICAgICAgICAgfTsKICAgICAgICAgIHJldHVybiBtYXBwZWRJdGVtOwogICAgICAgIH0pLmZpbHRlcihmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgcmV0dXJuIGl0ZW0gIT09IG51bGw7CiAgICAgICAgfSk7IC8vIOi/h+a7pOaOieaYoOWwhOWksei0peeahOmhuQoKICAgICAgICAvLyDpgILphY3mlrDnmoTmgLvorrDlvZXmlbDmoLzlvI/vvJrkvb/nlKggbW9kZWxfY29udGVudCDlkI7vvIx0b3RhbCDkvJrnm7TmjqXlnKjlk43lupTmoLnnuqfliKsKICAgICAgICBpZiAocmVzcG9uc2UudG90YWwgIT09IHVuZGVmaW5lZCkgewogICAgICAgICAgX3RoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsKICAgICAgICB9IGVsc2UgaWYgKHJlc3BvbnNlLmRhdGEgJiYgcmVzcG9uc2UuZGF0YS50b3RhbCAhPT0gdW5kZWZpbmVkKSB7CiAgICAgICAgICBfdGhpcy50b3RhbCA9IHJlc3BvbnNlLmRhdGEudG90YWw7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIF90aGlzLnRvdGFsID0gMDsKICAgICAgICB9CiAgICAgICAgX3RoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoZXJyb3IpIHsKICAgICAgICBfdGhpcy4kbW9kYWwubXNnRXJyb3IoJ+iOt+WPluWIhuaekOiusOW9leWIl+ihqOWksei0pTogJyArIChlcnJvci5tZXNzYWdlIHx8ICfmnKrnn6XplJnor68nKSk7CiAgICAgICAgX3RoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5pig5bCE5Lu75Yqh54q25oCBICovbWFwVGFza1N0YXR1czogZnVuY3Rpb24gbWFwVGFza1N0YXR1cyh0YXNrU3RhdHVzKSB7CiAgICAgIC8vIOaVsOaNruW6k3Rhc2sgc3RhdHVzOiBwZW5kaW5nLeW+heaJp+ihjO+8jHJ1bm5pbmct5omn6KGM5Lit77yMY29tcGxldGVkLeW3suWujOaIkO+8jGZhaWxlZC3lpLHotKXvvIxwYXVzZWQt5pqC5YGcCiAgICAgIC8vIOWJjeerr3N0YXR1czogMC3lvoXnlJ/miJDvvIwxLeeUn+aIkOS4re+8jDIt5bey55Sf5oiQ77yMLTEt55Sf5oiQ5aSx6LSlCiAgICAgIHZhciBzdGF0dXNNYXAgPSB7CiAgICAgICAgJ3BlbmRpbmcnOiAwLAogICAgICAgIC8vIOW+heaJp+ihjCAtPiDlvoXnlJ/miJAKICAgICAgICAncnVubmluZyc6IDEsCiAgICAgICAgLy8g5omn6KGM5LitIC0+IOeUn+aIkOS4rQogICAgICAgICdjb21wbGV0ZWQnOiAyLAogICAgICAgIC8vIOW3suWujOaIkCAtPiDlt7LnlJ/miJAKICAgICAgICAnZmFpbGVkJzogLTEsCiAgICAgICAgLy8g5aSx6LSlIC0+IOeUn+aIkOWksei0pQogICAgICAgICdwYXVzZWQnOiAwIC8vIOaaguWBnCAtPiDlvoXnlJ/miJAKICAgICAgfTsKICAgICAgcmV0dXJuIHN0YXR1c01hcFt0YXNrU3RhdHVzXSAhPT0gdW5kZWZpbmVkID8gc3RhdHVzTWFwW3Rhc2tTdGF0dXNdIDogMDsKICAgIH0sCiAgICAvKiog5pig5bCE5YiG5p6Q54q25oCB77yI5L+d55WZ5YW85a655oCn77yJICovbWFwQW5hbHlzaXNTdGF0dXM6IGZ1bmN0aW9uIG1hcEFuYWx5c2lzU3RhdHVzKGFuYWx5c2lzU3RhdHVzKSB7CiAgICAgIC8vIOaVsOaNruW6k2FuYWx5c2lzX3N0YXR1czogMC3mnKrlvIDlp4vvvIwxLeWIhuaekOS4re+8jDIt5bey5a6M5oiQ77yMMy3lpLHotKUKICAgICAgLy8g5YmN56uvc3RhdHVzOiAwLeW+heeUn+aIkO+8jDEt55Sf5oiQ5Lit77yMMi3lt7LnlJ/miJDvvIwtMS3nlJ/miJDlpLHotKUKICAgICAgdmFyIHN0YXR1c01hcCA9IHsKICAgICAgICAwOiAwLAogICAgICAgIC8vIOacquW8gOWniyAtPiDlvoXnlJ/miJAKICAgICAgICAxOiAxLAogICAgICAgIC8vIOWIhuaekOS4rSAtPiDnlJ/miJDkuK0KICAgICAgICAyOiAyLAogICAgICAgIC8vIOW3suWujOaIkCAtPiDlt7LnlJ/miJAKICAgICAgICAzOiAtMSAvLyDlpLHotKUgLT4g55Sf5oiQ5aSx6LSlCiAgICAgIH07CiAgICAgIHJldHVybiBzdGF0dXNNYXBbYW5hbHlzaXNTdGF0dXNdICE9PSB1bmRlZmluZWQgPyBzdGF0dXNNYXBbYW5hbHlzaXNTdGF0dXNdIDogMDsKICAgIH0sCiAgICAvKiog6ZyA5rGC5ZCN56ew6L6T5YWl55uR5ZCsICovb25SZXF1aXJlbWVudE5hbWVJbnB1dDogZnVuY3Rpb24gb25SZXF1aXJlbWVudE5hbWVJbnB1dCh2YWx1ZSkgewogICAgICBjb25zb2xlLmxvZygn8J+UjSDpnIDmsYLlkI3np7DovpPlhaXlj5jljJY6JywgdmFsdWUpOwogICAgICAvLyDlj6/ku6Xmt7vliqDpmLLmipbpgLvovpHvvIzpgb/lhY3popHnuYHmn6Xor6IKICAgICAgLy8gdGhpcy5kZWJvdW5jZVNlYXJjaCgpCiAgICB9LAogICAgLyoqIOWFs+mUruivjei+k+WFpeebkeWQrCAqL29uRW50aXR5S2V5d29yZElucHV0OiBmdW5jdGlvbiBvbkVudGl0eUtleXdvcmRJbnB1dCh2YWx1ZSkgewogICAgICBjb25zb2xlLmxvZygn8J+UjSDlhbPplK7or43ovpPlhaXlj5jljJY6JywgdmFsdWUpOwogICAgICAvLyDlj6/ku6Xmt7vliqDpmLLmipbpgLvovpHvvIzpgb/lhY3popHnuYHmn6Xor6IKICAgICAgLy8gdGhpcy5kZWJvdW5jZVNlYXJjaCgpCiAgICB9LAogICAgLyoqIOWIhuaekOeKtuaAgeWPmOWMluebkeWQrCAqL29uQW5hbHlzaXNTdGF0dXNDaGFuZ2U6IGZ1bmN0aW9uIG9uQW5hbHlzaXNTdGF0dXNDaGFuZ2UodmFsdWUpIHsKICAgICAgY29uc29sZS5sb2coJ/CflI0g5YiG5p6Q54q25oCB5Y+Y5YyWOicsIHZhbHVlKTsKICAgICAgLy8g54q25oCB5Y+Y5YyW5pe256uL5Y2z5pCc57SiCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovaGFuZGxlUXVlcnk6IGZ1bmN0aW9uIGhhbmRsZVF1ZXJ5KCkgewogICAgICAvLyDph43nva7liLDnrKzkuIDpobUKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlX251bSA9IDE7CiAgICAgIC8vIOa3u+WKoOaQnOe0ouaXpeW/lwogICAgICBjb25zb2xlLmxvZygn8J+UjSDmiafooYzmkJzntKLvvIzmn6Xor6Llj4LmlbA6JywgdGhpcy5xdWVyeVBhcmFtcyk7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi9yZXNldFF1ZXJ5OiBmdW5jdGlvbiByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLmRhdGVSYW5nZSA9IFtdOwogICAgICAvLyDmiYvliqjph43nva7mn6Xor6Llj4LmlbAKICAgICAgdGhpcy5xdWVyeVBhcmFtcyA9IHsKICAgICAgICBwYWdlX251bTogMSwKICAgICAgICBwYWdlX3NpemU6IDEwLAogICAgICAgIHJlcXVpcmVtZW50TmFtZTogbnVsbCwKICAgICAgICBlbnRpdHlLZXl3b3JkOiBudWxsLAogICAgICAgIGFuYWx5c2lzU3RhdHVzOiBudWxsCiAgICAgIH07CiAgICAgIC8vIOmHjee9ruihqOWNlQogICAgICB0aGlzLnJlc2V0Rm9ybSgncXVlcnlGb3JtJyk7CiAgICAgIC8vIOa3u+WKoOmHjee9ruaXpeW/lwogICAgICBjb25zb2xlLmxvZygn8J+UhCDph43nva7mkJzntKLmnaHku7bvvIzmn6Xor6Llj4LmlbA6JywgdGhpcy5xdWVyeVBhcmFtcyk7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvKiog5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uICovaGFuZGxlU2VsZWN0aW9uQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIC8vIOi/h+a7pOaOiWlk5Li6bnVsbOaIlnVuZGVmaW5lZOeahOmhue+8jOW5tuehruS/nWlk5piv5pW05pWwCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLmZpbHRlcihmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLmlkICE9IG51bGwgJiYgaXRlbS5pZCAhPT0gdW5kZWZpbmVkOwogICAgICB9KS5tYXAoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICByZXR1cm4gcGFyc2VJbnQoaXRlbS5pZCk7CiAgICAgIH0pOwogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDE7CiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsKICAgIH0sCiAgICAvKiog5p+l55yL5YiG5p6Q5pel5b+XICovaGFuZGxlVmlld0xvZ3M6IGZ1bmN0aW9uIGhhbmRsZVZpZXdMb2dzKHJvdykgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgdGhpcy5jdXJyZW50UmVjb3JkID0gcm93OwogICAgICB0aGlzLmxvZ0RpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgICB0aGlzLmxvZ0xvYWRpbmcgPSB0cnVlOwoKICAgICAgLy8g6I635Y+W5YiG5p6Q6L+b5bqm5pel5b+XCiAgICAgICgwLCBfb3BpbmlvbkFuYWx5c2lzLmdldEFuYWx5c2lzUHJvZ3Jlc3MpKHJvdy50YXNrSWQgfHwgcm93LmlkKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzMi5sb2dMaXN0ID0gcmVzcG9uc2UuZGF0YSB8fCBbXTsKICAgICAgICBfdGhpczIubG9nTG9hZGluZyA9IGZhbHNlOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXMyLmxvZ0xvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOafpeeci+aKpeWRiiAqL2hhbmRsZVZpZXdSZXBvcnQ6IGZ1bmN0aW9uIGhhbmRsZVZpZXdSZXBvcnQocm93KSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICB0aGlzLmN1cnJlbnRSZWNvcmQgPSByb3c7CiAgICAgIHRoaXMucmVwb3J0RGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICAgIHRoaXMucmVwb3J0TG9hZGluZyA9IHRydWU7CgogICAgICAvLyDmqKHmi5/ojrflj5bmiqXlkYrmlbDmja4KICAgICAgc2V0VGltZW91dChmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXMzLnJlcG9ydERhdGEgPSB7CiAgICAgICAgICByZXF1aXJlbWVudE5hbWU6IHJvdy5yZXF1aXJlbWVudE5hbWUsCiAgICAgICAgICBlbnRpdHlLZXl3b3JkOiByb3cuZW50aXR5S2V5d29yZCwKICAgICAgICAgIHNwZWNpZmljUmVxdWlyZW1lbnQ6IHJvdy5zcGVjaWZpY1JlcXVpcmVtZW50LAogICAgICAgICAgdG90YWxBcnRpY2xlczogTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogMTAwMCkgKyAxMDAsCiAgICAgICAgICBzZW50aW1lbnQ6IHsKICAgICAgICAgICAgcG9zaXRpdmU6IE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIDUwKSArIDIwLAogICAgICAgICAgICBuZXV0cmFsOiBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiAzMCkgKyAxMCwKICAgICAgICAgICAgbmVnYXRpdmU6IE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIDIwKSArIDUKICAgICAgICAgIH0KICAgICAgICB9OwogICAgICAgIF90aGlzMy5yZXBvcnRMb2FkaW5nID0gZmFsc2U7CiAgICAgIH0sIDEwMDApOwogICAgfSwKICAgIC8qKiDmiqXlkYogKi9oYW5kbGVSZXBvcnQ6IGZ1bmN0aW9uIGhhbmRsZVJlcG9ydChyb3cpIHsKICAgICAgLy8g5qOA5p+l5piv5ZCm5pyJ5oql5ZGKT1NTIFVSTAogICAgICBpZiAocm93LnJlcG9ydE9zc1VybCkgewogICAgICAgIC8vIOWmguaenOaciU9TUyBVUkzvvIznm7TmjqXlnKjmlrDnqpflj6PmiZPlvIAKICAgICAgICB3aW5kb3cub3Blbihyb3cucmVwb3J0T3NzVXJsLCAnX2JsYW5rJyk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8g5aaC5p6c5rKh5pyJT1NTIFVSTO+8jOaYvuekuuaaguaXoOaKpeWRiuaPkOekugogICAgICAgIHRoaXMuJG1vZGFsLm1zZygn5pqC5peg5oql5ZGKJyk7CiAgICAgIH0KICAgIH0sCiAgICAvKiog56uL5Y2z5o6o6YCBICovaGFuZGxlUHVzaE5vdzogZnVuY3Rpb24gaGFuZGxlUHVzaE5vdyhyb3cpIHsKICAgICAgLy8g56e76ZmkT1NTIFVSTOajgOafpe+8jOiuqeWQjuerr+WkhOeQhuaKpeWRiueUn+aIkOWSjOS4iuS8oAogICAgICAvLyDlpoLmnpzmsqHmnIlyZXBvcnRPc3NVcmzvvIzlkI7nq6/kvJroh6rliqjnlJ/miJBIVE1M5oql5ZGK5bm25LiK5Lyg5YiwT1NTCgogICAgICB0aGlzLmN1cnJlbnRSZWNvcmQgPSByb3c7CiAgICAgIHRoaXMucHVzaEZvcm0gPSB7CiAgICAgICAgcHVzaF91cmw6ICcnLAogICAgICAgIHB1c2hfY29udGVudDogIlx1NTIwNlx1Njc5MFx1NjJBNVx1NTQ0QVx1RkYxQSIuY29uY2F0KHJvdy5yZXF1aXJlbWVudE5hbWUsICIgLSBcdTdBQ0JcdTUzNzNcdTYzQThcdTkwMDEiKQogICAgICB9OwogICAgICB0aGlzLnB1c2hEaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICAvKiog5a6a5pe25o6o6YCBICovaGFuZGxlU2NoZWR1bGVQdXNoOiBmdW5jdGlvbiBoYW5kbGVTY2hlZHVsZVB1c2gocm93KSB7CiAgICAgIHRoaXMuY3VycmVudFJlY29yZCA9IHJvdzsKICAgICAgdGhpcy5zY2hlZHVsZVB1c2hGb3JtID0gewogICAgICAgIHB1c2hfdXJsOiAnJywKICAgICAgICBwdXNoX3RpbWU6ICcnLAogICAgICAgIGZyZXF1ZW5jeTogJ29uY2UnLAogICAgICAgIHB1c2hfY29udGVudDogIlx1NTIwNlx1Njc5MFx1NjJBNVx1NTQ0QVx1RkYxQSIuY29uY2F0KHJvdy5yZXF1aXJlbWVudE5hbWUpCiAgICAgIH07CiAgICAgIHRoaXMuc2NoZWR1bGVQdXNoRGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgLyoqIOaPkOS6pOeri+WNs+aOqOmAgSAqL3N1Ym1pdFB1c2g6IGZ1bmN0aW9uIHN1Ym1pdFB1c2goKSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICB0aGlzLiRyZWZzWydwdXNoRm9ybSddLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgX3RoaXM0LnB1c2hMb2FkaW5nID0gdHJ1ZTsKCiAgICAgICAgICAvLyDmnoTlu7rmjqjpgIHmlbDmja4KICAgICAgICAgIC8vIOWmguaenOayoeaciXJlcG9ydE9zc1VybO+8jOWQjuerr+S8muiHquWKqOeUn+aIkEhUTUzmiqXlkYrlubbkuIrkvKDliLBPU1MKICAgICAgICAgIHZhciBwdXNoRGF0YSA9IHsKICAgICAgICAgICAgdGFyZ2V0X3VybDogX3RoaXM0LnB1c2hGb3JtLnB1c2hfdXJsLAogICAgICAgICAgICByZXBvcnRfZGF0YTogewogICAgICAgICAgICAgIHJlcXVpcmVtZW50TmFtZTogX3RoaXM0LmN1cnJlbnRSZWNvcmQucmVxdWlyZW1lbnROYW1lLAogICAgICAgICAgICAgIGVudGl0eUtleXdvcmQ6IF90aGlzNC5jdXJyZW50UmVjb3JkLmVudGl0eUtleXdvcmQsCiAgICAgICAgICAgICAgc3BlY2lmaWNSZXF1aXJlbWVudDogX3RoaXM0LmN1cnJlbnRSZWNvcmQuc3BlY2lmaWNSZXF1aXJlbWVudCwKICAgICAgICAgICAgICByZXBvcnRPc3NVcmw6IF90aGlzNC5jdXJyZW50UmVjb3JkLnJlcG9ydE9zc1VybCB8fCBudWxsLAogICAgICAgICAgICAgIC8vIOWPr+iDveS4uuepuu+8jOWQjuerr+S8muWkhOeQhgogICAgICAgICAgICAgIHRvdGFsQXJ0aWNsZXM6IDAsCiAgICAgICAgICAgICAgLy8g5ZCO56uv5Lya5LuO5a6e6ZmF5pWw5o2u5Lit6I635Y+WCiAgICAgICAgICAgICAgc2VudGltZW50OiB7fSwKICAgICAgICAgICAgICAvLyDlkI7nq6/kvJrku47lrp7pmYXmlbDmja7kuK3ojrflj5YKICAgICAgICAgICAgICBkYXRhU291cmNlczogMCwKICAgICAgICAgICAgICAvLyDlkI7nq6/kvJrku47lrp7pmYXmlbDmja7kuK3ojrflj5YKICAgICAgICAgICAgICAvLyDmt7vliqDmm7TlpJrkv6Hmga/ku6Xkvr/lkI7nq6/nlJ/miJDlrozmlbTmiqXlkYoKICAgICAgICAgICAgICB0YXNrSWQ6IF90aGlzNC5jdXJyZW50UmVjb3JkLmlkLAogICAgICAgICAgICAgIGNyZWF0ZVRpbWU6IF90aGlzNC5jdXJyZW50UmVjb3JkLmNyZWF0ZVRpbWUsCiAgICAgICAgICAgICAgc3RhdHVzOiBfdGhpczQuY3VycmVudFJlY29yZC5zdGF0dXMKICAgICAgICAgICAgfSwKICAgICAgICAgICAgYW5hbHlzaXNfcmVzdWx0czogewogICAgICAgICAgICAgIHN1bW1hcnk6IF90aGlzNC5wdXNoRm9ybS5wdXNoX2NvbnRlbnQsCiAgICAgICAgICAgICAgcmVwb3J0VXJsOiBfdGhpczQuY3VycmVudFJlY29yZC5yZXBvcnRPc3NVcmwgfHwgbnVsbCAvLyDlj6/og73kuLrnqbrvvIzlkI7nq6/kvJrnlJ/miJAKICAgICAgICAgICAgfSwKICAgICAgICAgICAgcmVxdWlyZW1lbnRfaWQ6IF90aGlzNC5jdXJyZW50UmVjb3JkLmlkLAogICAgICAgICAgICBwdXNoX3R5cGU6ICdpbW1lZGlhdGUnCiAgICAgICAgICB9OwogICAgICAgICAgKDAsIF9vcGluaW9uQW5hbHlzaXMucHVzaFJlcG9ydCkocHVzaERhdGEpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgIC8vIOajgOafpeWTjeW6lOW5tuaYvuekuuivpue7huS/oeaBrwogICAgICAgICAgICBpZiAocmVzcG9uc2UgJiYgcmVzcG9uc2Uuc3VjY2VzcykgewogICAgICAgICAgICAgIF90aGlzNC4kbW9kYWwubXNnU3VjY2Vzcygn5o6o6YCB5oiQ5YqfJyk7CgogICAgICAgICAgICAgIC8vIOWmguaenOWQjuerr+eUn+aIkOS6huaWsOeahOaKpeWRilVSTO+8jOWPr+S7peabtOaWsOacrOWcsOiusOW9lQogICAgICAgICAgICAgIGlmIChyZXNwb25zZS5kYXRhICYmIHJlc3BvbnNlLmRhdGEucmVwb3J0X3VybCAmJiAhX3RoaXM0LmN1cnJlbnRSZWNvcmQucmVwb3J0T3NzVXJsKSB7CiAgICAgICAgICAgICAgICBfdGhpczQuY3VycmVudFJlY29yZC5yZXBvcnRPc3NVcmwgPSByZXNwb25zZS5kYXRhLnJlcG9ydF91cmw7CiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn5pu05paw5pys5Zyw5oql5ZGKVVJMOicsIHJlc3BvbnNlLmRhdGEucmVwb3J0X3VybCk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIF90aGlzNC4kbW9kYWwubXNnU3VjY2Vzcygn5o6o6YCB5a6M5oiQJyk7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgX3RoaXM0LnB1c2hEaWFsb2dWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICAgIF90aGlzNC5wdXNoTG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKGVycm9yKSB7CiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+aOqOmAgeWksei0pTonLCBlcnJvcik7CiAgICAgICAgICAgIGlmIChlcnJvci5yZXNwb25zZSAmJiBlcnJvci5yZXNwb25zZS5kYXRhICYmIGVycm9yLnJlc3BvbnNlLmRhdGEubXNnKSB7CiAgICAgICAgICAgICAgX3RoaXM0LiRtb2RhbC5tc2dFcnJvcign5o6o6YCB5aSx6LSlOiAnICsgZXJyb3IucmVzcG9uc2UuZGF0YS5tc2cpOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIF90aGlzNC4kbW9kYWwubXNnRXJyb3IoJ+aOqOmAgeWksei0pe+8jOivt+mHjeivlScpOwogICAgICAgICAgICB9CiAgICAgICAgICAgIF90aGlzNC5wdXNoTG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5o+Q5Lqk5a6a5pe25o6o6YCBICovc3VibWl0U2NoZWR1bGVQdXNoOiBmdW5jdGlvbiBzdWJtaXRTY2hlZHVsZVB1c2goKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICB0aGlzLiRyZWZzWydzY2hlZHVsZVB1c2hGb3JtJ10udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBfdGhpczUuc2NoZWR1bGVQdXNoTG9hZGluZyA9IHRydWU7CiAgICAgICAgICB2YXIgdGFza0RhdGEgPSB7CiAgICAgICAgICAgIHJlcXVpcmVtZW50X2lkOiBfdGhpczUuY3VycmVudFJlY29yZC5pZCwKICAgICAgICAgICAgcHVzaF91cmw6IF90aGlzNS5zY2hlZHVsZVB1c2hGb3JtLnB1c2hfdXJsLAogICAgICAgICAgICBwdXNoX3RpbWU6IF90aGlzNS5zY2hlZHVsZVB1c2hGb3JtLnB1c2hfdGltZSwKICAgICAgICAgICAgZnJlcXVlbmN5OiBfdGhpczUuc2NoZWR1bGVQdXNoRm9ybS5mcmVxdWVuY3ksCiAgICAgICAgICAgIHB1c2hfY29udGVudDogX3RoaXM1LnNjaGVkdWxlUHVzaEZvcm0ucHVzaF9jb250ZW50CiAgICAgICAgICB9OwogICAgICAgICAgKDAsIF9vcGluaW9uQW5hbHlzaXMuY3JlYXRlVGltZWRUYXNrKSh0YXNrRGF0YSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgX3RoaXM1LiRtb2RhbC5tc2dTdWNjZXNzKCflrprml7bmjqjpgIHku7vliqHliJvlu7rmiJDlip8nKTsKICAgICAgICAgICAgX3RoaXM1LnNjaGVkdWxlUHVzaERpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgICAgICAgICAgX3RoaXM1LnNjaGVkdWxlUHVzaExvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgX3RoaXM1LnNjaGVkdWxlUHVzaExvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqL2hhbmRsZURlbGV0ZTogZnVuY3Rpb24gaGFuZGxlRGVsZXRlKHJvdykgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKICAgICAgdmFyIGlkcywgbmFtZXM7CgogICAgICAvLyDliKTmlq3mmK/lkKbkuLrmibnph4/liKDpmaTvvIjml6Dlj4LmlbDmiJblj4LmlbDkuLrkuovku7blr7nosaHvvIkKICAgICAgaWYgKCFyb3cgfHwgcm93IGluc3RhbmNlb2YgRXZlbnQpIHsKICAgICAgICAvLyDmibnph4/liKDpmaQKICAgICAgICBpZiAoIXRoaXMuaWRzIHx8IHRoaXMuaWRzLmxlbmd0aCA9PT0gMCkgewogICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoJ+ivt+WFiOmAieaLqeimgeWIoOmZpOeahOiusOW9lScpOwogICAgICAgICAgcmV0dXJuOwogICAgICAgIH0KICAgICAgICAvLyDov4fmu6Tlubbpqozor4FJRHMKICAgICAgICBpZHMgPSB0aGlzLmlkcy5maWx0ZXIoZnVuY3Rpb24gKGlkKSB7CiAgICAgICAgICByZXR1cm4gaWQgIT0gbnVsbCAmJiAhaXNOYU4oaWQpOwogICAgICAgIH0pLm1hcChmdW5jdGlvbiAoaWQpIHsKICAgICAgICAgIHJldHVybiBwYXJzZUludChpZCk7CiAgICAgICAgfSk7CiAgICAgICAgaWYgKGlkcy5sZW5ndGggPT09IDApIHsKICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfpgInkuK3nmoTorrDlvZVJROaXoOaViO+8jOaXoOazleWIoOmZpCcpOwogICAgICAgICAgcmV0dXJuOwogICAgICAgIH0KICAgICAgICBuYW1lcyA9IHRoaXMucmVjb3JkTGlzdC5maWx0ZXIoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgIHJldHVybiBpZHMuaW5jbHVkZXMoaXRlbS5pZCk7CiAgICAgICAgfSkubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICByZXR1cm4gaXRlbS5yZXF1aXJlbWVudE5hbWU7CiAgICAgICAgfSkuam9pbign44CBJyk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8g5Y2V5Liq5Yig6ZmkCiAgICAgICAgaWYgKCFyb3cuaWQgfHwgcm93LmlkID09IG51bGwpIHsKICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCforrDlvZVJROaXoOaViO+8jOaXoOazleWIoOmZpCcpOwogICAgICAgICAgcmV0dXJuOwogICAgICAgIH0KICAgICAgICBpZHMgPSBbcGFyc2VJbnQocm93LmlkKV07CiAgICAgICAgbmFtZXMgPSByb3cucmVxdWlyZW1lbnROYW1lIHx8ICfmnKrnn6XorrDlvZUnOwogICAgICB9CiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oIlx1NjYyRlx1NTQyNlx1Nzg2RVx1OEJBNFx1NTIyMFx1OTY2NFx1NTIwNlx1Njc5MFx1OEJCMFx1NUY1NVwiIi5jb25jYXQobmFtZXMsICJcIlx1RkYxRiIpKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICAvLyDmoLnmja5JRHPmlbDph4/liKTmlq3mmK/ljZXkuKrliKDpmaTov5jmmK/mibnph4/liKDpmaQKICAgICAgICBpZiAoaWRzLmxlbmd0aCA9PT0gMSkgewogICAgICAgICAgLy8g5Y2V5Liq5Yig6ZmkCiAgICAgICAgICByZXR1cm4gKDAsIF9vcGluaW9uQW5hbHlzaXMuZGVsZXRlQW5hbHlzaXNUYXNrKShpZHNbMF0pOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAvLyDmibnph4/liKDpmaQKICAgICAgICAgIHJldHVybiAoMCwgX29waW5pb25BbmFseXNpcy5iYXRjaERlbGV0ZUFuYWx5c2lzVGFza3MpKGlkcyk7CiAgICAgICAgfQogICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIGNvbnNvbGUubG9nKCfwn5eR77iPIOWIoOmZpOaTjeS9nOaIkOWKn++8jOWTjeW6lDonLCByZXNwb25zZSk7CgogICAgICAgIC8vIOajgOafpeaYr+WQpumcgOimgemHjee9ruWIhumhte+8iOeJqeeQhuWIoOmZpOWQjumcgOimgemHjeaWsOiuoeeul+WIhumhte+8iQogICAgICAgIHZhciBjdXJyZW50UGFnZVJlY29yZHMgPSBfdGhpczYucmVjb3JkTGlzdC5sZW5ndGg7CiAgICAgICAgdmFyIGRlbGV0ZWRDb3VudCA9IGlkcy5sZW5ndGg7CgogICAgICAgIC8vIOWmguaenOWIoOmZpOWQjuW9k+WJjemhteWPr+iDveayoeacieiusOW9leS6hu+8jOS4lOS4jeaYr+esrOS4gOmhte+8jOWImeWbnuWIsOS4iuS4gOmhtQogICAgICAgIGlmIChjdXJyZW50UGFnZVJlY29yZHMgPD0gZGVsZXRlZENvdW50ICYmIF90aGlzNi5xdWVyeVBhcmFtcy5wYWdlX251bSA+IDEpIHsKICAgICAgICAgIF90aGlzNi5xdWVyeVBhcmFtcy5wYWdlX251bSA9IF90aGlzNi5xdWVyeVBhcmFtcy5wYWdlX251bSAtIDE7CiAgICAgICAgICBjb25zb2xlLmxvZygn8J+UhCDliKDpmaTlkI7lm57liLDkuIrkuIDpobU6JywgX3RoaXM2LnF1ZXJ5UGFyYW1zLnBhZ2VfbnVtKTsKICAgICAgICB9CgogICAgICAgIC8vIOmHjee9rumAieS4reeKtuaAgQogICAgICAgIF90aGlzNi5pZHMgPSBbXTsKICAgICAgICBfdGhpczYuc2luZ2xlID0gdHJ1ZTsKICAgICAgICBfdGhpczYubXVsdGlwbGUgPSB0cnVlOwoKICAgICAgICAvLyDniannkIbliKDpmaTmiJDlip/lkI7vvIznm7TmjqXph43mlrDojrflj5bmlbDmja7ku6Xnoa7kv53mlbDmja7kuIDoh7TmgKcKICAgICAgICBfdGhpczYuZ2V0TGlzdCgpOwogICAgICAgIF90aGlzNi4kbW9kYWwubXNnU3VjY2Vzcygn5Yig6Zmk5oiQ5YqfJyk7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uIChlcnJvcikgewogICAgICAgIGlmIChlcnJvci5yZXNwb25zZSAmJiBlcnJvci5yZXNwb25zZS5kYXRhKSB7CiAgICAgICAgICBpZiAoZXJyb3IucmVzcG9uc2UuZGF0YS5tc2cpIHsKICAgICAgICAgICAgX3RoaXM2LiRtb2RhbC5tc2dFcnJvcihlcnJvci5yZXNwb25zZS5kYXRhLm1zZyk7CiAgICAgICAgICB9IGVsc2UgaWYgKGVycm9yLnJlc3BvbnNlLmRhdGEubWVzc2FnZSkgewogICAgICAgICAgICBfdGhpczYuJG1vZGFsLm1zZ0Vycm9yKGVycm9yLnJlc3BvbnNlLmRhdGEubWVzc2FnZSk7CiAgICAgICAgICB9IGVsc2UgaWYgKGVycm9yLnJlc3BvbnNlLmRhdGEuZGV0YWlsKSB7CiAgICAgICAgICAgIC8vIOWkhOeQhkZhc3RBUEnnmoTpqozor4HplJnor68KICAgICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkoZXJyb3IucmVzcG9uc2UuZGF0YS5kZXRhaWwpKSB7CiAgICAgICAgICAgICAgdmFyIGRldGFpbHMgPSBlcnJvci5yZXNwb25zZS5kYXRhLmRldGFpbC5tYXAoZnVuY3Rpb24gKGQpIHsKICAgICAgICAgICAgICAgIHJldHVybiBkLm1zZyB8fCBkLm1lc3NhZ2UgfHwgSlNPTi5zdHJpbmdpZnkoZCk7CiAgICAgICAgICAgICAgfSkuam9pbignLCAnKTsKICAgICAgICAgICAgICBfdGhpczYuJG1vZGFsLm1zZ0Vycm9yKCJcdTUyMjBcdTk2NjRcdTU5MzFcdThEMjU6ICIuY29uY2F0KGRldGFpbHMpKTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICBfdGhpczYuJG1vZGFsLm1zZ0Vycm9yKCJcdTUyMjBcdTk2NjRcdTU5MzFcdThEMjU6ICIuY29uY2F0KGVycm9yLnJlc3BvbnNlLmRhdGEuZGV0YWlsKSk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIF90aGlzNi4kbW9kYWwubXNnRXJyb3IoIlx1NTIyMFx1OTY2NFx1NTkzMVx1OEQyNTogIi5jb25jYXQoSlNPTi5zdHJpbmdpZnkoZXJyb3IucmVzcG9uc2UuZGF0YSkpKTsKICAgICAgICAgIH0KICAgICAgICB9IGVsc2UgaWYgKGVycm9yLm1lc3NhZ2UpIHsKICAgICAgICAgIF90aGlzNi4kbW9kYWwubXNnRXJyb3IoIlx1NTIyMFx1OTY2NFx1NTkzMVx1OEQyNTogIi5jb25jYXQoZXJyb3IubWVzc2FnZSkpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBfdGhpczYuJG1vZGFsLm1zZ0Vycm9yKCfliKDpmaTlpLHotKXvvIzor7fph43or5UnKTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi9oYW5kbGVFeHBvcnQ6IGZ1bmN0aW9uIGhhbmRsZUV4cG9ydCgpIHsKICAgICAgdmFyIF90aGlzNyA9IHRoaXM7CiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+aYr+WQpuehruiupOWvvOWHuuaJgOacieWIhuaekOiusOW9leaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHZhciBwYXJhbXMgPSB7fTsKCiAgICAgICAgLy8g5re75Yqg5pCc57Si5p2h5Lu277yI5L2/55So5ZCO56uv5pyf5pyb55qE5LiL5YiS57q/5ZG95ZCN77yJCiAgICAgICAgaWYgKF90aGlzNy5xdWVyeVBhcmFtcy5yZXF1aXJlbWVudE5hbWUpIHsKICAgICAgICAgIHBhcmFtcy5yZXF1aXJlbWVudF9uYW1lID0gX3RoaXM3LnF1ZXJ5UGFyYW1zLnJlcXVpcmVtZW50TmFtZTsKICAgICAgICB9CiAgICAgICAgaWYgKF90aGlzNy5xdWVyeVBhcmFtcy5lbnRpdHlLZXl3b3JkKSB7CiAgICAgICAgICBwYXJhbXMuZW50aXR5X2tleXdvcmQgPSBfdGhpczcucXVlcnlQYXJhbXMuZW50aXR5S2V5d29yZDsKICAgICAgICB9CiAgICAgICAgaWYgKF90aGlzNy5xdWVyeVBhcmFtcy5hbmFseXNpc1N0YXR1cyAhPT0gbnVsbCAmJiBfdGhpczcucXVlcnlQYXJhbXMuYW5hbHlzaXNTdGF0dXMgIT09IHVuZGVmaW5lZCAmJiBfdGhpczcucXVlcnlQYXJhbXMuYW5hbHlzaXNTdGF0dXMgIT09ICcnKSB7CiAgICAgICAgICBwYXJhbXMuYW5hbHlzaXNfc3RhdHVzID0gX3RoaXM3LnF1ZXJ5UGFyYW1zLmFuYWx5c2lzU3RhdHVzOwogICAgICAgIH0KCiAgICAgICAgLy8g5re75Yqg5pe26Ze06IyD5Zu0CiAgICAgICAgaWYgKF90aGlzNy5kYXRlUmFuZ2UgJiYgX3RoaXM3LmRhdGVSYW5nZS5sZW5ndGggPT09IDIpIHsKICAgICAgICAgIHBhcmFtcy5jcmVhdGVfdGltZV9zdGFydCA9IF90aGlzNy5kYXRlUmFuZ2VbMF07CiAgICAgICAgICBwYXJhbXMuY3JlYXRlX3RpbWVfZW5kID0gX3RoaXM3LmRhdGVSYW5nZVsxXTsKICAgICAgICB9CiAgICAgICAgcmV0dXJuICgwLCBfb3BpbmlvbkFuYWx5c2lzLmV4cG9ydFJlcXVpcmVtZW50cykocGFyYW1zKTsKICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAvLyDlpITnkIbmlofku7bkuIvovb0KICAgICAgICB2YXIgYmxvYiA9IG5ldyBCbG9iKFtyZXNwb25zZV0sIHsKICAgICAgICAgIHR5cGU6ICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQuc3ByZWFkc2hlZXRtbC5zaGVldCcKICAgICAgICB9KTsKICAgICAgICB2YXIgdXJsID0gd2luZG93LlVSTC5jcmVhdGVPYmplY3RVUkwoYmxvYik7CiAgICAgICAgdmFyIGxpbmsgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdhJyk7CiAgICAgICAgbGluay5ocmVmID0gdXJsOwogICAgICAgIGxpbmsuZG93bmxvYWQgPSAiXHU1MjA2XHU2NzkwXHU4QkIwXHU1RjU1XyIuY29uY2F0KG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zbGljZSgwLCAxMCksICIueGxzeCIpOwogICAgICAgIGRvY3VtZW50LmJvZHkuYXBwZW5kQ2hpbGQobGluayk7CiAgICAgICAgbGluay5jbGljaygpOwogICAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQobGluayk7CiAgICAgICAgd2luZG93LlVSTC5yZXZva2VPYmplY3RVUkwodXJsKTsKICAgICAgICBfdGhpczcuJG1vZGFsLm1zZ1N1Y2Nlc3MoJ+WvvOWHuuaIkOWKnycpOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoZXJyb3IpIHsKICAgICAgICBpZiAoZXJyb3IucmVzcG9uc2UgJiYgZXJyb3IucmVzcG9uc2UuZGF0YSAmJiBlcnJvci5yZXNwb25zZS5kYXRhLm1zZykgewogICAgICAgICAgX3RoaXM3LiRtb2RhbC5tc2dFcnJvcihlcnJvci5yZXNwb25zZS5kYXRhLm1zZyk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIF90aGlzNy4kbW9kYWwubXNnRXJyb3IoJ+WvvOWHuuWksei0pe+8jOivt+mHjeivlScpOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWvvOWHuuaKpeWRiiAqL2hhbmRsZUV4cG9ydFJlcG9ydDogZnVuY3Rpb24gaGFuZGxlRXhwb3J0UmVwb3J0KCkgewogICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCfmiqXlkYrlr7zlh7rmiJDlip8nKTsKICAgIH0sCiAgICAvKiog6I635Y+W54q25oCB5qCH562+57G75Z6LICovZ2V0U3RhdHVzVGFnVHlwZTogZnVuY3Rpb24gZ2V0U3RhdHVzVGFnVHlwZShzdGF0dXMpIHsKICAgICAgdmFyIHN0YXR1c01hcCA9IHsKICAgICAgICAwOiAnaW5mbycsCiAgICAgICAgLy8g5b6F55Sf5oiQIC0g54Gw6ImyCiAgICAgICAgMTogJ3dhcm5pbmcnLAogICAgICAgIC8vIOeUn+aIkOS4rSAtIOapmeiJsgogICAgICAgIDI6ICdzdWNjZXNzJywKICAgICAgICAvLyDlt7LnlJ/miJAgLSDnu7/oibIKICAgICAgICAnLTEnOiAnZGFuZ2VyJyAvLyDnlJ/miJDlpLHotKUgLSDnuqLoibIKICAgICAgfTsKICAgICAgcmV0dXJuIHN0YXR1c01hcFtzdGF0dXNdIHx8ICdpbmZvJzsKICAgIH0sCiAgICAvKiog6I635Y+W54q25oCB5paH5pysICovZ2V0U3RhdHVzVGV4dDogZnVuY3Rpb24gZ2V0U3RhdHVzVGV4dChzdGF0dXMpIHsKICAgICAgdmFyIHN0YXR1c01hcCA9IHsKICAgICAgICAwOiAn5b6F55Sf5oiQJywKICAgICAgICAxOiAn55Sf5oiQ5LitJywKICAgICAgICAyOiAn5bey55Sf5oiQJywKICAgICAgICAnLTEnOiAn55Sf5oiQ5aSx6LSlJwogICAgICB9OwogICAgICByZXR1cm4gc3RhdHVzTWFwW3N0YXR1c10gfHwgJ+acquefpSc7CiAgICB9LAogICAgLyoqIOiOt+WPluaXpeW/l+e6p+WIq+agh+etvuexu+WeiyAqL2dldExvZ0xldmVsVGFnVHlwZTogZnVuY3Rpb24gZ2V0TG9nTGV2ZWxUYWdUeXBlKGxldmVsKSB7CiAgICAgIHZhciBsZXZlbE1hcCA9IHsKICAgICAgICAnaW5mbyc6ICdpbmZvJywKICAgICAgICAnc3VjY2Vzcyc6ICdzdWNjZXNzJywKICAgICAgICAnd2FybmluZyc6ICd3YXJuaW5nJywKICAgICAgICAnZXJyb3InOiAnZGFuZ2VyJwogICAgICB9OwogICAgICByZXR1cm4gbGV2ZWxNYXBbbGV2ZWxdIHx8ICdpbmZvJzsKICAgIH0sCiAgICAvLyA9PT09PT09PT09PT09PT09PT09PSDlrprml7bku7vliqHnm7jlhbPmlrnms5UgPT09PT09PT09PT09PT09PT09PT0KICAgIC8vIOWkhOeQhuWumuaXtuaOqOmAgeaMiemSrueCueWHuwogICAgaGFuZGxlVGltZWRQdXNoOiBmdW5jdGlvbiBoYW5kbGVUaW1lZFB1c2goKSB7CiAgICAgIHRoaXMudGltZWRUYXNrRGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICAgIC8vIOaJk+W8gOWumuaXtuaOqOmAgeW8ueeql+aXtuWKoOi9veWumuaXtuS7u+WKoeWIl+ihqAogICAgICB0aGlzLmxvYWRUaW1lZFRhc2tMaXN0KCk7CiAgICB9LAogICAgLy8g5YWz6Zet5a6a5pe25Lu75Yqh5by556qXCiAgICBjbG9zZVRpbWVkVGFza0RpYWxvZzogZnVuY3Rpb24gY2xvc2VUaW1lZFRhc2tEaWFsb2coKSB7CiAgICAgIHRoaXMudGltZWRUYXNrRGlhbG9nVmlzaWJsZSA9IGZhbHNlOwogICAgfSwKICAgIC8vIOWkhOeQhuWIm+W7uuWumuaXtuS7u+WKoQogICAgaGFuZGxlQ3JlYXRlVGltZWRUYXNrOiBmdW5jdGlvbiBoYW5kbGVDcmVhdGVUaW1lZFRhc2soKSB7CiAgICAgIHRoaXMucmVzZXRUYXNrRm9ybSgpOwogICAgICB0aGlzLmVkaXRpbmdUYXNrSW5kZXggPSAtMTsgLy8g6K6+572u5Li65paw5bu65qih5byPCiAgICAgIHRoaXMubG9hZE9waW5pb25UYXNrTGlzdCgpOyAvLyDliqDovb3oiIbmg4Xku7vliqHliJfooagKICAgICAgdGhpcy5jcmVhdGVUYXNrRGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgLy8g5aSE55CG5re75Yqg5a6a5pe25Lu75Yqh5oyJ6ZKuCiAgICBoYW5kbGVBZGRUaW1lZFRhc2s6IGZ1bmN0aW9uIGhhbmRsZUFkZFRpbWVkVGFzaygpIHsKICAgICAgdGhpcy5yZXNldFRhc2tGb3JtKCk7CiAgICAgIHRoaXMuZWRpdGluZ1Rhc2tJbmRleCA9IC0xOyAvLyDorr7nva7kuLrmlrDlu7rmqKHlvI8KICAgICAgdGhpcy5sb2FkT3BpbmlvblRhc2tMaXN0KCk7IC8vIOWKoOi9veiIhuaDheS7u+WKoeWIl+ihqAogICAgICB0aGlzLmNyZWF0ZVRhc2tEaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICAvLyDliqDovb3oiIbmg4Xku7vliqHliJfooajvvIjnlKjkuo7lrprml7bmjqjpgIHnu5HlrprvvIkKICAgIGxvYWRPcGluaW9uVGFza0xpc3Q6IGZ1bmN0aW9uIGxvYWRPcGluaW9uVGFza0xpc3QoKSB7CiAgICAgIHZhciBfdGhpczggPSB0aGlzOwogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSgvKiNfX1BVUkVfXyovKDAsIF9yZWdlbmVyYXRvcjIuZGVmYXVsdCkoKS5tKGZ1bmN0aW9uIF9jYWxsZWUoKSB7CiAgICAgICAgdmFyIHJlc3BvbnNlLCB0YXNrcywgX3Q7CiAgICAgICAgcmV0dXJuICgwLCBfcmVnZW5lcmF0b3IyLmRlZmF1bHQpKCkudyhmdW5jdGlvbiAoX2NvbnRleHQpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0Lm4pIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIF9jb250ZXh0LnAgPSAwOwogICAgICAgICAgICAgIF9jb250ZXh0Lm4gPSAxOwogICAgICAgICAgICAgIHJldHVybiAoMCwgX29waW5pb25BbmFseXNpcy5nZXRBbmFseXNpc1Rhc2tMaXN0KSh7CiAgICAgICAgICAgICAgICBwYWdlX251bTogMSwKICAgICAgICAgICAgICAgIHBhZ2Vfc2l6ZTogMTAwCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIGNhc2UgMToKICAgICAgICAgICAgICByZXNwb25zZSA9IF9jb250ZXh0LnY7CiAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLnN1Y2Nlc3MpIHsKICAgICAgICAgICAgICAgIC8vIOWkhOeQhuWIhumhteaVsOaNru+8jOS7jiBvcGluaW9uX3Rhc2sg6KGo6I635Y+W5pWw5o2uCiAgICAgICAgICAgICAgICB0YXNrcyA9IFtdOwogICAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEgJiYgcmVzcG9uc2UuZGF0YS5yb3dzKSB7CiAgICAgICAgICAgICAgICAgIHRhc2tzID0gcmVzcG9uc2UuZGF0YS5yb3dzOwogICAgICAgICAgICAgICAgfSBlbHNlIGlmIChBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEpKSB7CiAgICAgICAgICAgICAgICAgIHRhc2tzID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAocmVzcG9uc2Uucm93cykgewogICAgICAgICAgICAgICAgICB0YXNrcyA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgLy8g5pig5bCE5Lu75Yqh5pWw5o2u5Yiw6ZyA5rGC5YiX6KGo5qC85byPCiAgICAgICAgICAgICAgICBfdGhpczgucmVxdWlyZW1lbnRMaXN0ID0gdGFza3MubWFwKGZ1bmN0aW9uICh0YXNrKSB7CiAgICAgICAgICAgICAgICAgIHJldHVybiB7CiAgICAgICAgICAgICAgICAgICAgaWQ6IHRhc2suaWQsCiAgICAgICAgICAgICAgICAgICAgcmVxdWlyZW1lbnROYW1lOiB0YXNrLnJlcXVpcmVtZW50TmFtZSB8fCB0YXNrLnJlcXVpcmVtZW50X25hbWUgfHwgIlx1NEVGQlx1NTJBMSIuY29uY2F0KHRhc2suaWQpLAogICAgICAgICAgICAgICAgICAgIHRhc2tJZDogdGFzay5pZCwKICAgICAgICAgICAgICAgICAgICBrZXl3b3JkczogdGFzay5rZXl3b3JkcyB8fCB0YXNrLmVudGl0eV9rZXl3b3JkLAogICAgICAgICAgICAgICAgICAgIHNwZWNpZmljUmVxdWlyZW1lbnQ6IHRhc2suc3BlY2lmaWNSZXF1aXJlbWVudCB8fCB0YXNrLnNwZWNpZmljX3JlcXVpcmVtZW50LAogICAgICAgICAgICAgICAgICAgIGNyZWF0ZVRpbWU6IHRhc2suY3JlYXRlVGltZSB8fCB0YXNrLmNyZWF0ZV90aW1lCiAgICAgICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgX3RoaXM4LiRtZXNzYWdlLmVycm9yKHJlc3BvbnNlLm1zZyB8fCAn6I635Y+W6IiG5oOF5Lu75Yqh5YiX6KGo5aSx6LSlJyk7CiAgICAgICAgICAgICAgICBfdGhpczgucmVxdWlyZW1lbnRMaXN0ID0gW107CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF9jb250ZXh0Lm4gPSAzOwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDI6CiAgICAgICAgICAgICAgX2NvbnRleHQucCA9IDI7CiAgICAgICAgICAgICAgX3QgPSBfY29udGV4dC52OwogICAgICAgICAgICAgIF90aGlzOC4kbWVzc2FnZS5lcnJvcign5Yqg6L296IiG5oOF5Lu75Yqh5YiX6KGo5aSx6LSlJyk7CiAgICAgICAgICAgICAgX3RoaXM4LnJlcXVpcmVtZW50TGlzdCA9IFtdOwogICAgICAgICAgICBjYXNlIDM6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LmEoMik7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZSwgbnVsbCwgW1swLCAyXV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvLyDliqDovb3lrprml7bku7vliqHliJfooagKICAgIGxvYWRUaW1lZFRhc2tMaXN0OiBmdW5jdGlvbiBsb2FkVGltZWRUYXNrTGlzdCgpIHsKICAgICAgdmFyIF90aGlzOSA9IHRoaXM7CiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKC8qI19fUFVSRV9fKi8oMCwgX3JlZ2VuZXJhdG9yMi5kZWZhdWx0KSgpLm0oZnVuY3Rpb24gX2NhbGxlZTIoKSB7CiAgICAgICAgdmFyIHJlc3BvbnNlLCBfdDI7CiAgICAgICAgcmV0dXJuICgwLCBfcmVnZW5lcmF0b3IyLmRlZmF1bHQpKCkudyhmdW5jdGlvbiAoX2NvbnRleHQyKSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDIubikgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX2NvbnRleHQyLnAgPSAwOwogICAgICAgICAgICAgIF9jb250ZXh0Mi5uID0gMTsKICAgICAgICAgICAgICByZXR1cm4gKDAsIF9vcGluaW9uQW5hbHlzaXMuZ2V0VGltZWRUYXNrTGlzdCkoKTsKICAgICAgICAgICAgY2FzZSAxOgogICAgICAgICAgICAgIHJlc3BvbnNlID0gX2NvbnRleHQyLnY7CiAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLnN1Y2Nlc3MpIHsKICAgICAgICAgICAgICAgIC8vIOWkhOeQhuWIhumhteaVsOaNriAtIOWQjuerr+i/lOWbnueahOaYr1BhZ2VSZXNwb25zZU1vZGVs5qC85byPCgogICAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEgJiYgcmVzcG9uc2UuZGF0YS5yb3dzKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzOS50aW1lZFRhc2tMaXN0ID0gcmVzcG9uc2UuZGF0YS5yb3dzLm1hcChmdW5jdGlvbiAodGFzaykgewogICAgICAgICAgICAgICAgICAgIHJldHVybiB7CiAgICAgICAgICAgICAgICAgICAgICBpZDogdGFzay5pZCwKICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVtZW50SWQ6IHRhc2sucmVxdWlyZW1lbnRJZCwKICAgICAgICAgICAgICAgICAgICAgIG5hbWU6IHRhc2sudGFza05hbWUsCiAgICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogdGFzay50YXNrRGVzY3JpcHRpb24sCiAgICAgICAgICAgICAgICAgICAgICBleGVjdXRlVGltZTogdGFzay5leGVjdXRlVGltZSwKICAgICAgICAgICAgICAgICAgICAgIGV4ZWN1dGVEYXRlVGltZTogdGFzay5mcmVxdWVuY3kgPT09ICdvbmNlJyAmJiB0YXNrLmV4ZWN1dGVfZGF0ZSA/ICIiLmNvbmNhdCh0YXNrLmV4ZWN1dGVfZGF0ZSwgIiAiKS5jb25jYXQodGFzay5leGVjdXRlVGltZSkgOiAnJywKICAgICAgICAgICAgICAgICAgICAgIGZyZXF1ZW5jeTogdGFzay5mcmVxdWVuY3ksCiAgICAgICAgICAgICAgICAgICAgICBzdGF0dXM6IHRhc2suc3RhdHVzID09PSAncnVubmluZycgPyAncnVubmluZycgOiAncGVuZGluZycsCiAgICAgICAgICAgICAgICAgICAgICBwdXNoVXJsOiB0YXNrLnB1c2hVcmwKICAgICAgICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAocmVzcG9uc2UuZGF0YSAmJiByZXNwb25zZS5kYXRhLnJlY29yZHMgJiYgQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhLnJlY29yZHMpKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzOS50aW1lZFRhc2tMaXN0ID0gcmVzcG9uc2UuZGF0YS5yZWNvcmRzLm1hcChmdW5jdGlvbiAodGFzaykgewogICAgICAgICAgICAgICAgICAgIHJldHVybiB7CiAgICAgICAgICAgICAgICAgICAgICBpZDogdGFzay5pZCwKICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVtZW50SWQ6IHRhc2sucmVxdWlyZW1lbnRJZCwKICAgICAgICAgICAgICAgICAgICAgIG5hbWU6IHRhc2sudGFza05hbWUsCiAgICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogdGFzay50YXNrRGVzY3JpcHRpb24sCiAgICAgICAgICAgICAgICAgICAgICBleGVjdXRlVGltZTogdGFzay5leGVjdXRlVGltZSwKICAgICAgICAgICAgICAgICAgICAgIGV4ZWN1dGVEYXRlVGltZTogdGFzay5mcmVxdWVuY3kgPT09ICdvbmNlJyAmJiB0YXNrLmV4ZWN1dGVfZGF0ZSA/ICIiLmNvbmNhdCh0YXNrLmV4ZWN1dGVfZGF0ZSwgIiAiKS5jb25jYXQodGFzay5leGVjdXRlVGltZSkgOiAnJywKICAgICAgICAgICAgICAgICAgICAgIGZyZXF1ZW5jeTogdGFzay5mcmVxdWVuY3ksCiAgICAgICAgICAgICAgICAgICAgICBzdGF0dXM6IHRhc2suc3RhdHVzID09PSAncnVubmluZycgPyAncnVubmluZycgOiAncGVuZGluZycsCiAgICAgICAgICAgICAgICAgICAgICBwdXNoVXJsOiB0YXNrLnB1c2hVcmwKICAgICAgICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhKSkgewogICAgICAgICAgICAgICAgICBfdGhpczkudGltZWRUYXNrTGlzdCA9IHJlc3BvbnNlLmRhdGEubWFwKGZ1bmN0aW9uICh0YXNrKSB7CiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgICAgICAgICAgIGlkOiB0YXNrLmlkLAogICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZW1lbnRJZDogdGFzay5yZXF1aXJlbWVudElkLAogICAgICAgICAgICAgICAgICAgICAgbmFtZTogdGFzay50YXNrTmFtZSwKICAgICAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiB0YXNrLnRhc2tEZXNjcmlwdGlvbiwKICAgICAgICAgICAgICAgICAgICAgIGV4ZWN1dGVUaW1lOiB0YXNrLmV4ZWN1dGVUaW1lLAogICAgICAgICAgICAgICAgICAgICAgZXhlY3V0ZURhdGVUaW1lOiB0YXNrLmZyZXF1ZW5jeSA9PT0gJ29uY2UnICYmIHRhc2suZXhlY3V0ZV9kYXRlID8gIiIuY29uY2F0KHRhc2suZXhlY3V0ZV9kYXRlLCAiICIpLmNvbmNhdCh0YXNrLmV4ZWN1dGVUaW1lKSA6ICcnLAogICAgICAgICAgICAgICAgICAgICAgZnJlcXVlbmN5OiB0YXNrLmZyZXF1ZW5jeSwKICAgICAgICAgICAgICAgICAgICAgIHN0YXR1czogdGFzay5zdGF0dXMgPT09ICdydW5uaW5nJyA/ICdydW5uaW5nJyA6ICdwZW5kaW5nJywKICAgICAgICAgICAgICAgICAgICAgIHB1c2hVcmw6IHRhc2sucHVzaFVybAogICAgICAgICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgfSBlbHNlIGlmIChyZXNwb25zZS5yb3dzICYmIEFycmF5LmlzQXJyYXkocmVzcG9uc2Uucm93cykpIHsKICAgICAgICAgICAgICAgICAgX3RoaXM5LnRpbWVkVGFza0xpc3QgPSByZXNwb25zZS5yb3dzLm1hcChmdW5jdGlvbiAodGFzaykgewogICAgICAgICAgICAgICAgICAgIHJldHVybiB7CiAgICAgICAgICAgICAgICAgICAgICBpZDogdGFzay5pZCwKICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVtZW50SWQ6IHRhc2sucmVxdWlyZW1lbnRJZCwKICAgICAgICAgICAgICAgICAgICAgIG5hbWU6IHRhc2sudGFza05hbWUsCiAgICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogdGFzay50YXNrRGVzY3JpcHRpb24sCiAgICAgICAgICAgICAgICAgICAgICBleGVjdXRlVGltZTogdGFzay5leGVjdXRlVGltZSwKICAgICAgICAgICAgICAgICAgICAgIGV4ZWN1dGVEYXRlVGltZTogdGFzay5mcmVxdWVuY3kgPT09ICdvbmNlJyAmJiB0YXNrLmV4ZWN1dGVfZGF0ZSA/ICIiLmNvbmNhdCh0YXNrLmV4ZWN1dGVfZGF0ZSwgIiAiKS5jb25jYXQodGFzay5leGVjdXRlVGltZSkgOiAnJywKICAgICAgICAgICAgICAgICAgICAgIGZyZXF1ZW5jeTogdGFzay5mcmVxdWVuY3ksCiAgICAgICAgICAgICAgICAgICAgICBzdGF0dXM6IHRhc2suc3RhdHVzID09PSAncnVubmluZycgPyAncnVubmluZycgOiAncGVuZGluZycsCiAgICAgICAgICAgICAgICAgICAgICBwdXNoVXJsOiB0YXNrLnB1c2hVcmwKICAgICAgICAgICAgICAgICAgICB9OwogICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAocmVzcG9uc2UucmVjb3JkcyAmJiBBcnJheS5pc0FycmF5KHJlc3BvbnNlLnJlY29yZHMpKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzOS50aW1lZFRhc2tMaXN0ID0gcmVzcG9uc2UucmVjb3Jkcy5tYXAoZnVuY3Rpb24gKHRhc2spIHsKICAgICAgICAgICAgICAgICAgICByZXR1cm4gewogICAgICAgICAgICAgICAgICAgICAgaWQ6IHRhc2suaWQsCiAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlbWVudElkOiB0YXNrLnJlcXVpcmVtZW50SWQsCiAgICAgICAgICAgICAgICAgICAgICBuYW1lOiB0YXNrLnRhc2tOYW1lLAogICAgICAgICAgICAgICAgICAgICAgZGVzY3JpcHRpb246IHRhc2sudGFza0Rlc2NyaXB0aW9uLAogICAgICAgICAgICAgICAgICAgICAgZXhlY3V0ZVRpbWU6IHRhc2suZXhlY3V0ZVRpbWUsCiAgICAgICAgICAgICAgICAgICAgICBleGVjdXRlRGF0ZVRpbWU6IHRhc2suZnJlcXVlbmN5ID09PSAnb25jZScgJiYgdGFzay5leGVjdXRlX2RhdGUgPyAiIi5jb25jYXQodGFzay5leGVjdXRlX2RhdGUsICIgIikuY29uY2F0KHRhc2suZXhlY3V0ZVRpbWUpIDogJycsCiAgICAgICAgICAgICAgICAgICAgICBmcmVxdWVuY3k6IHRhc2suZnJlcXVlbmN5LAogICAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiB0YXNrLnN0YXR1cyA9PT0gJ3J1bm5pbmcnID8gJ3J1bm5pbmcnIDogJ3BlbmRpbmcnLAogICAgICAgICAgICAgICAgICAgICAgcHVzaFVybDogdGFzay5wdXNoVXJsCiAgICAgICAgICAgICAgICAgICAgfTsKICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICBfdGhpczkudGltZWRUYXNrTGlzdCA9IFtdOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICBfdGhpczkuJG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UubXNnIHx8ICfojrflj5blrprml7bku7vliqHliJfooajlpLHotKUnKTsKICAgICAgICAgICAgICAgIF90aGlzOS50aW1lZFRhc2tMaXN0ID0gW107CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF9jb250ZXh0Mi5uID0gMzsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSAyOgogICAgICAgICAgICAgIF9jb250ZXh0Mi5wID0gMjsKICAgICAgICAgICAgICBfdDIgPSBfY29udGV4dDIudjsKICAgICAgICAgICAgICBfdGhpczkuJG1lc3NhZ2UuZXJyb3IoJ+WKoOi9veWumuaXtuS7u+WKoeWIl+ihqOWksei0pScpOwogICAgICAgICAgICAgIF90aGlzOS50aW1lZFRhc2tMaXN0ID0gW107CiAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQyLmEoMik7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTIsIG51bGwsIFtbMCwgMl1dKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgLy8g5YWz6Zet5Yib5bu65Lu75Yqh5by556qXCiAgICBjbG9zZUNyZWF0ZVRhc2tEaWFsb2c6IGZ1bmN0aW9uIGNsb3NlQ3JlYXRlVGFza0RpYWxvZygpIHsKICAgICAgdGhpcy5jcmVhdGVUYXNrRGlhbG9nVmlzaWJsZSA9IGZhbHNlOwogICAgICB0aGlzLnJlc2V0VGFza0Zvcm0oKTsKICAgICAgdGhpcy5lZGl0aW5nVGFza0luZGV4ID0gLTE7IC8vIOmHjee9rue8lui+keeKtuaAgQogICAgfSwKICAgIC8vIOmHjee9ruS7u+WKoeihqOWNlQogICAgcmVzZXRUYXNrRm9ybTogZnVuY3Rpb24gcmVzZXRUYXNrRm9ybSgpIHsKICAgICAgdGhpcy50YXNrRm9ybSA9IHsKICAgICAgICByZXF1aXJlbWVudF9pZDogJycsCiAgICAgICAgbmFtZTogJycsCiAgICAgICAgZGVzY3JpcHRpb246ICcnLAogICAgICAgIGV4ZWN1dGVfdGltZTogJzE2OjAwJywKICAgICAgICBleGVjdXRlX2RhdGVfdGltZTogJycsCiAgICAgICAgZnJlcXVlbmN5OiAnZGFpbHknLAogICAgICAgIHB1c2hfdXJsOiAnJwogICAgICB9OwogICAgfSwKICAgIC8vIOS/neWtmOW5tui/kOihjOS7u+WKoQogICAgc2F2ZUFuZFJ1blRhc2s6IGZ1bmN0aW9uIHNhdmVBbmRSdW5UYXNrKCkgewogICAgICB2YXIgX3RoaXMwID0gdGhpczsKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoLyojX19QVVJFX18qLygwLCBfcmVnZW5lcmF0b3IyLmRlZmF1bHQpKCkubShmdW5jdGlvbiBfY2FsbGVlMygpIHsKICAgICAgICB2YXIgdGFza0RhdGEsIHJlc3BvbnNlLCB0YXNrLCBfcmVzcG9uc2UsIF90MzsKICAgICAgICByZXR1cm4gKDAsIF9yZWdlbmVyYXRvcjIuZGVmYXVsdCkoKS53KGZ1bmN0aW9uIChfY29udGV4dDMpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0My5uKSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBpZiAoX3RoaXMwLnZhbGlkYXRlVGFza0Zvcm0oKSkgewogICAgICAgICAgICAgICAgX2NvbnRleHQzLm4gPSAxOwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDMuYSgyKTsKICAgICAgICAgICAgY2FzZSAxOgogICAgICAgICAgICAgIF9jb250ZXh0My5wID0gMTsKICAgICAgICAgICAgICB0YXNrRGF0YSA9IF90aGlzMC5idWlsZFRhc2tEYXRhKCk7CiAgICAgICAgICAgICAgaWYgKCEoX3RoaXMwLmVkaXRpbmdUYXNrSW5kZXggPT09IC0xKSkgewogICAgICAgICAgICAgICAgX2NvbnRleHQzLm4gPSA4OwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF9jb250ZXh0My5uID0gMjsKICAgICAgICAgICAgICByZXR1cm4gKDAsIF9vcGluaW9uQW5hbHlzaXMuY3JlYXRlVGltZWRUYXNrKSh0YXNrRGF0YSk7CiAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICByZXNwb25zZSA9IF9jb250ZXh0My52OwogICAgICAgICAgICAgIGlmICghcmVzcG9uc2Uuc3VjY2VzcykgewogICAgICAgICAgICAgICAgX2NvbnRleHQzLm4gPSA2OwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF90aGlzMC4kbWVzc2FnZS5zdWNjZXNzKCfku7vliqHlt7Lkv53lrZjlubblvIDlp4vov5DooYwnKTsKICAgICAgICAgICAgICAvLyDph43mlrDliqDovb3ku7vliqHliJfooagKICAgICAgICAgICAgICBfY29udGV4dDMubiA9IDM7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzMC5sb2FkVGltZWRUYXNrTGlzdCgpOwogICAgICAgICAgICBjYXNlIDM6CiAgICAgICAgICAgICAgaWYgKCEocmVzcG9uc2UuZGF0YSAmJiByZXNwb25zZS5kYXRhLmlkKSkgewogICAgICAgICAgICAgICAgX2NvbnRleHQzLm4gPSA1OwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF9jb250ZXh0My5uID0gNDsKICAgICAgICAgICAgICByZXR1cm4gKDAsIF9vcGluaW9uQW5hbHlzaXMudXBkYXRlVGFza1N0YXR1cykocmVzcG9uc2UuZGF0YS5pZCwgJ3J1bm5pbmcnKTsKICAgICAgICAgICAgY2FzZSA0OgogICAgICAgICAgICAgIF9jb250ZXh0My5uID0gNTsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXMwLmxvYWRUaW1lZFRhc2tMaXN0KCk7CiAgICAgICAgICAgIGNhc2UgNToKICAgICAgICAgICAgICBfY29udGV4dDMubiA9IDc7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgNjoKICAgICAgICAgICAgICBfdGhpczAuJG1lc3NhZ2UuZXJyb3IoJ+WIm+W7uuS7u+WKoeWksei0pe+8micgKyByZXNwb25zZS5tc2cpOwogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDMuYSgyKTsKICAgICAgICAgICAgY2FzZSA3OgogICAgICAgICAgICAgIF9jb250ZXh0My5uID0gMTM7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgODoKICAgICAgICAgICAgICAvLyDnvJbovpHku7vliqEKICAgICAgICAgICAgICB0YXNrID0gX3RoaXMwLnRpbWVkVGFza0xpc3RbX3RoaXMwLmVkaXRpbmdUYXNrSW5kZXhdOwogICAgICAgICAgICAgIF9jb250ZXh0My5uID0gOTsKICAgICAgICAgICAgICByZXR1cm4gKDAsIF9vcGluaW9uQW5hbHlzaXMudXBkYXRlVGltZWRUYXNrKSh0YXNrLmlkLCB0YXNrRGF0YSk7CiAgICAgICAgICAgIGNhc2UgOToKICAgICAgICAgICAgICBfcmVzcG9uc2UgPSBfY29udGV4dDMudjsKICAgICAgICAgICAgICBpZiAoIV9yZXNwb25zZS5zdWNjZXNzKSB7CiAgICAgICAgICAgICAgICBfY29udGV4dDMubiA9IDEyOwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF9jb250ZXh0My5uID0gMTA7CiAgICAgICAgICAgICAgcmV0dXJuICgwLCBfb3BpbmlvbkFuYWx5c2lzLnVwZGF0ZVRhc2tTdGF0dXMpKHRhc2suaWQsICdydW5uaW5nJyk7CiAgICAgICAgICAgIGNhc2UgMTA6CiAgICAgICAgICAgICAgX3RoaXMwLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S7u+WKoeW3suabtOaWsOW5tuW8gOWni+i/kOihjCcpOwogICAgICAgICAgICAgIC8vIOmHjeaWsOWKoOi9veS7u+WKoeWIl+ihqAogICAgICAgICAgICAgIF9jb250ZXh0My5uID0gMTE7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzMC5sb2FkVGltZWRUYXNrTGlzdCgpOwogICAgICAgICAgICBjYXNlIDExOgogICAgICAgICAgICAgIF9jb250ZXh0My5uID0gMTM7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgMTI6CiAgICAgICAgICAgICAgX3RoaXMwLiRtZXNzYWdlLmVycm9yKCfmm7TmlrDku7vliqHlpLHotKXvvJonICsgX3Jlc3BvbnNlLm1zZyk7CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0My5hKDIpOwogICAgICAgICAgICBjYXNlIDEzOgogICAgICAgICAgICAgIF90aGlzMC5jcmVhdGVUYXNrRGlhbG9nVmlzaWJsZSA9IGZhbHNlOwogICAgICAgICAgICAgIF90aGlzMC5yZXNldFRhc2tGb3JtKCk7CiAgICAgICAgICAgICAgX3RoaXMwLmVkaXRpbmdUYXNrSW5kZXggPSAtMTsgLy8g6YeN572u57yW6L6R54q25oCBCiAgICAgICAgICAgICAgX2NvbnRleHQzLm4gPSAxNTsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSAxNDoKICAgICAgICAgICAgICBfY29udGV4dDMucCA9IDE0OwogICAgICAgICAgICAgIF90MyA9IF9jb250ZXh0My52OwogICAgICAgICAgICAgIF90aGlzMC4kbWVzc2FnZS5lcnJvcign5L+d5a2Y5Lu75Yqh5aSx6LSl77yM6K+36YeN6K+VJyk7CiAgICAgICAgICAgIGNhc2UgMTU6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0My5hKDIpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUzLCBudWxsLCBbWzEsIDE0XV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvLyDkv53lrZjku7vliqHorqHliJIKICAgIHNhdmVUYXNrUGxhbjogZnVuY3Rpb24gc2F2ZVRhc2tQbGFuKCkgewogICAgICB2YXIgX3RoaXMxID0gdGhpczsKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoLyojX19QVVJFX18qLygwLCBfcmVnZW5lcmF0b3IyLmRlZmF1bHQpKCkubShmdW5jdGlvbiBfY2FsbGVlNCgpIHsKICAgICAgICB2YXIgdGFza0RhdGEsIHJlc3BvbnNlLCB0YXNrLCBfcmVzcG9uc2UyLCBfdDQ7CiAgICAgICAgcmV0dXJuICgwLCBfcmVnZW5lcmF0b3IyLmRlZmF1bHQpKCkudyhmdW5jdGlvbiAoX2NvbnRleHQ0KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDQubikgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgaWYgKF90aGlzMS52YWxpZGF0ZVRhc2tGb3JtKCkpIHsKICAgICAgICAgICAgICAgIF9jb250ZXh0NC5uID0gMTsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ0LmEoMik7CiAgICAgICAgICAgIGNhc2UgMToKICAgICAgICAgICAgICBfY29udGV4dDQucCA9IDE7CiAgICAgICAgICAgICAgdGFza0RhdGEgPSBfdGhpczEuYnVpbGRUYXNrRGF0YSgpOwogICAgICAgICAgICAgIGlmICghKF90aGlzMS5lZGl0aW5nVGFza0luZGV4ID09PSAtMSkpIHsKICAgICAgICAgICAgICAgIF9jb250ZXh0NC5uID0gNjsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfY29udGV4dDQubiA9IDI7CiAgICAgICAgICAgICAgcmV0dXJuICgwLCBfb3BpbmlvbkFuYWx5c2lzLmNyZWF0ZVRpbWVkVGFzaykodGFza0RhdGEpOwogICAgICAgICAgICBjYXNlIDI6CiAgICAgICAgICAgICAgcmVzcG9uc2UgPSBfY29udGV4dDQudjsKICAgICAgICAgICAgICBpZiAoIXJlc3BvbnNlLnN1Y2Nlc3MpIHsKICAgICAgICAgICAgICAgIF9jb250ZXh0NC5uID0gNDsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfdGhpczEuJG1lc3NhZ2Uuc3VjY2Vzcygn5Lu75Yqh6K6h5YiS5bey5L+d5a2YJyk7CiAgICAgICAgICAgICAgLy8g6YeN5paw5Yqg6L295Lu75Yqh5YiX6KGoCiAgICAgICAgICAgICAgX2NvbnRleHQ0Lm4gPSAzOwogICAgICAgICAgICAgIHJldHVybiBfdGhpczEubG9hZFRpbWVkVGFza0xpc3QoKTsKICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgIF9jb250ZXh0NC5uID0gNTsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSA0OgogICAgICAgICAgICAgIF90aGlzMS4kbWVzc2FnZS5lcnJvcign5L+d5a2Y5Lu75Yqh6K6h5YiS5aSx6LSl77yaJyArIHJlc3BvbnNlLm1zZyk7CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0NC5hKDIpOwogICAgICAgICAgICBjYXNlIDU6CiAgICAgICAgICAgICAgX2NvbnRleHQ0Lm4gPSAxMDsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSA2OgogICAgICAgICAgICAgIC8vIOe8lui+keS7u+WKoQogICAgICAgICAgICAgIHRhc2sgPSBfdGhpczEudGltZWRUYXNrTGlzdFtfdGhpczEuZWRpdGluZ1Rhc2tJbmRleF07CiAgICAgICAgICAgICAgX2NvbnRleHQ0Lm4gPSA3OwogICAgICAgICAgICAgIHJldHVybiAoMCwgX29waW5pb25BbmFseXNpcy51cGRhdGVUaW1lZFRhc2spKHRhc2suaWQsIHRhc2tEYXRhKTsKICAgICAgICAgICAgY2FzZSA3OgogICAgICAgICAgICAgIF9yZXNwb25zZTIgPSBfY29udGV4dDQudjsKICAgICAgICAgICAgICBpZiAoIV9yZXNwb25zZTIuc3VjY2VzcykgewogICAgICAgICAgICAgICAgX2NvbnRleHQ0Lm4gPSA5OwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF90aGlzMS4kbWVzc2FnZS5zdWNjZXNzKCfku7vliqHorqHliJLlt7Lmm7TmlrAnKTsKICAgICAgICAgICAgICAvLyDph43mlrDliqDovb3ku7vliqHliJfooagKICAgICAgICAgICAgICBfY29udGV4dDQubiA9IDg7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzMS5sb2FkVGltZWRUYXNrTGlzdCgpOwogICAgICAgICAgICBjYXNlIDg6CiAgICAgICAgICAgICAgX2NvbnRleHQ0Lm4gPSAxMDsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSA5OgogICAgICAgICAgICAgIF90aGlzMS4kbWVzc2FnZS5lcnJvcign5pu05paw5Lu75Yqh6K6h5YiS5aSx6LSl77yaJyArIF9yZXNwb25zZTIubXNnKTsKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ0LmEoMik7CiAgICAgICAgICAgIGNhc2UgMTA6CiAgICAgICAgICAgICAgX3RoaXMxLmNyZWF0ZVRhc2tEaWFsb2dWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICAgICAgX3RoaXMxLnJlc2V0VGFza0Zvcm0oKTsKICAgICAgICAgICAgICBfdGhpczEuZWRpdGluZ1Rhc2tJbmRleCA9IC0xOyAvLyDph43nva7nvJbovpHnirbmgIEKICAgICAgICAgICAgICBfY29udGV4dDQubiA9IDEyOwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDExOgogICAgICAgICAgICAgIF9jb250ZXh0NC5wID0gMTE7CiAgICAgICAgICAgICAgX3Q0ID0gX2NvbnRleHQ0LnY7CiAgICAgICAgICAgICAgX3RoaXMxLiRtZXNzYWdlLmVycm9yKCfkv53lrZjku7vliqHorqHliJLlpLHotKXvvIzor7fph43or5UnKTsKICAgICAgICAgICAgY2FzZSAxMjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ0LmEoMik7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTQsIG51bGwsIFtbMSwgMTFdXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8vIOmqjOivgeS7u+WKoeihqOWNlQogICAgdmFsaWRhdGVUYXNrRm9ybTogZnVuY3Rpb24gdmFsaWRhdGVUYXNrRm9ybSgpIHsKICAgICAgaWYgKCF0aGlzLnRhc2tGb3JtLnJlcXVpcmVtZW50X2lkKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7fpgInmi6nopoHlhbPogZTnmoToiIbmg4XliIbmnpDku7vliqEnKTsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KICAgICAgaWYgKCF0aGlzLnRhc2tGb3JtLm5hbWUgfHwgIXRoaXMudGFza0Zvcm0ubmFtZS50cmltKCkpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+i+k+WFpeS7u+WKoeWQjeensCcpOwogICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgfQogICAgICBpZiAoIXRoaXMudGFza0Zvcm0uZGVzY3JpcHRpb24gfHwgIXRoaXMudGFza0Zvcm0uZGVzY3JpcHRpb24udHJpbSgpKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7fovpPlhaXku7vliqHmj4/ov7AnKTsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KCiAgICAgIC8vIOmqjOivgeaJp+ihjOaXtumXtAogICAgICBpZiAodGhpcy50YXNrRm9ybS5mcmVxdWVuY3kgPT09ICdvbmNlJykgewogICAgICAgIGlmICghdGhpcy50YXNrRm9ybS5leGVjdXRlX2RhdGVfdGltZSkgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7fpgInmi6nmiafooYzml6XmnJ/lkozml7bpl7QnKTsKICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgICB9CiAgICAgICAgLy8g5qOA5p+l5piv5ZCm5piv5pyq5p2l5pe26Ze0CiAgICAgICAgdmFyIGV4ZWN1dGVUaW1lID0gbmV3IERhdGUodGhpcy50YXNrRm9ybS5leGVjdXRlX2RhdGVfdGltZSk7CiAgICAgICAgdmFyIG5vdyA9IG5ldyBEYXRlKCk7CiAgICAgICAgaWYgKGV4ZWN1dGVUaW1lIDw9IG5vdykgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfmiafooYzml7bpl7Tlv4XpobvmmK/mnKrmnaXml7bpl7QnKTsKICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgICB9CiAgICAgIH0gZWxzZSB7CiAgICAgICAgaWYgKCF0aGlzLnRhc2tGb3JtLmV4ZWN1dGVfdGltZSkgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7fpgInmi6nmiafooYzml7bpl7QnKTsKICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgICB9CiAgICAgIH0KICAgICAgcmV0dXJuIHRydWU7CiAgICB9LAogICAgLy8g5p6E5bu65Lu75Yqh5pWw5o2uCiAgICBidWlsZFRhc2tEYXRhOiBmdW5jdGlvbiBidWlsZFRhc2tEYXRhKCkgewogICAgICAvLyDlpITnkIbmiafooYzml7bpl7TvvJrlr7nkuo7kuIDmrKHmgKfku7vliqHvvIzlj6rmj5Dlj5bml7bpl7Tpg6jliIbvvJvlr7nkuo7lkajmnJ/mgKfku7vliqHvvIznm7TmjqXkvb/nlKjml7bpl7QKICAgICAgdmFyIGV4ZWN1dGVUaW1lOwogICAgICBpZiAodGhpcy50YXNrRm9ybS5mcmVxdWVuY3kgPT09ICdvbmNlJykgewogICAgICAgIC8vIOS4gOasoeaAp+S7u+WKoe+8muS7juWujOaVtOaXpeacn+aXtumXtOS4reaPkOWPluaXtumXtOmDqOWIhiAoSEg6TU0pCiAgICAgICAgaWYgKHRoaXMudGFza0Zvcm0uZXhlY3V0ZV9kYXRlX3RpbWUpIHsKICAgICAgICAgIHZhciBkYXRlVGltZSA9IG5ldyBEYXRlKHRoaXMudGFza0Zvcm0uZXhlY3V0ZV9kYXRlX3RpbWUpOwogICAgICAgICAgZXhlY3V0ZVRpbWUgPSAiIi5jb25jYXQoZGF0ZVRpbWUuZ2V0SG91cnMoKS50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJyksICI6IikuY29uY2F0KGRhdGVUaW1lLmdldE1pbnV0ZXMoKS50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJykpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBleGVjdXRlVGltZSA9ICcwOTowMCc7IC8vIOm7mOiupOaXtumXtAogICAgICAgIH0KICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDlkajmnJ/mgKfku7vliqHvvJrnm7TmjqXkvb/nlKjml7bpl7QKICAgICAgICBleGVjdXRlVGltZSA9IHRoaXMudGFza0Zvcm0uZXhlY3V0ZV90aW1lIHx8ICcwOTowMCc7CiAgICAgIH0KICAgICAgdmFyIHRhc2tEYXRhID0gewogICAgICAgIHJlcXVpcmVtZW50X2lkOiB0aGlzLnRhc2tGb3JtLnJlcXVpcmVtZW50X2lkLAogICAgICAgIHRhc2tfbmFtZTogdGhpcy50YXNrRm9ybS5uYW1lLAogICAgICAgIHRhc2tfdHlwZTogJ3NjaGVkdWxlZCcsCiAgICAgICAgZnJlcXVlbmN5OiB0aGlzLnRhc2tGb3JtLmZyZXF1ZW5jeSwKICAgICAgICAvLyDkvb/nlKggZnJlcXVlbmN5IOiAjOS4jeaYryBzY2hlZHVsZV90eXBlCiAgICAgICAgZXhlY3V0ZV90aW1lOiBleGVjdXRlVGltZSwKICAgICAgICAvLyDlj6rlrZjlgqjml7bpl7Tpg6jliIYgKEhIOk1NKQogICAgICAgIHRhc2tfZGVzY3JpcHRpb246IHRoaXMudGFza0Zvcm0uZGVzY3JpcHRpb24sCiAgICAgICAgcHVzaF91cmw6IHRoaXMudGFza0Zvcm0ucHVzaF91cmwgfHwgJycKICAgICAgfTsKCiAgICAgIC8vIOWvueS6juS4gOasoeaAp+S7u+WKoe+8jOWPr+iDvemcgOimgemineWklueahOaXpeacn+S/oeaBrwogICAgICBpZiAodGhpcy50YXNrRm9ybS5mcmVxdWVuY3kgPT09ICdvbmNlJyAmJiB0aGlzLnRhc2tGb3JtLmV4ZWN1dGVfZGF0ZV90aW1lKSB7CiAgICAgICAgdGFza0RhdGFbJ2V4ZWN1dGVfZGF0ZSddID0gdGhpcy50YXNrRm9ybS5leGVjdXRlX2RhdGVfdGltZS5zcGxpdCgnICcpWzBdOyAvLyDmj5Dlj5bml6XmnJ/pg6jliIYgKFlZWVktTU0tREQpCiAgICAgIH0KICAgICAgcmV0dXJuIHRhc2tEYXRhOwogICAgfSwKICAgIC8vIOWIoOmZpOS7u+WKoQogICAgZGVsZXRlVGFzazogZnVuY3Rpb24gZGVsZXRlVGFzayhpbmRleCkgewogICAgICB2YXIgX3RoaXMxMCA9IHRoaXM7CiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKC8qI19fUFVSRV9fKi8oMCwgX3JlZ2VuZXJhdG9yMi5kZWZhdWx0KSgpLm0oZnVuY3Rpb24gX2NhbGxlZTUoKSB7CiAgICAgICAgdmFyIHRhc2ssIHJlc3BvbnNlLCBfdDU7CiAgICAgICAgcmV0dXJuICgwLCBfcmVnZW5lcmF0b3IyLmRlZmF1bHQpKCkudyhmdW5jdGlvbiAoX2NvbnRleHQ1KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDUubikgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX2NvbnRleHQ1LnAgPSAwOwogICAgICAgICAgICAgIHRhc2sgPSBfdGhpczEwLnRpbWVkVGFza0xpc3RbaW5kZXhdOwogICAgICAgICAgICAgIGlmICh0YXNrKSB7CiAgICAgICAgICAgICAgICBfY29udGV4dDUubiA9IDE7CiAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgX3RoaXMxMC4kbWVzc2FnZS5lcnJvcign5Lu75Yqh5pWw5o2u5LiN5a2Y5ZyoJyk7CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0NS5hKDIpOwogICAgICAgICAgICBjYXNlIDE6CiAgICAgICAgICAgICAgX2NvbnRleHQ1Lm4gPSAyOwogICAgICAgICAgICAgIHJldHVybiBfdGhpczEwLiRjb25maXJtKCJcdTc4NkVcdTVCOUFcdTg5ODFcdTUyMjBcdTk2NjRcdTRFRkJcdTUyQTFcdTMwMEMiLmNvbmNhdCh0YXNrLm5hbWUsICJcdTMwMERcdTU0MTdcdUZGMUYiKSwgJ+aPkOekuicsIHsKICAgICAgICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICBfY29udGV4dDUubiA9IDM7CiAgICAgICAgICAgICAgcmV0dXJuICgwLCBfb3BpbmlvbkFuYWx5c2lzLmRlbGV0ZVRpbWVkVGFzaykodGFzay5pZCk7CiAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICByZXNwb25zZSA9IF9jb250ZXh0NS52OwogICAgICAgICAgICAgIGlmICghcmVzcG9uc2Uuc3VjY2VzcykgewogICAgICAgICAgICAgICAgX2NvbnRleHQ1Lm4gPSA1OwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF90aGlzMTAuJG1lc3NhZ2Uuc3VjY2Vzcygn5Yig6Zmk5oiQ5YqfJyk7CiAgICAgICAgICAgICAgLy8g6YeN5paw5Yqg6L295Lu75Yqh5YiX6KGoCiAgICAgICAgICAgICAgX2NvbnRleHQ1Lm4gPSA0OwogICAgICAgICAgICAgIHJldHVybiBfdGhpczEwLmxvYWRUaW1lZFRhc2tMaXN0KCk7CiAgICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgICBfY29udGV4dDUubiA9IDY7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgNToKICAgICAgICAgICAgICBfdGhpczEwLiRtZXNzYWdlLmVycm9yKCfliKDpmaTlpLHotKXvvJonICsgKHJlc3BvbnNlLm1zZyB8fCByZXNwb25zZS5tZXNzYWdlIHx8ICfmnKrnn6XplJnor68nKSk7CiAgICAgICAgICAgIGNhc2UgNjoKICAgICAgICAgICAgICBfY29udGV4dDUubiA9IDk7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgNzoKICAgICAgICAgICAgICBfY29udGV4dDUucCA9IDc7CiAgICAgICAgICAgICAgX3Q1ID0gX2NvbnRleHQ1LnY7CiAgICAgICAgICAgICAgaWYgKCEoX3Q1ID09PSAnY2FuY2VsJykpIHsKICAgICAgICAgICAgICAgIF9jb250ZXh0NS5uID0gODsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ1LmEoMik7CiAgICAgICAgICAgIGNhc2UgODoKICAgICAgICAgICAgICBfdGhpczEwLiRtZXNzYWdlLmVycm9yKCfliKDpmaTku7vliqHlpLHotKXvvIzor7fph43or5UnKTsKICAgICAgICAgICAgY2FzZSA5OgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDUuYSgyKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlNSwgbnVsbCwgW1swLCA3XV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvLyDnvJbovpHku7vliqEKICAgIGVkaXRUYXNrOiBmdW5jdGlvbiBlZGl0VGFzayhpbmRleCkgewogICAgICB2YXIgdGFzayA9IHRoaXMudGltZWRUYXNrTGlzdFtpbmRleF07CiAgICAgIGlmICghdGFzaykgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+S7u+WKoeaVsOaNruS4jeWtmOWcqCcpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICB0aGlzLmVkaXRpbmdUYXNrSW5kZXggPSBpbmRleDsKICAgICAgdGhpcy50YXNrRm9ybSA9IHsKICAgICAgICByZXF1aXJlbWVudF9pZDogdGFzay5yZXF1aXJlbWVudElkIHx8ICcnLAogICAgICAgIG5hbWU6IHRhc2submFtZSB8fCAnJywKICAgICAgICBkZXNjcmlwdGlvbjogdGFzay5kZXNjcmlwdGlvbiB8fCAnJywKICAgICAgICBleGVjdXRlX3RpbWU6IHRhc2suZXhlY3V0ZVRpbWUgfHwgJzE2OjAwJywKICAgICAgICBleGVjdXRlX2RhdGVfdGltZTogdGFzay5leGVjdXRlRGF0ZVRpbWUgfHwgJycsCiAgICAgICAgZnJlcXVlbmN5OiB0YXNrLmZyZXF1ZW5jeSB8fCAnZGFpbHknLAogICAgICAgIHB1c2hfdXJsOiB0YXNrLnB1c2hVcmwgfHwgJycKICAgICAgfTsKICAgICAgdGhpcy5sb2FkT3BpbmlvblRhc2tMaXN0KCk7IC8vIOWKoOi9veiIhuaDheS7u+WKoeWIl+ihqAogICAgICB0aGlzLmNyZWF0ZVRhc2tEaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICAvLyDliIfmjaLku7vliqHnirbmgIEKICAgIHRvZ2dsZVRhc2tTdGF0dXM6IGZ1bmN0aW9uIHRvZ2dsZVRhc2tTdGF0dXMoaW5kZXgpIHsKICAgICAgdmFyIF90aGlzMTEgPSB0aGlzOwogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSgvKiNfX1BVUkVfXyovKDAsIF9yZWdlbmVyYXRvcjIuZGVmYXVsdCkoKS5tKGZ1bmN0aW9uIF9jYWxsZWU2KCkgewogICAgICAgIHZhciB0YXNrLCBuZXdTdGF0dXMsIHJlc3BvbnNlLCBfdDY7CiAgICAgICAgcmV0dXJuICgwLCBfcmVnZW5lcmF0b3IyLmRlZmF1bHQpKCkudyhmdW5jdGlvbiAoX2NvbnRleHQ2KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDYubikgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX2NvbnRleHQ2LnAgPSAwOwogICAgICAgICAgICAgIHRhc2sgPSBfdGhpczExLnRpbWVkVGFza0xpc3RbaW5kZXhdOwogICAgICAgICAgICAgIG5ld1N0YXR1cyA9IHRhc2suc3RhdHVzID09PSAncnVubmluZycgPyAncGVuZGluZycgOiAncnVubmluZyc7CiAgICAgICAgICAgICAgX2NvbnRleHQ2Lm4gPSAxOwogICAgICAgICAgICAgIHJldHVybiAoMCwgX29waW5pb25BbmFseXNpcy51cGRhdGVUYXNrU3RhdHVzKSh0YXNrLmlkLCBuZXdTdGF0dXMpOwogICAgICAgICAgICBjYXNlIDE6CiAgICAgICAgICAgICAgcmVzcG9uc2UgPSBfY29udGV4dDYudjsKICAgICAgICAgICAgICBpZiAoIXJlc3BvbnNlLnN1Y2Nlc3MpIHsKICAgICAgICAgICAgICAgIF9jb250ZXh0Ni5uID0gMzsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBpZiAobmV3U3RhdHVzID09PSAncnVubmluZycpIHsKICAgICAgICAgICAgICAgIF90aGlzMTEuJG1lc3NhZ2Uuc3VjY2VzcygiXHU0RUZCXHU1MkExXHUzMDBDIi5jb25jYXQodGFzay5uYW1lLCAiXHUzMDBEXHU1REYyXHU1NDJGXHU1MkE4IikpOwogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICBfdGhpczExLiRtZXNzYWdlLmluZm8oIlx1NEVGQlx1NTJBMVx1MzAwQyIuY29uY2F0KHRhc2submFtZSwgIlx1MzAwRFx1NURGMlx1NjY4Mlx1NTA1QyIpKTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgLy8g6YeN5paw5Yqg6L295Lu75Yqh5YiX6KGoCiAgICAgICAgICAgICAgX2NvbnRleHQ2Lm4gPSAyOwogICAgICAgICAgICAgIHJldHVybiBfdGhpczExLmxvYWRUaW1lZFRhc2tMaXN0KCk7CiAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICBfY29udGV4dDYubiA9IDQ7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICBfdGhpczExLiRtZXNzYWdlLmVycm9yKCfnirbmgIHmm7TmlrDlpLHotKXvvJonICsgcmVzcG9uc2UubXNnKTsKICAgICAgICAgICAgY2FzZSA0OgogICAgICAgICAgICAgIF9jb250ZXh0Ni5uID0gNjsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSA1OgogICAgICAgICAgICAgIF9jb250ZXh0Ni5wID0gNTsKICAgICAgICAgICAgICBfdDYgPSBfY29udGV4dDYudjsKICAgICAgICAgICAgICBfdGhpczExLiRtZXNzYWdlLmVycm9yKCfnirbmgIHmm7TmlrDlpLHotKXvvIzor7fph43or5UnKTsKICAgICAgICAgICAgY2FzZSA2OgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDYuYSgyKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlNiwgbnVsbCwgW1swLCA1XV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvLyDpooTop4jku7vliqEKICAgIHByZXZpZXdUYXNrOiBmdW5jdGlvbiBwcmV2aWV3VGFzayhpbmRleCkgewogICAgICB2YXIgdGFzayA9IHRoaXMudGltZWRUYXNrTGlzdFtpbmRleF07CiAgICAgIGlmICghdGFzaykgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+S7u+WKoeaVsOaNruS4jeWtmOWcqCcpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICB0aGlzLnRhc2tQcmV2aWV3RGlhbG9nLnRhc2tEYXRhID0gdGFzazsKICAgICAgdGhpcy50YXNrUHJldmlld0RpYWxvZy52aXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICAvLyDojrflj5bku7vliqHorqHliJLmlofmnKwKICAgIGdldFRhc2tTY2hlZHVsZVRleHQ6IGZ1bmN0aW9uIGdldFRhc2tTY2hlZHVsZVRleHQodGFzaykgewogICAgICBpZiAoIXRhc2spIHJldHVybiAnJzsKICAgICAgaWYgKHRhc2suZnJlcXVlbmN5ID09PSAnb25jZScpIHsKICAgICAgICAvLyDkuIDmrKHmgKfku7vliqHmmL7npLrlhbfkvZPmiafooYzml7bpl7QKICAgICAgICByZXR1cm4gIlx1NEVDNVx1NEUwMFx1NkIyMSAiLmNvbmNhdCh0YXNrLmV4ZWN1dGVEYXRlVGltZSB8fCB0YXNrLmV4ZWN1dGVUaW1lKTsKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDlkajmnJ/mgKfku7vliqHmmL7npLrpopHnjoflkozml7bpl7QKICAgICAgICB2YXIgZnJlcXVlbmN5VGV4dCA9IHRoaXMuZ2V0RnJlcXVlbmN5VGV4dCh0YXNrLmZyZXF1ZW5jeSk7CiAgICAgICAgcmV0dXJuICIiLmNvbmNhdChmcmVxdWVuY3lUZXh0LCAiICIpLmNvbmNhdCh0YXNrLmV4ZWN1dGVUaW1lKTsKICAgICAgfQogICAgfSwKICAgIC8vIOiOt+WPlumikeeOh+aWh+acrAogICAgZ2V0RnJlcXVlbmN5VGV4dDogZnVuY3Rpb24gZ2V0RnJlcXVlbmN5VGV4dChmcmVxdWVuY3kpIHsKICAgICAgdmFyIGZyZXF1ZW5jeU1hcCA9IHsKICAgICAgICAnb25jZSc6ICfku4XkuIDmrKEnLAogICAgICAgICdkYWlseSc6ICfmr4/lpKknLAogICAgICAgICd3ZWVrbHknOiAn5q+P5ZGoJywKICAgICAgICAnbW9udGhseSc6ICfmr4/mnIgnCiAgICAgIH07CiAgICAgIHJldHVybiBmcmVxdWVuY3lNYXBbZnJlcXVlbmN5XSB8fCBmcmVxdWVuY3k7CiAgICB9LAogICAgLy8g5L+u5pS56K6h5YiSCiAgICBtb2RpZnlQbGFuOiBmdW5jdGlvbiBtb2RpZnlQbGFuKCkgewogICAgICAvLyDov5nph4zlj6/ku6Xmt7vliqDkv67mlLnorqHliJLnmoTpgLvovpEKICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCfkv67mlLnorqHliJLlip/og73lvoXlrp7njrAnKTsKICAgIH0sCiAgICAvKiog5pe26Ze05qC85byP5YyWICovCiAgICBwYXJzZVRpbWU6IF9ydW95aS5wYXJzZVRpbWUKICB9Cn07"}, {"version": 3, "names": ["_opinionAnalysis", "require", "_ruoyi", "_default", "exports", "default", "name", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "recordList", "date<PERSON><PERSON><PERSON>", "queryParams", "page_num", "page_size", "requirementName", "entityKeyword", "analysisStatus", "logDialogVisible", "logLoading", "logList", "reportDialogVisible", "reportLoading", "reportData", "pushDialogVisible", "pushLoading", "pushForm", "push_url", "push_content", "pushRules", "required", "message", "trigger", "schedulePushDialogVisible", "schedulePushLoading", "schedulePushForm", "push_time", "frequency", "schedulePushRules", "currentRecord", "timedTaskDialogVisible", "timedTaskList", "createTaskDialogVisible", "editingTaskIndex", "taskForm", "requirement_id", "description", "execute_time", "execute_date_time", "requirementList", "taskPreviewDialog", "visible", "taskData", "created", "getList", "methods", "paginationParams", "_this", "page", "limit", "params", "trim", "task_name", "task_description", "undefined", "statusMap", "status", "length", "create_time_start", "create_time_end", "console", "log", "getAnalysisTaskList", "then", "response", "requirements", "records", "rows", "Array", "isArray", "filter", "item", "id", "map", "parseInt", "isNaN", "mappedItem", "taskName", "taskDescription", "specificRequirement", "createTime", "create_time", "mapTaskStatus", "reportOssUrl", "report_oss_url", "catch", "error", "$modal", "msgError", "taskStatus", "mapAnalysisStatus", "onRequirementNameInput", "value", "onEntityKeywordInput", "onAnalysisStatusChange", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleSelectionChange", "selection", "handleViewLogs", "row", "_this2", "getAnalysisProgress", "taskId", "handleViewReport", "_this3", "setTimeout", "totalArticles", "Math", "floor", "random", "sentiment", "positive", "neutral", "negative", "handleReport", "window", "open", "msg", "handlePushNow", "concat", "handleSchedulePush", "submitPush", "_this4", "$refs", "validate", "valid", "pushData", "target_url", "report_data", "dataSources", "analysis_results", "summary", "reportUrl", "push_type", "pushReport", "success", "msgSuccess", "report_url", "submitSchedulePush", "_this5", "createTimedTask", "handleDelete", "_this6", "names", "Event", "includes", "join", "confirm", "deleteAnalysisTask", "batchDeleteAnalysisTasks", "currentPageRecords", "deletedCount", "detail", "details", "d", "JSON", "stringify", "handleExport", "_this7", "requirement_name", "entity_keyword", "analysis_status", "exportRequirements", "blob", "Blob", "type", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "Date", "toISOString", "slice", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleExportReport", "getStatusTagType", "getStatusText", "getLogLevelTagType", "level", "levelMap", "handleTimedPush", "loadTimedTaskList", "closeTimedTaskDialog", "handleCreateTimedTask", "resetTaskForm", "loadOpinionTaskList", "handleAddTimedTask", "_this8", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "tasks", "_t", "w", "_context", "n", "p", "v", "task", "keywords", "specific_requirement", "$message", "a", "_this9", "_callee2", "_t2", "_context2", "getTimedTaskList", "requirementId", "executeTime", "executeDateTime", "execute_date", "pushUrl", "closeCreateTaskDialog", "saveAndRunTask", "_this0", "_callee3", "_response", "_t3", "_context3", "validateTaskForm", "buildTaskData", "updateTaskStatus", "updateTimedTask", "saveTaskPlan", "_this1", "_callee4", "_response2", "_t4", "_context4", "warning", "now", "dateTime", "getHours", "toString", "padStart", "getMinutes", "task_type", "split", "deleteTask", "index", "_this10", "_callee5", "_t5", "_context5", "$confirm", "confirmButtonText", "cancelButtonText", "deleteTimedTask", "editTask", "toggleTaskStatus", "_this11", "_callee6", "newStatus", "_t6", "_context6", "info", "previewTask", "getTaskScheduleText", "frequencyText", "getFrequencyText", "frequencyMap", "modifyPlan", "parseTime"], "sources": ["src/views/analyze-record/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 搜索区域 -->\r\n    <el-form v-show=\"showSearch\" ref=\"queryForm\" :model=\"queryParams\" size=\"small\" :inline=\"true\" label-width=\"68px\">\r\n\r\n      <el-form-item label=\"需求名称\" prop=\"requirementName\">\r\n        <el-input\r\n          id=\"requirementName\"\r\n          v-model=\"queryParams.requirementName\"\r\n          placeholder=\"请输入需求名称\"\r\n          clearable\r\n          @input=\"onRequirementNameInput\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"关键词\" prop=\"entityKeyword\">\r\n        <el-input\r\n          id=\"entityKeyword\"\r\n          v-model=\"queryParams.entityKeyword\"\r\n          placeholder=\"请输入实体关键词\"\r\n          clearable\r\n          @input=\"onEntityKeywordInput\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"分析状态\" prop=\"analysisStatus\">\r\n        <el-select\r\n          id=\"analysisStatus\"\r\n          v-model=\"queryParams.analysisStatus\"\r\n          placeholder=\"请选择分析状态\"\r\n          clearable\r\n          @change=\"onAnalysisStatusChange\"\r\n        >\r\n          <el-option label=\"未开始\" value=\"0\" />\r\n          <el-option label=\"分析中\" value=\"1\" />\r\n          <el-option label=\"已完成\" value=\"2\" />\r\n          <el-option label=\"失败\" value=\"3\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"创建时间\">\r\n        <el-date-picker\r\n          id=\"create_time_range\"\r\n          v-model=\"dateRange\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 操作按钮区域 -->\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          v-hasPermi=\"['opinion:record:export']\"\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-time\"\r\n          size=\"mini\"\r\n          @click=\"handleTimedPush\"\r\n        >定时推送</el-button>\r\n      </el-col>\r\n      <right-toolbar :show-search.sync=\"showSearch\" @queryTable=\"getList\" />\r\n    </el-row>\r\n\r\n    <!-- 数据表格 -->\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"recordList\"\r\n      :default-sort=\"{prop: 'createTime', order: 'descending'}\"\r\n      @selection-change=\"handleSelectionChange\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"ID\" align=\"center\" prop=\"id\" width=\"80\" />\r\n      <el-table-column label=\"需求名称\" align=\"center\" prop=\"requirementName\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"关键词\" align=\"center\" prop=\"entityKeyword\" width=\"120\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"具体需求\" align=\"center\" prop=\"specificRequirement\" width=\"200\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"生成状态\" align=\"center\" prop=\"status\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            :type=\"getStatusTagType(scope.row.status)\"\r\n            size=\"mini\"\r\n          >\r\n            {{ getStatusText(scope.row.status) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"380\">\r\n        <template slot-scope=\"scope\">\r\n\r\n          <el-button\r\n            v-if=\"scope.row.status === 2\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-notebook-1\"\r\n            @click=\"handleReport(scope.row)\"\r\n          >报告</el-button>\r\n          <el-button\r\n            v-if=\"scope.row.status === 2\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-s-promotion\"\r\n            @click=\"handlePushNow(scope.row)\"\r\n          >立即推送</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <!-- 分页 -->\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.page_num\"\r\n      :limit.sync=\"queryParams.page_size\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 分析日志对话框 -->\r\n    <el-dialog title=\"分析日志\" :visible.sync=\"logDialogVisible\" width=\"1000px\" append-to-body>\r\n      <el-table\r\n        v-loading=\"logLoading\"\r\n        :data=\"logList\"\r\n        height=\"400\"\r\n        :default-sort=\"{prop: 'createTime', order: 'descending'}\"\r\n      >\r\n        <el-table-column label=\"时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"级别\" align=\"center\" prop=\"logLevel\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag\r\n              :type=\"getLogLevelTagType(scope.row.logLevel)\"\r\n              size=\"mini\"\r\n            >\r\n              {{ scope.row.logLevel.toUpperCase() }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"步骤\" align=\"center\" prop=\"stepName\" width=\"120\" />\r\n        <el-table-column label=\"进度\" align=\"center\" prop=\"progressPercentage\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ scope.row.progressPercentage }}%</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"日志内容\" align=\"center\" prop=\"logMessage\" :show-overflow-tooltip=\"true\" />\r\n      </el-table>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"logDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 查看报告对话框 -->\r\n    <el-dialog title=\"分析报告\" :visible.sync=\"reportDialogVisible\" width=\"1200px\" append-to-body>\r\n      <div v-loading=\"reportLoading\" class=\"report-content\">\r\n        <div v-if=\"reportData\">\r\n          <el-descriptions title=\"基本信息\" :column=\"2\" border>\r\n            <el-descriptions-item label=\"需求名称\">{{ reportData.requirementName }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"关键词\">{{ reportData.entityKeyword }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"具体需求\" :span=\"2\">{{ reportData.specificRequirement }}</el-descriptions-item>\r\n          </el-descriptions>\r\n\r\n          <el-divider content-position=\"left\">分析结果</el-divider>\r\n          <div class=\"analysis-results\">\r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"8\">\r\n                <el-card shadow=\"never\">\r\n                  <div slot=\"header\">\r\n                    <span>文章总数</span>\r\n                  </div>\r\n                  <div class=\"statistic-value\">{{ reportData.totalArticles || 0 }}</div>\r\n                </el-card>\r\n              </el-col>\r\n              <el-col :span=\"8\">\r\n                <el-card shadow=\"never\">\r\n                  <div slot=\"header\">\r\n                    <span>正面情感</span>\r\n                  </div>\r\n                  <div class=\"statistic-value positive\">{{ reportData.sentiment?.positive || 0 }}</div>\r\n                </el-card>\r\n              </el-col>\r\n              <el-col :span=\"8\">\r\n                <el-card shadow=\"never\">\r\n                  <div slot=\"header\">\r\n                    <span>负面情感</span>\r\n                  </div>\r\n                  <div class=\"statistic-value negative\">{{ reportData.sentiment?.negative || 0 }}</div>\r\n                </el-card>\r\n              </el-col>\r\n            </el-row>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"handleExportReport\">导出报告</el-button>\r\n        <el-button @click=\"reportDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 立即推送对话框 -->\r\n    <el-dialog title=\"立即推送\" :visible.sync=\"pushDialogVisible\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"pushForm\" :model=\"pushForm\" :rules=\"pushRules\" label-width=\"100px\">\r\n        <el-form-item label=\"推送地址\" prop=\"push_url\">\r\n          <el-input v-model=\"pushForm.push_url\" placeholder=\"请输入推送地址\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"推送内容\" prop=\"push_content\">\r\n          <el-input\r\n            v-model=\"pushForm.push_content\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n            placeholder=\"请输入推送内容\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"pushDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" :loading=\"pushLoading\" @click=\"submitPush\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 定时推送对话框 -->\r\n    <el-dialog title=\"定时推送\" :visible.sync=\"schedulePushDialogVisible\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"schedulePushForm\" :model=\"schedulePushForm\" :rules=\"schedulePushRules\" label-width=\"100px\">\r\n        <el-form-item label=\"推送地址\" prop=\"push_url\">\r\n          <el-input v-model=\"schedulePushForm.push_url\" placeholder=\"请输入推送地址\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"推送时间\" prop=\"push_time\">\r\n          <el-date-picker\r\n            v-model=\"schedulePushForm.push_time\"\r\n            type=\"datetime\"\r\n            placeholder=\"选择推送时间\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            style=\"width: 100%\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"推送频率\" prop=\"frequency\">\r\n          <el-select v-model=\"schedulePushForm.frequency\" placeholder=\"请选择推送频率\">\r\n            <el-option label=\"仅一次\" value=\"once\" />\r\n            <el-option label=\"每日\" value=\"daily\" />\r\n            <el-option label=\"每周\" value=\"weekly\" />\r\n            <el-option label=\"每月\" value=\"monthly\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"推送内容\" prop=\"push_content\">\r\n          <el-input\r\n            v-model=\"schedulePushForm.push_content\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n            placeholder=\"请输入推送内容\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"schedulePushDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" :loading=\"schedulePushLoading\" @click=\"submitSchedulePush\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 定时任务抽屉 -->\r\n    <el-drawer\r\n      title=\"定时任务\"\r\n      :visible.sync=\"timedTaskDialogVisible\"\r\n      direction=\"rtl\"\r\n      size=\"600px\"\r\n      :before-close=\"closeTimedTaskDialog\"\r\n      custom-class=\"timed-task-drawer\"\r\n    >\r\n      <!-- 抽屉头部右侧按钮 -->\r\n      <div slot=\"title\" class=\"drawer-header\">\r\n        <span class=\"drawer-title\">定时任务</span>\r\n        <el-button\r\n          type=\"primary\"\r\n          size=\"mini\"\r\n          icon=\"el-icon-plus\"\r\n          class=\"add-task-btn\"\r\n          @click=\"handleAddTimedTask\"\r\n        >\r\n          定时任务\r\n        </el-button>\r\n      </div>\r\n\r\n      <!-- 抽屉内容 -->\r\n      <div class=\"drawer-content\">\r\n        <!-- 空状态 -->\r\n        <div v-if=\"timedTaskList.length === 0\" class=\"empty-state\">\r\n          <div class=\"empty-content\">\r\n            <!-- 空状态图标 -->\r\n            <div class=\"empty-icon\">\r\n              <svg width=\"120\" height=\"120\" viewBox=\"0 0 120 120\" fill=\"none\">\r\n                <!-- 文件夹图标 -->\r\n                <path d=\"M20 30h25l5-10h50v70H20V30z\" fill=\"#f0f0f0\" stroke=\"#d0d0d0\" stroke-width=\"2\" />\r\n                <path d=\"M25 35h70v50H25V35z\" fill=\"#fafafa\" stroke=\"#e0e0e0\" stroke-width=\"1\" />\r\n                <!-- 文档图标 -->\r\n                <rect x=\"35\" y=\"45\" width=\"30\" height=\"25\" fill=\"#ffffff\" stroke=\"#d0d0d0\" stroke-width=\"1\" rx=\"2\" />\r\n                <rect x=\"70\" y=\"50\" width=\"20\" height=\"15\" fill=\"#ffffff\" stroke=\"#d0d0d0\" stroke-width=\"1\" rx=\"2\" />\r\n                <!-- 装饰线条 -->\r\n                <line x1=\"40\" y1=\"52\" x2=\"60\" y2=\"52\" stroke=\"#e0e0e0\" stroke-width=\"1\" />\r\n                <line x1=\"40\" y1=\"57\" x2=\"55\" y2=\"57\" stroke=\"#e0e0e0\" stroke-width=\"1\" />\r\n                <line x1=\"40\" y1=\"62\" x2=\"58\" y2=\"62\" stroke=\"#e0e0e0\" stroke-width=\"1\" />\r\n                <line x1=\"75\" y1=\"55\" x2=\"85\" y2=\"55\" stroke=\"#e0e0e0\" stroke-width=\"1\" />\r\n                <line x1=\"75\" y1=\"60\" x2=\"82\" y2=\"60\" stroke=\"#e0e0e0\" stroke-width=\"1\" />\r\n              </svg>\r\n            </div>\r\n            <p class=\"empty-text\">暂无定时任务</p>\r\n            <el-button type=\"primary\" class=\"create-btn\" @click=\"handleCreateTimedTask\">\r\n              去创建\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 任务列表 -->\r\n        <div v-else class=\"task-list\">\r\n          <div v-if=\"timedTaskList.length === 0\" class=\"empty-task-list\">\r\n            <div class=\"empty-icon\">📅</div>\r\n            <div class=\"empty-text\">暂无定时任务</div>\r\n            <el-button type=\"primary\" size=\"small\" class=\"add-task-btn\" @click=\"handleAddTimedTask\">\r\n              添加任务\r\n            </el-button>\r\n          </div>\r\n          <div v-else class=\"task-items\">\r\n            <div v-for=\"(task, index) in timedTaskList\" :key=\"index\" class=\"task-item\">\r\n              <div class=\"task-info\">\r\n                <div class=\"task-header\">\r\n                  <div class=\"task-name\">{{ task.name }}</div>\r\n                  <div class=\"task-status\" :class=\"{ 'status-running': task.status === 'running', 'status-pending': task.status === 'pending' }\">\r\n                    {{ task.status === 'running' ? '运行中' : '待运行' }}\r\n                  </div>\r\n                </div>\r\n                <!-- 任务描述已隐藏 -->\r\n                <div class=\"task-schedule\">\r\n                  <i class=\"el-icon-time\" />\r\n                  <span>{{ getTaskScheduleText(task) }}</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"task-actions\">\r\n                <el-button type=\"text\" size=\"mini\" title=\"预览任务详情\" @click.stop=\"previewTask(index)\">\r\n                  <i class=\"el-icon-view\" />\r\n                </el-button>\r\n                <el-button type=\"text\" size=\"mini\" :title=\"task.status === 'running' ? '暂停任务' : '启动任务'\" @click.stop=\"toggleTaskStatus(index)\">\r\n                  <i :class=\"task.status === 'running' ? 'el-icon-video-pause' : 'el-icon-video-play'\" />\r\n                </el-button>\r\n                <el-button type=\"text\" size=\"mini\" title=\"编辑任务\" style=\"margin-right: 5px;\" @click.stop=\"editTask(index)\">\r\n                  <i class=\"el-icon-edit\" />\r\n                </el-button>\r\n                <el-button type=\"text\" size=\"mini\" title=\"删除任务\" style=\"color: #f56c6c;\" @click.stop=\"deleteTask(index)\">\r\n                  <i class=\"el-icon-delete\" />\r\n                </el-button>\r\n                <!-- 测试按钮 -->\r\n                <el-button type=\"text\" size=\"mini\" title=\"测试点击\" style=\"color: #409eff;\" @click.stop=\"testClick(index)\">\r\n                  <i class=\"el-icon-info\" />\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 任务预览对话框 -->\r\n      <el-dialog\r\n        title=\"任务详情\"\r\n        :visible.sync=\"taskPreviewDialog.visible\"\r\n        width=\"600px\"\r\n        append-to-body\r\n      >\r\n        <div v-if=\"taskPreviewDialog.taskData\" class=\"task-preview-content\">\r\n          <el-descriptions title=\"基本信息\" :column=\"2\" border>\r\n            <el-descriptions-item label=\"任务名称\">{{ taskPreviewDialog.taskData.name }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"任务状态\">\r\n              <el-tag :type=\"taskPreviewDialog.taskData.status === 'running' ? 'success' : 'info'\">\r\n                {{ taskPreviewDialog.taskData.status === 'running' ? '运行中' : '待运行' }}\r\n              </el-tag>\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"执行频率\">{{ getFrequencyText(taskPreviewDialog.taskData.frequency) }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"执行时间\">{{ taskPreviewDialog.taskData.executeTime }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"推送地址\" :span=\"2\">{{ taskPreviewDialog.taskData.pushUrl || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"任务描述\" :span=\"2\">{{ taskPreviewDialog.taskData.description || '无描述' }}</el-descriptions-item>\r\n          </el-descriptions>\r\n        </div>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button @click=\"taskPreviewDialog.visible = false\">关 闭</el-button>\r\n        </div>\r\n      </el-dialog>\r\n\r\n      <!-- 创建/编辑任务弹窗 -->\r\n      <el-dialog\r\n        :title=\"editingTaskIndex === -1 ? '创建定时任务' : '编辑定时任务'\"\r\n        :visible.sync=\"createTaskDialogVisible\"\r\n        width=\"500px\"\r\n        :before-close=\"closeCreateTaskDialog\"\r\n        :append-to-body=\"true\"\r\n        class=\"create-task-dialog\"\r\n      >\r\n        <div class=\"task-form\">\r\n          <!-- 任务需求 -->\r\n          <div class=\"task-requirement-section\">\r\n            <div class=\"section-label\">\r\n              任务需求\r\n              <span class=\"required\">*</span>\r\n            </div>\r\n            <div class=\"form-group\">\r\n              <div class=\"input-label\">关联任务</div>\r\n              <el-select\r\n                v-model=\"taskForm.requirement_id\"\r\n                placeholder=\"请选择要关联的舆情分析任务\"\r\n                class=\"task-name-input\"\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"task in requirementList\"\r\n                  :key=\"task.id\"\r\n                  :label=\"task.requirementName\"\r\n                  :value=\"task.id\"\r\n                >\r\n                  <span style=\"float: left\">{{ task.requirementName }}</span>\r\n                  <span style=\"float: right; color: #8492a6; font-size: 13px\">ID: {{ task.id }}</span>\r\n                </el-option>\r\n              </el-select>\r\n            </div>\r\n            <div class=\"form-group\">\r\n              <div class=\"input-label\">任务名称</div>\r\n              <el-input\r\n                v-model=\"taskForm.name\"\r\n                placeholder=\"请输入任务名称\"\r\n                class=\"task-name-input\"\r\n              />\r\n            </div>\r\n            <div class=\"form-group\">\r\n              <div class=\"input-label\">任务描述</div>\r\n              <el-input\r\n                v-model=\"taskForm.description\"\r\n                type=\"textarea\"\r\n                :rows=\"3\"\r\n                placeholder=\"请描述具体的任务需求，例如：基于当前分析需求自动生成AI新闻总结\"\r\n                class=\"task-description-input\"\r\n              />\r\n              <div class=\"form-group\">\r\n                <div class=\"input-label\">推送地址</div>\r\n                <el-input\r\n                  v-model=\"taskForm.push_url\"\r\n                  placeholder=\"例如：https://www.baidu.com\"\r\n                  class=\"task-name-input\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 执行时间 -->\r\n          <div class=\"execute-time-section\">\r\n            <div class=\"section-label\">执行时间</div>\r\n            <div class=\"time-selector\">\r\n              <el-select v-model=\"taskForm.frequency\" placeholder=\"选择频率\" class=\"frequency-select\">\r\n                <el-option label=\"仅一次\" value=\"once\" />\r\n                <el-option label=\"每天\" value=\"daily\" />\r\n                <el-option label=\"每周\" value=\"weekly\" />\r\n                <el-option label=\"每月\" value=\"monthly\" />\r\n              </el-select>\r\n              <!-- 一次性任务：选择具体日期时间 -->\r\n              <el-date-picker\r\n                v-if=\"taskForm.frequency === 'once'\"\r\n                v-model=\"taskForm.execute_date_time\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择执行日期和时间\"\r\n                format=\"yyyy-MM-dd HH:mm\"\r\n                value-format=\"yyyy-MM-dd HH:mm\"\r\n                class=\"datetime-picker\"\r\n                :picker-options=\"{\r\n                  disabledDate(time) {\r\n                    return time.getTime() < Date.now() - 8.64e7\r\n                  }\r\n                }\"\r\n              />\r\n              <!-- 周期性任务：选择时间 -->\r\n              <el-time-picker\r\n                v-else\r\n                v-model=\"taskForm.execute_time\"\r\n                format=\"HH:mm\"\r\n                value-format=\"HH:mm\"\r\n                placeholder=\"选择时间\"\r\n                class=\"time-picker\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 底部按钮 -->\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button class=\"modify-btn\" @click=\"modifyPlan\">修改计划</el-button>\r\n          <el-button type=\"primary\" class=\"run-btn\" @click=\"saveAndRunTask\">保存并运行</el-button>\r\n          <el-button type=\"success\" class=\"save-btn\" @click=\"saveTaskPlan\">保存计划</el-button>\r\n        </div>\r\n      </el-dialog>\r\n    </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  getAnalysisTaskList,\r\n  deleteAnalysisTask,\r\n  batchDeleteAnalysisTasks,\r\n  exportRequirements,\r\n  getAnalysisProgress,\r\n  pushReport,\r\n  createTimedTask,\r\n  getTimedTaskList,\r\n  getTimedTaskDetail,\r\n  updateTimedTask,\r\n  updateTaskStatus,\r\n  deleteTimedTask\r\n} from '@/api/opinion-analysis'\r\nimport { parseTime } from '@/utils/ruoyi'\r\n\r\nexport default {\r\n  name: 'AnalyzeRecord',\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 分析记录表格数据\r\n      recordList: [],\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        page_num: 1,\r\n        page_size: 10,\r\n        requirementName: null,\r\n        entityKeyword: null,\r\n        analysisStatus: null\r\n      },\r\n      // 分析日志对话框\r\n      logDialogVisible: false,\r\n      logLoading: false,\r\n      logList: [],\r\n      // 查看报告对话框\r\n      reportDialogVisible: false,\r\n      reportLoading: false,\r\n      reportData: null,\r\n      // 立即推送对话框\r\n      pushDialogVisible: false,\r\n      pushLoading: false,\r\n      pushForm: {\r\n        push_url: '',\r\n        push_content: ''\r\n      },\r\n      pushRules: {\r\n        push_url: [\r\n          { required: true, message: '推送地址不能为空', trigger: 'blur' }\r\n        ],\r\n        push_content: [\r\n          { required: true, message: '推送内容不能为空', trigger: 'blur' }\r\n        ]\r\n      },\r\n      // 定时推送对话框\r\n      schedulePushDialogVisible: false,\r\n      schedulePushLoading: false,\r\n      schedulePushForm: {\r\n        push_url: '',\r\n        push_time: '',\r\n        frequency: 'once',\r\n        push_content: ''\r\n      },\r\n      schedulePushRules: {\r\n        push_url: [\r\n          { required: true, message: '推送地址不能为空', trigger: 'blur' }\r\n        ],\r\n        push_time: [\r\n          { required: true, message: '推送时间不能为空', trigger: 'change' }\r\n        ],\r\n        push_content: [\r\n          { required: true, message: '推送内容不能为空', trigger: 'blur' }\r\n        ]\r\n      },\r\n      // 当前操作的记录\r\n      currentRecord: null,\r\n      // 定时任务相关数据\r\n      timedTaskDialogVisible: false, // 定时任务抽屉显示状态\r\n      timedTaskList: [], // 定时任务列表\r\n      createTaskDialogVisible: false, // 创建任务弹窗显示状态\r\n      editingTaskIndex: -1, // 当前编辑的任务索引，-1表示新建任务\r\n      taskForm: {\r\n        requirement_id: '', // 需求ID\r\n        name: '', // 任务名称\r\n        description: '',\r\n        execute_time: '16:00',\r\n        execute_date_time: '', // 一次性任务的执行日期时间\r\n        frequency: 'daily',\r\n        push_url: '' // 推送地址\r\n      },\r\n      requirementList: [], // 需求列表\r\n      // 任务预览弹窗状态\r\n      taskPreviewDialog: {\r\n        visible: false,\r\n        taskData: null,\r\n        loading: false\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    // 只重置日期范围，保留其他查询参数\r\n    this.dateRange = []\r\n    this.getList()\r\n    // 注释掉页面加载时的定时任务列表加载，改为点击定时推送按钮时才加载\r\n    // this.loadTimedTaskList()\r\n  },\r\n  methods: {\r\n    /** 查询分析记录列表 */\r\n    getList(paginationParams) {\r\n      this.loading = true\r\n\r\n      // 处理分页参数\r\n      if (paginationParams) {\r\n        this.queryParams.page_num = paginationParams.page\r\n        this.queryParams.page_size = paginationParams.limit\r\n      }\r\n\r\n      const params = {\r\n        page_num: this.queryParams.page_num,\r\n        page_size: this.queryParams.page_size\r\n      }\r\n\r\n      // 添加搜索条件（正确映射到任务表字段）\r\n      if (this.queryParams.requirementName && this.queryParams.requirementName.trim() !== '') {\r\n        params.task_name = this.queryParams.requirementName.trim() // 映射到任务名称\r\n      }\r\n      if (this.queryParams.entityKeyword && this.queryParams.entityKeyword.trim() !== '') {\r\n        params.task_description = this.queryParams.entityKeyword.trim() // 映射到任务描述（包含关键词）\r\n      }\r\n      if (this.queryParams.analysisStatus !== null && this.queryParams.analysisStatus !== undefined && this.queryParams.analysisStatus !== '') {\r\n        // 将前端状态映射回任务状态\r\n        const statusMap = { 0: 'pending', 1: 'running', 2: 'completed', 3: 'failed' }\r\n        params.status = statusMap[this.queryParams.analysisStatus] || 'pending'\r\n      }\r\n\r\n      // 添加时间范围\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.create_time_start = this.dateRange[0]\r\n        params.create_time_end = this.dateRange[1]\r\n        console.log('🔍 添加时间范围筛选:', this.dateRange)\r\n      }\r\n\r\n      // 调用真实API获取任务列表数据\r\n      console.log('🔍 发送API请求，参数:', params)\r\n      getAnalysisTaskList(params).then(response => {\r\n        console.log('🔍 API响应:', response)\r\n        // 处理响应数据，将数据库字段映射到前端显示字段\r\n\r\n        // 适配新的响应格式：使用 model_content 后，PageResponseModel 会直接合并到响应根级别\r\n        let requirements = []\r\n        if (response.records) {\r\n          // 新格式：model_content 使 PageResponseModel 直接合并到响应根级别\r\n          requirements = response.records\r\n        } else if (response.data && response.data.records) {\r\n          // 备用格式：PageResponseModel 在 data 字段中\r\n          requirements = response.data.records\r\n        } else if (response.data && response.data.rows) {\r\n          // 旧的分页数据格式\r\n          requirements = response.data.rows\r\n        } else if (response.data && Array.isArray(response.data)) {\r\n          // 直接数组格式\r\n          requirements = response.data\r\n        } else if (response.rows) {\r\n          // 兼容旧格式\r\n          requirements = response.rows\r\n        } else {\r\n          requirements = []\r\n        }\r\n        this.recordList = requirements\r\n          .filter(item => item.id != null && item.id !== undefined && item.id !== '') // 过滤掉无效id的数据\r\n          .map(item => {\r\n            // 确保ID是有效的整数\r\n            const id = parseInt(item.id)\r\n            if (isNaN(id)) {\r\n              return null\r\n            }\r\n\r\n            const mappedItem = {\r\n              id: id, // 确保id是整数\r\n              requirementName: item.task_name || item.taskName || '', // 任务名称\r\n              entityKeyword: item.task_description || item.taskDescription || '', // 任务描述作为关键词显示\r\n              specificRequirement: item.task_description || item.taskDescription || '', // 任务描述\r\n              createTime: item.create_time || item.createTime || '',\r\n              // 将任务状态映射到前端的status字段\r\n              // pending-待执行->0待生成, running-执行中->1生成中, completed-已完成->2已生成, failed-失败->-1生成失败\r\n              status: this.mapTaskStatus(item.status || 'pending'),\r\n              // 添加报告OSS URL字段\r\n              reportOssUrl: item.report_oss_url || item.reportOssUrl || null\r\n            }\r\n            return mappedItem\r\n          })\r\n          .filter(item => item !== null) // 过滤掉映射失败的项\r\n\r\n        // 适配新的总记录数格式：使用 model_content 后，total 会直接在响应根级别\r\n        if (response.total !== undefined) {\r\n          this.total = response.total\r\n        } else if (response.data && response.data.total !== undefined) {\r\n          this.total = response.data.total\r\n        } else {\r\n          this.total = 0\r\n        }\r\n\r\n        this.loading = false\r\n      }).catch(error => {\r\n        this.$modal.msgError('获取分析记录列表失败: ' + (error.message || '未知错误'))\r\n        this.loading = false\r\n      })\r\n    },\r\n\r\n    /** 映射任务状态 */\r\n    mapTaskStatus(taskStatus) {\r\n      // 数据库task status: pending-待执行，running-执行中，completed-已完成，failed-失败，paused-暂停\r\n      // 前端status: 0-待生成，1-生成中，2-已生成，-1-生成失败\r\n      const statusMap = {\r\n        'pending': 0, // 待执行 -> 待生成\r\n        'running': 1, // 执行中 -> 生成中\r\n        'completed': 2, // 已完成 -> 已生成\r\n        'failed': -1, // 失败 -> 生成失败\r\n        'paused': 0 // 暂停 -> 待生成\r\n      }\r\n      return statusMap[taskStatus] !== undefined ? statusMap[taskStatus] : 0\r\n    },\r\n\r\n    /** 映射分析状态（保留兼容性） */\r\n    mapAnalysisStatus(analysisStatus) {\r\n      // 数据库analysis_status: 0-未开始，1-分析中，2-已完成，3-失败\r\n      // 前端status: 0-待生成，1-生成中，2-已生成，-1-生成失败\r\n      const statusMap = {\r\n        0: 0, // 未开始 -> 待生成\r\n        1: 1, // 分析中 -> 生成中\r\n        2: 2, // 已完成 -> 已生成\r\n        3: -1 // 失败 -> 生成失败\r\n      }\r\n      return statusMap[analysisStatus] !== undefined ? statusMap[analysisStatus] : 0\r\n    },\r\n\r\n    /** 需求名称输入监听 */\r\n    onRequirementNameInput(value) {\r\n      console.log('🔍 需求名称输入变化:', value)\r\n      // 可以添加防抖逻辑，避免频繁查询\r\n      // this.debounceSearch()\r\n    },\r\n\r\n    /** 关键词输入监听 */\r\n    onEntityKeywordInput(value) {\r\n      console.log('🔍 关键词输入变化:', value)\r\n      // 可以添加防抖逻辑，避免频繁查询\r\n      // this.debounceSearch()\r\n    },\r\n\r\n    /** 分析状态变化监听 */\r\n    onAnalysisStatusChange(value) {\r\n      console.log('🔍 分析状态变化:', value)\r\n      // 状态变化时立即搜索\r\n      this.handleQuery()\r\n    },\r\n\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      // 重置到第一页\r\n      this.queryParams.page_num = 1\r\n      // 添加搜索日志\r\n      console.log('🔍 执行搜索，查询参数:', this.queryParams)\r\n      this.getList()\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = []\r\n      // 手动重置查询参数\r\n      this.queryParams = {\r\n        page_num: 1,\r\n        page_size: 10,\r\n        requirementName: null,\r\n        entityKeyword: null,\r\n        analysisStatus: null\r\n      }\r\n      // 重置表单\r\n      this.resetForm('queryForm')\r\n      // 添加重置日志\r\n      console.log('🔄 重置搜索条件，查询参数:', this.queryParams)\r\n      this.handleQuery()\r\n    },\r\n\r\n    /** 多选框选中数据 */\r\n    handleSelectionChange(selection) {\r\n      // 过滤掉id为null或undefined的项，并确保id是整数\r\n      this.ids = selection\r\n        .filter(item => item.id != null && item.id !== undefined)\r\n        .map(item => parseInt(item.id))\r\n\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n\r\n    /** 查看分析日志 */\r\n    handleViewLogs(row) {\r\n      this.currentRecord = row\r\n      this.logDialogVisible = true\r\n      this.logLoading = true\r\n\r\n      // 获取分析进度日志\r\n      getAnalysisProgress(row.taskId || row.id).then(response => {\r\n        this.logList = response.data || []\r\n        this.logLoading = false\r\n      }).catch(() => {\r\n        this.logLoading = false\r\n      })\r\n    },\r\n\r\n    /** 查看报告 */\r\n    handleViewReport(row) {\r\n      this.currentRecord = row\r\n      this.reportDialogVisible = true\r\n      this.reportLoading = true\r\n\r\n      // 模拟获取报告数据\r\n      setTimeout(() => {\r\n        this.reportData = {\r\n          requirementName: row.requirementName,\r\n          entityKeyword: row.entityKeyword,\r\n          specificRequirement: row.specificRequirement,\r\n          totalArticles: Math.floor(Math.random() * 1000) + 100,\r\n          sentiment: {\r\n            positive: Math.floor(Math.random() * 50) + 20,\r\n            neutral: Math.floor(Math.random() * 30) + 10,\r\n            negative: Math.floor(Math.random() * 20) + 5\r\n          }\r\n        }\r\n        this.reportLoading = false\r\n      }, 1000)\r\n    },\r\n\r\n    /** 报告 */\r\n    handleReport(row) {\r\n      // 检查是否有报告OSS URL\r\n      if (row.reportOssUrl) {\r\n        // 如果有OSS URL，直接在新窗口打开\r\n        window.open(row.reportOssUrl, '_blank')\r\n      } else {\r\n        // 如果没有OSS URL，显示暂无报告提示\r\n        this.$modal.msg('暂无报告')\r\n      }\r\n    },\r\n\r\n    /** 立即推送 */\r\n    handlePushNow(row) {\r\n      // 移除OSS URL检查，让后端处理报告生成和上传\r\n      // 如果没有reportOssUrl，后端会自动生成HTML报告并上传到OSS\r\n\r\n      this.currentRecord = row\r\n      this.pushForm = {\r\n        push_url: '',\r\n        push_content: `分析报告：${row.requirementName} - 立即推送`\r\n      }\r\n      this.pushDialogVisible = true\r\n    },\r\n\r\n    /** 定时推送 */\r\n    handleSchedulePush(row) {\r\n      this.currentRecord = row\r\n      this.schedulePushForm = {\r\n        push_url: '',\r\n        push_time: '',\r\n        frequency: 'once',\r\n        push_content: `分析报告：${row.requirementName}`\r\n      }\r\n      this.schedulePushDialogVisible = true\r\n    },\r\n\r\n    /** 提交立即推送 */\r\n    submitPush() {\r\n      this.$refs['pushForm'].validate(valid => {\r\n        if (valid) {\r\n          this.pushLoading = true\r\n\r\n          // 构建推送数据\r\n          // 如果没有reportOssUrl，后端会自动生成HTML报告并上传到OSS\r\n          const pushData = {\r\n            target_url: this.pushForm.push_url,\r\n            report_data: {\r\n              requirementName: this.currentRecord.requirementName,\r\n              entityKeyword: this.currentRecord.entityKeyword,\r\n              specificRequirement: this.currentRecord.specificRequirement,\r\n              reportOssUrl: this.currentRecord.reportOssUrl || null, // 可能为空，后端会处理\r\n              totalArticles: 0, // 后端会从实际数据中获取\r\n              sentiment: {}, // 后端会从实际数据中获取\r\n              dataSources: 0, // 后端会从实际数据中获取\r\n              // 添加更多信息以便后端生成完整报告\r\n              taskId: this.currentRecord.id,\r\n              createTime: this.currentRecord.createTime,\r\n              status: this.currentRecord.status\r\n            },\r\n            analysis_results: {\r\n              summary: this.pushForm.push_content,\r\n              reportUrl: this.currentRecord.reportOssUrl || null // 可能为空，后端会生成\r\n            },\r\n            requirement_id: this.currentRecord.id,\r\n            push_type: 'immediate'\r\n          }\r\n\r\n          pushReport(pushData).then(response => {\r\n            // 检查响应并显示详细信息\r\n            if (response && response.success) {\r\n              this.$modal.msgSuccess('推送成功')\r\n\r\n              // 如果后端生成了新的报告URL，可以更新本地记录\r\n              if (response.data && response.data.report_url && !this.currentRecord.reportOssUrl) {\r\n                this.currentRecord.reportOssUrl = response.data.report_url\r\n                console.log('更新本地报告URL:', response.data.report_url)\r\n              }\r\n            } else {\r\n              this.$modal.msgSuccess('推送完成')\r\n            }\r\n\r\n            this.pushDialogVisible = false\r\n            this.pushLoading = false\r\n          }).catch(error => {\r\n            console.error('推送失败:', error)\r\n            if (error.response && error.response.data && error.response.data.msg) {\r\n              this.$modal.msgError('推送失败: ' + error.response.data.msg)\r\n            } else {\r\n              this.$modal.msgError('推送失败，请重试')\r\n            }\r\n            this.pushLoading = false\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    /** 提交定时推送 */\r\n    submitSchedulePush() {\r\n      this.$refs['schedulePushForm'].validate(valid => {\r\n        if (valid) {\r\n          this.schedulePushLoading = true\r\n          const taskData = {\r\n            requirement_id: this.currentRecord.id,\r\n            push_url: this.schedulePushForm.push_url,\r\n            push_time: this.schedulePushForm.push_time,\r\n            frequency: this.schedulePushForm.frequency,\r\n            push_content: this.schedulePushForm.push_content\r\n          }\r\n\r\n          createTimedTask(taskData).then(response => {\r\n            this.$modal.msgSuccess('定时推送任务创建成功')\r\n            this.schedulePushDialogVisible = false\r\n            this.schedulePushLoading = false\r\n          }).catch(() => {\r\n            this.schedulePushLoading = false\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      let ids, names\r\n\r\n      // 判断是否为批量删除（无参数或参数为事件对象）\r\n      if (!row || row instanceof Event) {\r\n        // 批量删除\r\n        if (!this.ids || this.ids.length === 0) {\r\n          this.$modal.msgError('请先选择要删除的记录')\r\n          return\r\n        }\r\n        // 过滤并验证IDs\r\n        ids = this.ids.filter(id => id != null && !isNaN(id)).map(id => parseInt(id))\r\n        if (ids.length === 0) {\r\n          this.$modal.msgError('选中的记录ID无效，无法删除')\r\n          return\r\n        }\r\n        names = this.recordList.filter(item => ids.includes(item.id)).map(item => item.requirementName).join('、')\r\n      } else {\r\n        // 单个删除\r\n        if (!row.id || row.id == null) {\r\n          this.$modal.msgError('记录ID无效，无法删除')\r\n          return\r\n        }\r\n        ids = [parseInt(row.id)]\r\n        names = row.requirementName || '未知记录'\r\n      }\r\n      this.$modal.confirm(`是否确认删除分析记录\"${names}\"？`).then(() => {\r\n        // 根据IDs数量判断是单个删除还是批量删除\r\n        if (ids.length === 1) {\r\n          // 单个删除\r\n          return deleteAnalysisTask(ids[0])\r\n        } else {\r\n          // 批量删除\r\n          return batchDeleteAnalysisTasks(ids)\r\n        }\r\n      }).then(response => {\r\n        console.log('🗑️ 删除操作成功，响应:', response)\r\n\r\n        // 检查是否需要重置分页（物理删除后需要重新计算分页）\r\n        const currentPageRecords = this.recordList.length\r\n        const deletedCount = ids.length\r\n\r\n        // 如果删除后当前页可能没有记录了，且不是第一页，则回到上一页\r\n        if (currentPageRecords <= deletedCount && this.queryParams.page_num > 1) {\r\n          this.queryParams.page_num = this.queryParams.page_num - 1\r\n          console.log('🔄 删除后回到上一页:', this.queryParams.page_num)\r\n        }\r\n\r\n        // 重置选中状态\r\n        this.ids = []\r\n        this.single = true\r\n        this.multiple = true\r\n\r\n        // 物理删除成功后，直接重新获取数据以确保数据一致性\r\n        this.getList()\r\n\r\n        this.$modal.msgSuccess('删除成功')\r\n      }).catch(error => {\r\n        if (error.response && error.response.data) {\r\n          if (error.response.data.msg) {\r\n            this.$modal.msgError(error.response.data.msg)\r\n          } else if (error.response.data.message) {\r\n            this.$modal.msgError(error.response.data.message)\r\n          } else if (error.response.data.detail) {\r\n            // 处理FastAPI的验证错误\r\n            if (Array.isArray(error.response.data.detail)) {\r\n              const details = error.response.data.detail.map(d => d.msg || d.message || JSON.stringify(d)).join(', ')\r\n              this.$modal.msgError(`删除失败: ${details}`)\r\n            } else {\r\n              this.$modal.msgError(`删除失败: ${error.response.data.detail}`)\r\n            }\r\n          } else {\r\n            this.$modal.msgError(`删除失败: ${JSON.stringify(error.response.data)}`)\r\n          }\r\n        } else if (error.message) {\r\n          this.$modal.msgError(`删除失败: ${error.message}`)\r\n        } else {\r\n          this.$modal.msgError('删除失败，请重试')\r\n        }\r\n      })\r\n    },\r\n\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.$modal.confirm('是否确认导出所有分析记录数据项？').then(() => {\r\n        const params = {}\r\n\r\n        // 添加搜索条件（使用后端期望的下划线命名）\r\n        if (this.queryParams.requirementName) {\r\n          params.requirement_name = this.queryParams.requirementName\r\n        }\r\n        if (this.queryParams.entityKeyword) {\r\n          params.entity_keyword = this.queryParams.entityKeyword\r\n        }\r\n        if (this.queryParams.analysisStatus !== null && this.queryParams.analysisStatus !== undefined && this.queryParams.analysisStatus !== '') {\r\n          params.analysis_status = this.queryParams.analysisStatus\r\n        }\r\n\r\n        // 添加时间范围\r\n        if (this.dateRange && this.dateRange.length === 2) {\r\n          params.create_time_start = this.dateRange[0]\r\n          params.create_time_end = this.dateRange[1]\r\n        }\r\n\r\n        return exportRequirements(params)\r\n      }).then(response => {\r\n        // 处理文件下载\r\n        const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })\r\n        const url = window.URL.createObjectURL(blob)\r\n        const link = document.createElement('a')\r\n        link.href = url\r\n        link.download = `分析记录_${new Date().toISOString().slice(0, 10)}.xlsx`\r\n        document.body.appendChild(link)\r\n        link.click()\r\n        document.body.removeChild(link)\r\n        window.URL.revokeObjectURL(url)\r\n\r\n        this.$modal.msgSuccess('导出成功')\r\n      }).catch(error => {\r\n        if (error.response && error.response.data && error.response.data.msg) {\r\n          this.$modal.msgError(error.response.data.msg)\r\n        } else {\r\n          this.$modal.msgError('导出失败，请重试')\r\n        }\r\n      })\r\n    },\r\n\r\n    /** 导出报告 */\r\n    handleExportReport() {\r\n      this.$modal.msgSuccess('报告导出成功')\r\n    },\r\n\r\n    /** 获取状态标签类型 */\r\n    getStatusTagType(status) {\r\n      const statusMap = {\r\n        0: 'info', // 待生成 - 灰色\r\n        1: 'warning', // 生成中 - 橙色\r\n        2: 'success', // 已生成 - 绿色\r\n        '-1': 'danger' // 生成失败 - 红色\r\n      }\r\n      return statusMap[status] || 'info'\r\n    },\r\n\r\n    /** 获取状态文本 */\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        0: '待生成',\r\n        1: '生成中',\r\n        2: '已生成',\r\n        '-1': '生成失败'\r\n      }\r\n      return statusMap[status] || '未知'\r\n    },\r\n\r\n    /** 获取日志级别标签类型 */\r\n    getLogLevelTagType(level) {\r\n      const levelMap = {\r\n        'info': 'info',\r\n        'success': 'success',\r\n        'warning': 'warning',\r\n        'error': 'danger'\r\n      }\r\n      return levelMap[level] || 'info'\r\n    },\r\n\r\n    // ==================== 定时任务相关方法 ====================\r\n\r\n    // 处理定时推送按钮点击\r\n    handleTimedPush() {\r\n      this.timedTaskDialogVisible = true\r\n      // 打开定时推送弹窗时加载定时任务列表\r\n      this.loadTimedTaskList()\r\n    },\r\n\r\n    // 关闭定时任务弹窗\r\n    closeTimedTaskDialog() {\r\n      this.timedTaskDialogVisible = false\r\n    },\r\n\r\n    // 处理创建定时任务\r\n    handleCreateTimedTask() {\r\n      this.resetTaskForm()\r\n      this.editingTaskIndex = -1 // 设置为新建模式\r\n      this.loadOpinionTaskList() // 加载舆情任务列表\r\n      this.createTaskDialogVisible = true\r\n    },\r\n\r\n    // 处理添加定时任务按钮\r\n    handleAddTimedTask() {\r\n      this.resetTaskForm()\r\n      this.editingTaskIndex = -1 // 设置为新建模式\r\n      this.loadOpinionTaskList() // 加载舆情任务列表\r\n      this.createTaskDialogVisible = true\r\n    },\r\n\r\n    // 加载舆情任务列表（用于定时推送绑定）\r\n    async loadOpinionTaskList() {\r\n      try {\r\n        const response = await getAnalysisTaskList({\r\n          page_num: 1,\r\n          page_size: 100\r\n        })\r\n\r\n        if (response.success) {\r\n          // 处理分页数据，从 opinion_task 表获取数据\r\n          let tasks = []\r\n\r\n          if (response.data && response.data.rows) {\r\n            tasks = response.data.rows\r\n          } else if (Array.isArray(response.data)) {\r\n            tasks = response.data\r\n          } else if (response.rows) {\r\n            tasks = response.rows\r\n          }\r\n\r\n          // 映射任务数据到需求列表格式\r\n          this.requirementList = tasks.map(task => ({\r\n            id: task.id,\r\n            requirementName: task.requirementName || task.requirement_name || `任务${task.id}`,\r\n            taskId: task.id,\r\n            keywords: task.keywords || task.entity_keyword,\r\n            specificRequirement: task.specificRequirement || task.specific_requirement,\r\n            createTime: task.createTime || task.create_time\r\n          }))\r\n        } else {\r\n          this.$message.error(response.msg || '获取舆情任务列表失败')\r\n          this.requirementList = []\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('加载舆情任务列表失败')\r\n        this.requirementList = []\r\n      }\r\n    },\r\n\r\n    // 加载定时任务列表\r\n    async loadTimedTaskList() {\r\n      try {\r\n        const response = await getTimedTaskList()\r\n\r\n        if (response.success) {\r\n          // 处理分页数据 - 后端返回的是PageResponseModel格式\r\n\r\n          if (response.data && response.data.rows) {\r\n            this.timedTaskList = response.data.rows.map(task => ({\r\n              id: task.id,\r\n              requirementId: task.requirementId,\r\n              name: task.taskName,\r\n              description: task.taskDescription,\r\n              executeTime: task.executeTime,\r\n              executeDateTime: task.frequency === 'once' && task.execute_date ? `${task.execute_date} ${task.executeTime}` : '',\r\n              frequency: task.frequency,\r\n              status: task.status === 'running' ? 'running' : 'pending',\r\n              pushUrl: task.pushUrl\r\n            }))\r\n          } else if (response.data && response.data.records && Array.isArray(response.data.records)) {\r\n            this.timedTaskList = response.data.records.map(task => ({\r\n              id: task.id,\r\n              requirementId: task.requirementId,\r\n              name: task.taskName,\r\n              description: task.taskDescription,\r\n              executeTime: task.executeTime,\r\n              executeDateTime: task.frequency === 'once' && task.execute_date ? `${task.execute_date} ${task.executeTime}` : '',\r\n              frequency: task.frequency,\r\n              status: task.status === 'running' ? 'running' : 'pending',\r\n              pushUrl: task.pushUrl\r\n            }))\r\n          } else if (Array.isArray(response.data)) {\r\n            this.timedTaskList = response.data.map(task => ({\r\n              id: task.id,\r\n              requirementId: task.requirementId,\r\n              name: task.taskName,\r\n              description: task.taskDescription,\r\n              executeTime: task.executeTime,\r\n              executeDateTime: task.frequency === 'once' && task.execute_date ? `${task.execute_date} ${task.executeTime}` : '',\r\n              frequency: task.frequency,\r\n              status: task.status === 'running' ? 'running' : 'pending',\r\n              pushUrl: task.pushUrl\r\n            }))\r\n          } else if (response.rows && Array.isArray(response.rows)) {\r\n            this.timedTaskList = response.rows.map(task => ({\r\n              id: task.id,\r\n              requirementId: task.requirementId,\r\n              name: task.taskName,\r\n              description: task.taskDescription,\r\n              executeTime: task.executeTime,\r\n              executeDateTime: task.frequency === 'once' && task.execute_date ? `${task.execute_date} ${task.executeTime}` : '',\r\n              frequency: task.frequency,\r\n              status: task.status === 'running' ? 'running' : 'pending',\r\n              pushUrl: task.pushUrl\r\n            }))\r\n          } else if (response.records && Array.isArray(response.records)) {\r\n            this.timedTaskList = response.records.map(task => ({\r\n              id: task.id,\r\n              requirementId: task.requirementId,\r\n              name: task.taskName,\r\n              description: task.taskDescription,\r\n              executeTime: task.executeTime,\r\n              executeDateTime: task.frequency === 'once' && task.execute_date ? `${task.execute_date} ${task.executeTime}` : '',\r\n              frequency: task.frequency,\r\n              status: task.status === 'running' ? 'running' : 'pending',\r\n              pushUrl: task.pushUrl\r\n            }))\r\n          } else {\r\n            this.timedTaskList = []\r\n          }\r\n        } else {\r\n          this.$message.error(response.msg || '获取定时任务列表失败')\r\n          this.timedTaskList = []\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('加载定时任务列表失败')\r\n        this.timedTaskList = []\r\n      }\r\n    },\r\n\r\n    // 关闭创建任务弹窗\r\n    closeCreateTaskDialog() {\r\n      this.createTaskDialogVisible = false\r\n      this.resetTaskForm()\r\n      this.editingTaskIndex = -1 // 重置编辑状态\r\n    },\r\n\r\n    // 重置任务表单\r\n    resetTaskForm() {\r\n      this.taskForm = {\r\n        requirement_id: '',\r\n        name: '',\r\n        description: '',\r\n        execute_time: '16:00',\r\n        execute_date_time: '',\r\n        frequency: 'daily',\r\n        push_url: ''\r\n      }\r\n    },\r\n\r\n    // 保存并运行任务\r\n    async saveAndRunTask() {\r\n      if (!this.validateTaskForm()) {\r\n        return\r\n      }\r\n\r\n      try {\r\n        const taskData = this.buildTaskData()\r\n\r\n        if (this.editingTaskIndex === -1) {\r\n          // 新建任务\r\n          const response = await createTimedTask(taskData)\r\n          if (response.success) {\r\n            this.$message.success('任务已保存并开始运行')\r\n            // 重新加载任务列表\r\n            await this.loadTimedTaskList()\r\n            // 更新任务状态为运行中\r\n            if (response.data && response.data.id) {\r\n              await updateTaskStatus(response.data.id, 'running')\r\n              await this.loadTimedTaskList()\r\n            }\r\n          } else {\r\n            this.$message.error('创建任务失败：' + response.msg)\r\n            return\r\n          }\r\n        } else {\r\n          // 编辑任务\r\n          const task = this.timedTaskList[this.editingTaskIndex]\r\n          const response = await updateTimedTask(task.id, taskData)\r\n\r\n          if (response.success) {\r\n            // 更新任务状态为运行中\r\n            await updateTaskStatus(task.id, 'running')\r\n            this.$message.success('任务已更新并开始运行')\r\n            // 重新加载任务列表\r\n            await this.loadTimedTaskList()\r\n          } else {\r\n            this.$message.error('更新任务失败：' + response.msg)\r\n            return\r\n          }\r\n        }\r\n\r\n        this.createTaskDialogVisible = false\r\n        this.resetTaskForm()\r\n        this.editingTaskIndex = -1 // 重置编辑状态\r\n      } catch (error) {\r\n        this.$message.error('保存任务失败，请重试')\r\n      }\r\n    },\r\n\r\n    // 保存任务计划\r\n    async saveTaskPlan() {\r\n      if (!this.validateTaskForm()) {\r\n        return\r\n      }\r\n\r\n      try {\r\n        const taskData = this.buildTaskData()\r\n\r\n        if (this.editingTaskIndex === -1) {\r\n          // 新建任务\r\n          const response = await createTimedTask(taskData)\r\n          if (response.success) {\r\n            this.$message.success('任务计划已保存')\r\n            // 重新加载任务列表\r\n            await this.loadTimedTaskList()\r\n          } else {\r\n            this.$message.error('保存任务计划失败：' + response.msg)\r\n            return\r\n          }\r\n        } else {\r\n          // 编辑任务\r\n          const task = this.timedTaskList[this.editingTaskIndex]\r\n          const response = await updateTimedTask(task.id, taskData)\r\n\r\n          if (response.success) {\r\n            this.$message.success('任务计划已更新')\r\n            // 重新加载任务列表\r\n            await this.loadTimedTaskList()\r\n          } else {\r\n            this.$message.error('更新任务计划失败：' + response.msg)\r\n            return\r\n          }\r\n        }\r\n\r\n        this.createTaskDialogVisible = false\r\n        this.resetTaskForm()\r\n        this.editingTaskIndex = -1 // 重置编辑状态\r\n      } catch (error) {\r\n        this.$message.error('保存任务计划失败，请重试')\r\n      }\r\n    },\r\n\r\n    // 验证任务表单\r\n    validateTaskForm() {\r\n      if (!this.taskForm.requirement_id) {\r\n        this.$message.warning('请选择要关联的舆情分析任务')\r\n        return false\r\n      }\r\n\r\n      if (!this.taskForm.name || !this.taskForm.name.trim()) {\r\n        this.$message.warning('请输入任务名称')\r\n        return false\r\n      }\r\n\r\n      if (!this.taskForm.description || !this.taskForm.description.trim()) {\r\n        this.$message.warning('请输入任务描述')\r\n        return false\r\n      }\r\n\r\n      // 验证执行时间\r\n      if (this.taskForm.frequency === 'once') {\r\n        if (!this.taskForm.execute_date_time) {\r\n          this.$message.warning('请选择执行日期和时间')\r\n          return false\r\n        }\r\n        // 检查是否是未来时间\r\n        const executeTime = new Date(this.taskForm.execute_date_time)\r\n        const now = new Date()\r\n        if (executeTime <= now) {\r\n          this.$message.warning('执行时间必须是未来时间')\r\n          return false\r\n        }\r\n      } else {\r\n        if (!this.taskForm.execute_time) {\r\n          this.$message.warning('请选择执行时间')\r\n          return false\r\n        }\r\n      }\r\n\r\n      return true\r\n    },\r\n\r\n    // 构建任务数据\r\n    buildTaskData() {\r\n      // 处理执行时间：对于一次性任务，只提取时间部分；对于周期性任务，直接使用时间\r\n      let executeTime\r\n      if (this.taskForm.frequency === 'once') {\r\n        // 一次性任务：从完整日期时间中提取时间部分 (HH:MM)\r\n        if (this.taskForm.execute_date_time) {\r\n          const dateTime = new Date(this.taskForm.execute_date_time)\r\n          executeTime = `${dateTime.getHours().toString().padStart(2, '0')}:${dateTime.getMinutes().toString().padStart(2, '0')}`\r\n        } else {\r\n          executeTime = '09:00' // 默认时间\r\n        }\r\n      } else {\r\n        // 周期性任务：直接使用时间\r\n        executeTime = this.taskForm.execute_time || '09:00'\r\n      }\r\n\r\n      const taskData = {\r\n        requirement_id: this.taskForm.requirement_id,\r\n        task_name: this.taskForm.name,\r\n        task_type: 'scheduled',\r\n        frequency: this.taskForm.frequency, // 使用 frequency 而不是 schedule_type\r\n        execute_time: executeTime, // 只存储时间部分 (HH:MM)\r\n        task_description: this.taskForm.description,\r\n        push_url: this.taskForm.push_url || ''\r\n      }\r\n\r\n      // 对于一次性任务，可能需要额外的日期信息\r\n      if (this.taskForm.frequency === 'once' && this.taskForm.execute_date_time) {\r\n        taskData['execute_date'] = this.taskForm.execute_date_time.split(' ')[0] // 提取日期部分 (YYYY-MM-DD)\r\n      }\r\n\r\n      return taskData\r\n    },\r\n\r\n    // 删除任务\r\n    async deleteTask(index) {\r\n      try {\r\n        const task = this.timedTaskList[index]\r\n\r\n        if (!task) {\r\n          this.$message.error('任务数据不存在')\r\n          return\r\n        }\r\n\r\n        await this.$confirm(`确定要删除任务「${task.name}」吗？`, '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        })\r\n\r\n        const response = await deleteTimedTask(task.id)\r\n\r\n        if (response.success) {\r\n          this.$message.success('删除成功')\r\n          // 重新加载任务列表\r\n          await this.loadTimedTaskList()\r\n        } else {\r\n          this.$message.error('删除失败：' + (response.msg || response.message || '未知错误'))\r\n        }\r\n      } catch (error) {\r\n        if (error === 'cancel') {\r\n          return\r\n        }\r\n        this.$message.error('删除任务失败，请重试')\r\n      }\r\n    },\r\n\r\n    // 编辑任务\r\n    editTask(index) {\r\n      const task = this.timedTaskList[index]\r\n\r\n      if (!task) {\r\n        this.$message.error('任务数据不存在')\r\n        return\r\n      }\r\n\r\n      this.editingTaskIndex = index\r\n      this.taskForm = {\r\n        requirement_id: task.requirementId || '',\r\n        name: task.name || '',\r\n        description: task.description || '',\r\n        execute_time: task.executeTime || '16:00',\r\n        execute_date_time: task.executeDateTime || '',\r\n        frequency: task.frequency || 'daily',\r\n        push_url: task.pushUrl || ''\r\n      }\r\n\r\n      this.loadOpinionTaskList() // 加载舆情任务列表\r\n      this.createTaskDialogVisible = true\r\n    },\r\n\r\n    // 切换任务状态\r\n    async toggleTaskStatus(index) {\r\n      try {\r\n        const task = this.timedTaskList[index]\r\n        const newStatus = task.status === 'running' ? 'pending' : 'running'\r\n\r\n        const response = await updateTaskStatus(task.id, newStatus)\r\n\r\n        if (response.success) {\r\n          if (newStatus === 'running') {\r\n            this.$message.success(`任务「${task.name}」已启动`)\r\n          } else {\r\n            this.$message.info(`任务「${task.name}」已暂停`)\r\n          }\r\n          // 重新加载任务列表\r\n          await this.loadTimedTaskList()\r\n        } else {\r\n          this.$message.error('状态更新失败：' + response.msg)\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('状态更新失败，请重试')\r\n      }\r\n    },\r\n\r\n    // 预览任务\r\n    previewTask(index) {\r\n      const task = this.timedTaskList[index]\r\n      if (!task) {\r\n        this.$message.error('任务数据不存在')\r\n        return\r\n      }\r\n      this.taskPreviewDialog.taskData = task\r\n      this.taskPreviewDialog.visible = true\r\n    },\r\n\r\n    // 获取任务计划文本\r\n    getTaskScheduleText(task) {\r\n      if (!task) return ''\r\n\r\n      if (task.frequency === 'once') {\r\n        // 一次性任务显示具体执行时间\r\n        return `仅一次 ${task.executeDateTime || task.executeTime}`\r\n      } else {\r\n        // 周期性任务显示频率和时间\r\n        const frequencyText = this.getFrequencyText(task.frequency)\r\n        return `${frequencyText} ${task.executeTime}`\r\n      }\r\n    },\r\n\r\n    // 获取频率文本\r\n    getFrequencyText(frequency) {\r\n      const frequencyMap = {\r\n        'once': '仅一次',\r\n        'daily': '每天',\r\n        'weekly': '每周',\r\n        'monthly': '每月'\r\n      }\r\n      return frequencyMap[frequency] || frequency\r\n    },\r\n\r\n    // 修改计划\r\n    modifyPlan() {\r\n      // 这里可以添加修改计划的逻辑\r\n      this.$message.info('修改计划功能待实现')\r\n    },\r\n\r\n    /** 时间格式化 */\r\n    parseTime\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n.mb8 {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.report-content {\r\n  min-height: 400px;\r\n\r\n  .analysis-results {\r\n    margin-top: 20px;\r\n\r\n    .statistic-value {\r\n      font-size: 24px;\r\n      font-weight: bold;\r\n      text-align: center;\r\n      padding: 20px 0;\r\n\r\n      &.positive {\r\n        color: #67c23a;\r\n      }\r\n\r\n      &.negative {\r\n        color: #f56c6c;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.el-table .cell {\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.small-padding .cell {\r\n  padding-left: 5px;\r\n  padding-right: 5px;\r\n}\r\n\r\n.fixed-width .el-button--mini {\r\n  margin-right: 5px;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n\r\n// 状态标签样式\r\n.el-tag {\r\n  &.el-tag--info {\r\n    background-color: #f4f4f5;\r\n    border-color: #e9e9eb;\r\n    color: #909399;\r\n  }\r\n\r\n  &.el-tag--warning {\r\n    background-color: #fdf6ec;\r\n    border-color: #f5dab1;\r\n    color: #e6a23c;\r\n  }\r\n\r\n  &.el-tag--success {\r\n    background-color: #f0f9ff;\r\n    border-color: #c6f7d0;\r\n    color: #67c23a;\r\n  }\r\n\r\n  &.el-tag--danger {\r\n    background-color: #fef0f0;\r\n    border-color: #fbc4c4;\r\n    color: #f56c6c;\r\n  }\r\n}\r\n\r\n// 操作按钮样式\r\n.el-button--text {\r\n  padding: 0;\r\n  margin-right: 10px;\r\n\r\n  &:last-child {\r\n    margin-right: 0;\r\n  }\r\n}\r\n\r\n// 搜索表单样式\r\n.el-form--inline .el-form-item {\r\n  margin-right: 10px;\r\n}\r\n\r\n// 定时任务抽屉样式\r\n.timed-task-drawer {\r\n  .drawer-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    width: 100%;\r\n\r\n    .drawer-title {\r\n      font-size: 16px;\r\n      font-weight: 500;\r\n    }\r\n\r\n    .add-task-btn {\r\n      margin-left: auto;\r\n    }\r\n  }\r\n\r\n  .drawer-content {\r\n    padding: 20px;\r\n    height: calc(100vh - 120px);\r\n    overflow-y: auto;\r\n\r\n    .empty-state {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      justify-content: center;\r\n      height: 100%;\r\n      text-align: center;\r\n\r\n      .empty-content {\r\n        .empty-icon {\r\n          margin-bottom: 20px;\r\n        }\r\n\r\n        .empty-text {\r\n          font-size: 14px;\r\n          color: #999;\r\n          margin-bottom: 20px;\r\n        }\r\n\r\n        .create-btn {\r\n          padding: 10px 20px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .task-list {\r\n      .task-items {\r\n        .task-item {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          padding: 16px;\r\n          border: 1px solid #e8e8e8;\r\n          border-radius: 8px;\r\n          margin-bottom: 12px;\r\n          background: #fff;\r\n\r\n          &:hover {\r\n            border-color: #409eff;\r\n            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\r\n          }\r\n\r\n          .task-info {\r\n            flex: 1;\r\n\r\n            .task-header {\r\n              display: flex;\r\n              justify-content: space-between;\r\n              align-items: center;\r\n              margin-bottom: 8px;\r\n\r\n              .task-name {\r\n                font-size: 14px;\r\n                font-weight: 500;\r\n                color: #333;\r\n              }\r\n\r\n              .task-status {\r\n                padding: 2px 8px;\r\n                border-radius: 4px;\r\n                font-size: 12px;\r\n\r\n                &.status-running {\r\n                  background: #f0f9ff;\r\n                  color: #1890ff;\r\n                }\r\n\r\n                &.status-pending {\r\n                  background: #f6f6f6;\r\n                  color: #666;\r\n                }\r\n              }\r\n            }\r\n\r\n            .task-schedule {\r\n              display: flex;\r\n              align-items: center;\r\n              font-size: 12px;\r\n              color: #666;\r\n\r\n              i {\r\n                margin-right: 4px;\r\n              }\r\n            }\r\n          }\r\n\r\n          .task-actions {\r\n            display: flex;\r\n            gap: 8px;\r\n\r\n            .el-button {\r\n              padding: 4px;\r\n              min-width: auto;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 创建任务弹窗样式\r\n.create-task-dialog {\r\n  .task-form {\r\n    .task-requirement-section,\r\n    .execute-time-section {\r\n      margin-bottom: 24px;\r\n\r\n      .section-label {\r\n        font-size: 14px;\r\n        font-weight: 500;\r\n        margin-bottom: 16px;\r\n        color: #333;\r\n\r\n        .required {\r\n          color: #f56c6c;\r\n          margin-left: 4px;\r\n        }\r\n      }\r\n\r\n      .form-group {\r\n        margin-bottom: 16px;\r\n\r\n        .input-label {\r\n          font-size: 13px;\r\n          color: #666;\r\n          margin-bottom: 8px;\r\n        }\r\n\r\n        .task-name-input,\r\n        .task-description-input {\r\n          width: 100%;\r\n        }\r\n      }\r\n\r\n      .time-selector {\r\n        display: flex;\r\n        gap: 12px;\r\n\r\n        .frequency-select {\r\n          width: 120px;\r\n        }\r\n\r\n        .datetime-picker,\r\n        .time-picker {\r\n          flex: 1;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .dialog-footer {\r\n    text-align: right;\r\n\r\n    .el-button {\r\n      margin-left: 8px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAshBA,IAAAA,gBAAA,GAAAC,OAAA;AAcA,IAAAC,MAAA,GAAAD,OAAA;AAAA,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,UAAA;MACA;MACAC,SAAA;MACA;MACAC,WAAA;QACAC,QAAA;QACAC,SAAA;QACAC,eAAA;QACAC,aAAA;QACAC,cAAA;MACA;MACA;MACAC,gBAAA;MACAC,UAAA;MACAC,OAAA;MACA;MACAC,mBAAA;MACAC,aAAA;MACAC,UAAA;MACA;MACAC,iBAAA;MACAC,WAAA;MACAC,QAAA;QACAC,QAAA;QACAC,YAAA;MACA;MACAC,SAAA;QACAF,QAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAJ,YAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAC,yBAAA;MACAC,mBAAA;MACAC,gBAAA;QACAR,QAAA;QACAS,SAAA;QACAC,SAAA;QACAT,YAAA;MACA;MACAU,iBAAA;QACAX,QAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAI,SAAA,GACA;UAAAN,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAJ,YAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAO,aAAA;MACA;MACAC,sBAAA;MAAA;MACAC,aAAA;MAAA;MACAC,uBAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,QAAA;QACAC,cAAA;QAAA;QACA3C,IAAA;QAAA;QACA4C,WAAA;QACAC,YAAA;QACAC,iBAAA;QAAA;QACAX,SAAA;QACAV,QAAA;MACA;MACAsB,eAAA;MAAA;MACA;MACAC,iBAAA;QACAC,OAAA;QACAC,QAAA;QACAhD,OAAA;MACA;IACA;EACA;EACAiD,OAAA,WAAAA,QAAA;IACA;IACA,KAAA1C,SAAA;IACA,KAAA2C,OAAA;IACA;IACA;EACA;EACAC,OAAA;IACA,eACAD,OAAA,WAAAA,QAAAE,gBAAA;MAAA,IAAAC,KAAA;MACA,KAAArD,OAAA;;MAEA;MACA,IAAAoD,gBAAA;QACA,KAAA5C,WAAA,CAAAC,QAAA,GAAA2C,gBAAA,CAAAE,IAAA;QACA,KAAA9C,WAAA,CAAAE,SAAA,GAAA0C,gBAAA,CAAAG,KAAA;MACA;MAEA,IAAAC,MAAA;QACA/C,QAAA,OAAAD,WAAA,CAAAC,QAAA;QACAC,SAAA,OAAAF,WAAA,CAAAE;MACA;;MAEA;MACA,SAAAF,WAAA,CAAAG,eAAA,SAAAH,WAAA,CAAAG,eAAA,CAAA8C,IAAA;QACAD,MAAA,CAAAE,SAAA,QAAAlD,WAAA,CAAAG,eAAA,CAAA8C,IAAA;MACA;MACA,SAAAjD,WAAA,CAAAI,aAAA,SAAAJ,WAAA,CAAAI,aAAA,CAAA6C,IAAA;QACAD,MAAA,CAAAG,gBAAA,QAAAnD,WAAA,CAAAI,aAAA,CAAA6C,IAAA;MACA;MACA,SAAAjD,WAAA,CAAAK,cAAA,kBAAAL,WAAA,CAAAK,cAAA,KAAA+C,SAAA,SAAApD,WAAA,CAAAK,cAAA;QACA;QACA,IAAAgD,SAAA;UAAA;UAAA;UAAA;UAAA;QAAA;QACAL,MAAA,CAAAM,MAAA,GAAAD,SAAA,MAAArD,WAAA,CAAAK,cAAA;MACA;;MAEA;MACA,SAAAN,SAAA,SAAAA,SAAA,CAAAwD,MAAA;QACAP,MAAA,CAAAQ,iBAAA,QAAAzD,SAAA;QACAiD,MAAA,CAAAS,eAAA,QAAA1D,SAAA;QACA2D,OAAA,CAAAC,GAAA,sBAAA5D,SAAA;MACA;;MAEA;MACA2D,OAAA,CAAAC,GAAA,mBAAAX,MAAA;MACA,IAAAY,oCAAA,EAAAZ,MAAA,EAAAa,IAAA,WAAAC,QAAA;QACAJ,OAAA,CAAAC,GAAA,cAAAG,QAAA;QACA;;QAEA;QACA,IAAAC,YAAA;QACA,IAAAD,QAAA,CAAAE,OAAA;UACA;UACAD,YAAA,GAAAD,QAAA,CAAAE,OAAA;QACA,WAAAF,QAAA,CAAAvE,IAAA,IAAAuE,QAAA,CAAAvE,IAAA,CAAAyE,OAAA;UACA;UACAD,YAAA,GAAAD,QAAA,CAAAvE,IAAA,CAAAyE,OAAA;QACA,WAAAF,QAAA,CAAAvE,IAAA,IAAAuE,QAAA,CAAAvE,IAAA,CAAA0E,IAAA;UACA;UACAF,YAAA,GAAAD,QAAA,CAAAvE,IAAA,CAAA0E,IAAA;QACA,WAAAH,QAAA,CAAAvE,IAAA,IAAA2E,KAAA,CAAAC,OAAA,CAAAL,QAAA,CAAAvE,IAAA;UACA;UACAwE,YAAA,GAAAD,QAAA,CAAAvE,IAAA;QACA,WAAAuE,QAAA,CAAAG,IAAA;UACA;UACAF,YAAA,GAAAD,QAAA,CAAAG,IAAA;QACA;UACAF,YAAA;QACA;QACAlB,KAAA,CAAA/C,UAAA,GAAAiE,YAAA,CACAK,MAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAC,EAAA,YAAAD,IAAA,CAAAC,EAAA,KAAAlB,SAAA,IAAAiB,IAAA,CAAAC,EAAA;QAAA;QAAA,CACAC,GAAA,WAAAF,IAAA;UACA;UACA,IAAAC,EAAA,GAAAE,QAAA,CAAAH,IAAA,CAAAC,EAAA;UACA,IAAAG,KAAA,CAAAH,EAAA;YACA;UACA;UAEA,IAAAI,UAAA;YACAJ,EAAA,EAAAA,EAAA;YAAA;YACAnE,eAAA,EAAAkE,IAAA,CAAAnB,SAAA,IAAAmB,IAAA,CAAAM,QAAA;YAAA;YACAvE,aAAA,EAAAiE,IAAA,CAAAlB,gBAAA,IAAAkB,IAAA,CAAAO,eAAA;YAAA;YACAC,mBAAA,EAAAR,IAAA,CAAAlB,gBAAA,IAAAkB,IAAA,CAAAO,eAAA;YAAA;YACAE,UAAA,EAAAT,IAAA,CAAAU,WAAA,IAAAV,IAAA,CAAAS,UAAA;YACA;YACA;YACAxB,MAAA,EAAAT,KAAA,CAAAmC,aAAA,CAAAX,IAAA,CAAAf,MAAA;YACA;YACA2B,YAAA,EAAAZ,IAAA,CAAAa,cAAA,IAAAb,IAAA,CAAAY,YAAA;UACA;UACA,OAAAP,UAAA;QACA,GACAN,MAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA;QAAA;;QAEA;QACA,IAAAP,QAAA,CAAAjE,KAAA,KAAAuD,SAAA;UACAP,KAAA,CAAAhD,KAAA,GAAAiE,QAAA,CAAAjE,KAAA;QACA,WAAAiE,QAAA,CAAAvE,IAAA,IAAAuE,QAAA,CAAAvE,IAAA,CAAAM,KAAA,KAAAuD,SAAA;UACAP,KAAA,CAAAhD,KAAA,GAAAiE,QAAA,CAAAvE,IAAA,CAAAM,KAAA;QACA;UACAgD,KAAA,CAAAhD,KAAA;QACA;QAEAgD,KAAA,CAAArD,OAAA;MACA,GAAA2F,KAAA,WAAAC,KAAA;QACAvC,KAAA,CAAAwC,MAAA,CAAAC,QAAA,mBAAAF,KAAA,CAAAjE,OAAA;QACA0B,KAAA,CAAArD,OAAA;MACA;IACA;IAEA,aACAwF,aAAA,WAAAA,cAAAO,UAAA;MACA;MACA;MACA,IAAAlC,SAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,SAAA,CAAAkC,UAAA,MAAAnC,SAAA,GAAAC,SAAA,CAAAkC,UAAA;IACA;IAEA,oBACAC,iBAAA,WAAAA,kBAAAnF,cAAA;MACA;MACA;MACA,IAAAgD,SAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,SAAA,CAAAhD,cAAA,MAAA+C,SAAA,GAAAC,SAAA,CAAAhD,cAAA;IACA;IAEA,eACAoF,sBAAA,WAAAA,uBAAAC,KAAA;MACAhC,OAAA,CAAAC,GAAA,iBAAA+B,KAAA;MACA;MACA;IACA;IAEA,cACAC,oBAAA,WAAAA,qBAAAD,KAAA;MACAhC,OAAA,CAAAC,GAAA,gBAAA+B,KAAA;MACA;MACA;IACA;IAEA,eACAE,sBAAA,WAAAA,uBAAAF,KAAA;MACAhC,OAAA,CAAAC,GAAA,eAAA+B,KAAA;MACA;MACA,KAAAG,WAAA;IACA;IAEA,aACAA,WAAA,WAAAA,YAAA;MACA;MACA,KAAA7F,WAAA,CAAAC,QAAA;MACA;MACAyD,OAAA,CAAAC,GAAA,uBAAA3D,WAAA;MACA,KAAA0C,OAAA;IACA;IAEA,aACAoD,UAAA,WAAAA,WAAA;MACA,KAAA/F,SAAA;MACA;MACA,KAAAC,WAAA;QACAC,QAAA;QACAC,SAAA;QACAC,eAAA;QACAC,aAAA;QACAC,cAAA;MACA;MACA;MACA,KAAA0F,SAAA;MACA;MACArC,OAAA,CAAAC,GAAA,yBAAA3D,WAAA;MACA,KAAA6F,WAAA;IACA;IAEA,cACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA;MACA,KAAAxG,GAAA,GAAAwG,SAAA,CACA7B,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,EAAA,YAAAD,IAAA,CAAAC,EAAA,KAAAlB,SAAA;MAAA,GACAmB,GAAA,WAAAF,IAAA;QAAA,OAAAG,QAAA,CAAAH,IAAA,CAAAC,EAAA;MAAA;MAEA,KAAA5E,MAAA,GAAAuG,SAAA,CAAA1C,MAAA;MACA,KAAA5D,QAAA,IAAAsG,SAAA,CAAA1C,MAAA;IACA;IAEA,aACA2C,cAAA,WAAAA,eAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAzE,aAAA,GAAAwE,GAAA;MACA,KAAA7F,gBAAA;MACA,KAAAC,UAAA;;MAEA;MACA,IAAA8F,oCAAA,EAAAF,GAAA,CAAAG,MAAA,IAAAH,GAAA,CAAA7B,EAAA,EAAAT,IAAA,WAAAC,QAAA;QACAsC,MAAA,CAAA5F,OAAA,GAAAsD,QAAA,CAAAvE,IAAA;QACA6G,MAAA,CAAA7F,UAAA;MACA,GAAA4E,KAAA;QACAiB,MAAA,CAAA7F,UAAA;MACA;IACA;IAEA,WACAgG,gBAAA,WAAAA,iBAAAJ,GAAA;MAAA,IAAAK,MAAA;MACA,KAAA7E,aAAA,GAAAwE,GAAA;MACA,KAAA1F,mBAAA;MACA,KAAAC,aAAA;;MAEA;MACA+F,UAAA;QACAD,MAAA,CAAA7F,UAAA;UACAR,eAAA,EAAAgG,GAAA,CAAAhG,eAAA;UACAC,aAAA,EAAA+F,GAAA,CAAA/F,aAAA;UACAyE,mBAAA,EAAAsB,GAAA,CAAAtB,mBAAA;UACA6B,aAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;UACAC,SAAA;YACAC,QAAA,EAAAJ,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;YACAG,OAAA,EAAAL,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;YACAI,QAAA,EAAAN,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;UACA;QACA;QACAL,MAAA,CAAA9F,aAAA;MACA;IACA;IAEA,SACAwG,YAAA,WAAAA,aAAAf,GAAA;MACA;MACA,IAAAA,GAAA,CAAAlB,YAAA;QACA;QACAkC,MAAA,CAAAC,IAAA,CAAAjB,GAAA,CAAAlB,YAAA;MACA;QACA;QACA,KAAAI,MAAA,CAAAgC,GAAA;MACA;IACA;IAEA,WACAC,aAAA,WAAAA,cAAAnB,GAAA;MACA;MACA;;MAEA,KAAAxE,aAAA,GAAAwE,GAAA;MACA,KAAArF,QAAA;QACAC,QAAA;QACAC,YAAA,mCAAAuG,MAAA,CAAApB,GAAA,CAAAhG,eAAA;MACA;MACA,KAAAS,iBAAA;IACA;IAEA,WACA4G,kBAAA,WAAAA,mBAAArB,GAAA;MACA,KAAAxE,aAAA,GAAAwE,GAAA;MACA,KAAA5E,gBAAA;QACAR,QAAA;QACAS,SAAA;QACAC,SAAA;QACAT,YAAA,mCAAAuG,MAAA,CAAApB,GAAA,CAAAhG,eAAA;MACA;MACA,KAAAkB,yBAAA;IACA;IAEA,aACAoG,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,aAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAA7G,WAAA;;UAEA;UACA;UACA,IAAAiH,QAAA;YACAC,UAAA,EAAAL,MAAA,CAAA5G,QAAA,CAAAC,QAAA;YACAiH,WAAA;cACA7H,eAAA,EAAAuH,MAAA,CAAA/F,aAAA,CAAAxB,eAAA;cACAC,aAAA,EAAAsH,MAAA,CAAA/F,aAAA,CAAAvB,aAAA;cACAyE,mBAAA,EAAA6C,MAAA,CAAA/F,aAAA,CAAAkD,mBAAA;cACAI,YAAA,EAAAyC,MAAA,CAAA/F,aAAA,CAAAsD,YAAA;cAAA;cACAyB,aAAA;cAAA;cACAI,SAAA;cAAA;cACAmB,WAAA;cAAA;cACA;cACA3B,MAAA,EAAAoB,MAAA,CAAA/F,aAAA,CAAA2C,EAAA;cACAQ,UAAA,EAAA4C,MAAA,CAAA/F,aAAA,CAAAmD,UAAA;cACAxB,MAAA,EAAAoE,MAAA,CAAA/F,aAAA,CAAA2B;YACA;YACA4E,gBAAA;cACAC,OAAA,EAAAT,MAAA,CAAA5G,QAAA,CAAAE,YAAA;cACAoH,SAAA,EAAAV,MAAA,CAAA/F,aAAA,CAAAsD,YAAA;YACA;YACAhD,cAAA,EAAAyF,MAAA,CAAA/F,aAAA,CAAA2C,EAAA;YACA+D,SAAA;UACA;UAEA,IAAAC,2BAAA,EAAAR,QAAA,EAAAjE,IAAA,WAAAC,QAAA;YACA;YACA,IAAAA,QAAA,IAAAA,QAAA,CAAAyE,OAAA;cACAb,MAAA,CAAArC,MAAA,CAAAmD,UAAA;;cAEA;cACA,IAAA1E,QAAA,CAAAvE,IAAA,IAAAuE,QAAA,CAAAvE,IAAA,CAAAkJ,UAAA,KAAAf,MAAA,CAAA/F,aAAA,CAAAsD,YAAA;gBACAyC,MAAA,CAAA/F,aAAA,CAAAsD,YAAA,GAAAnB,QAAA,CAAAvE,IAAA,CAAAkJ,UAAA;gBACA/E,OAAA,CAAAC,GAAA,eAAAG,QAAA,CAAAvE,IAAA,CAAAkJ,UAAA;cACA;YACA;cACAf,MAAA,CAAArC,MAAA,CAAAmD,UAAA;YACA;YAEAd,MAAA,CAAA9G,iBAAA;YACA8G,MAAA,CAAA7G,WAAA;UACA,GAAAsE,KAAA,WAAAC,KAAA;YACA1B,OAAA,CAAA0B,KAAA,UAAAA,KAAA;YACA,IAAAA,KAAA,CAAAtB,QAAA,IAAAsB,KAAA,CAAAtB,QAAA,CAAAvE,IAAA,IAAA6F,KAAA,CAAAtB,QAAA,CAAAvE,IAAA,CAAA8H,GAAA;cACAK,MAAA,CAAArC,MAAA,CAAAC,QAAA,YAAAF,KAAA,CAAAtB,QAAA,CAAAvE,IAAA,CAAA8H,GAAA;YACA;cACAK,MAAA,CAAArC,MAAA,CAAAC,QAAA;YACA;YACAoC,MAAA,CAAA7G,WAAA;UACA;QACA;MACA;IACA;IAEA,aACA6H,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,MAAA;MACA,KAAAhB,KAAA,qBAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAc,MAAA,CAAArH,mBAAA;UACA,IAAAkB,QAAA;YACAP,cAAA,EAAA0G,MAAA,CAAAhH,aAAA,CAAA2C,EAAA;YACAvD,QAAA,EAAA4H,MAAA,CAAApH,gBAAA,CAAAR,QAAA;YACAS,SAAA,EAAAmH,MAAA,CAAApH,gBAAA,CAAAC,SAAA;YACAC,SAAA,EAAAkH,MAAA,CAAApH,gBAAA,CAAAE,SAAA;YACAT,YAAA,EAAA2H,MAAA,CAAApH,gBAAA,CAAAP;UACA;UAEA,IAAA4H,gCAAA,EAAApG,QAAA,EAAAqB,IAAA,WAAAC,QAAA;YACA6E,MAAA,CAAAtD,MAAA,CAAAmD,UAAA;YACAG,MAAA,CAAAtH,yBAAA;YACAsH,MAAA,CAAArH,mBAAA;UACA,GAAA6D,KAAA;YACAwD,MAAA,CAAArH,mBAAA;UACA;QACA;MACA;IACA;IAEA,aACAuH,YAAA,WAAAA,aAAA1C,GAAA;MAAA,IAAA2C,MAAA;MACA,IAAArJ,GAAA,EAAAsJ,KAAA;;MAEA;MACA,KAAA5C,GAAA,IAAAA,GAAA,YAAA6C,KAAA;QACA;QACA,UAAAvJ,GAAA,SAAAA,GAAA,CAAA8D,MAAA;UACA,KAAA8B,MAAA,CAAAC,QAAA;UACA;QACA;QACA;QACA7F,GAAA,QAAAA,GAAA,CAAA2E,MAAA,WAAAE,EAAA;UAAA,OAAAA,EAAA,aAAAG,KAAA,CAAAH,EAAA;QAAA,GAAAC,GAAA,WAAAD,EAAA;UAAA,OAAAE,QAAA,CAAAF,EAAA;QAAA;QACA,IAAA7E,GAAA,CAAA8D,MAAA;UACA,KAAA8B,MAAA,CAAAC,QAAA;UACA;QACA;QACAyD,KAAA,QAAAjJ,UAAA,CAAAsE,MAAA,WAAAC,IAAA;UAAA,OAAA5E,GAAA,CAAAwJ,QAAA,CAAA5E,IAAA,CAAAC,EAAA;QAAA,GAAAC,GAAA,WAAAF,IAAA;UAAA,OAAAA,IAAA,CAAAlE,eAAA;QAAA,GAAA+I,IAAA;MACA;QACA;QACA,KAAA/C,GAAA,CAAA7B,EAAA,IAAA6B,GAAA,CAAA7B,EAAA;UACA,KAAAe,MAAA,CAAAC,QAAA;UACA;QACA;QACA7F,GAAA,IAAA+E,QAAA,CAAA2B,GAAA,CAAA7B,EAAA;QACAyE,KAAA,GAAA5C,GAAA,CAAAhG,eAAA;MACA;MACA,KAAAkF,MAAA,CAAA8D,OAAA,kEAAA5B,MAAA,CAAAwB,KAAA,eAAAlF,IAAA;QACA;QACA,IAAApE,GAAA,CAAA8D,MAAA;UACA;UACA,WAAA6F,mCAAA,EAAA3J,GAAA;QACA;UACA;UACA,WAAA4J,yCAAA,EAAA5J,GAAA;QACA;MACA,GAAAoE,IAAA,WAAAC,QAAA;QACAJ,OAAA,CAAAC,GAAA,mBAAAG,QAAA;;QAEA;QACA,IAAAwF,kBAAA,GAAAR,MAAA,CAAAhJ,UAAA,CAAAyD,MAAA;QACA,IAAAgG,YAAA,GAAA9J,GAAA,CAAA8D,MAAA;;QAEA;QACA,IAAA+F,kBAAA,IAAAC,YAAA,IAAAT,MAAA,CAAA9I,WAAA,CAAAC,QAAA;UACA6I,MAAA,CAAA9I,WAAA,CAAAC,QAAA,GAAA6I,MAAA,CAAA9I,WAAA,CAAAC,QAAA;UACAyD,OAAA,CAAAC,GAAA,iBAAAmF,MAAA,CAAA9I,WAAA,CAAAC,QAAA;QACA;;QAEA;QACA6I,MAAA,CAAArJ,GAAA;QACAqJ,MAAA,CAAApJ,MAAA;QACAoJ,MAAA,CAAAnJ,QAAA;;QAEA;QACAmJ,MAAA,CAAApG,OAAA;QAEAoG,MAAA,CAAAzD,MAAA,CAAAmD,UAAA;MACA,GAAArD,KAAA,WAAAC,KAAA;QACA,IAAAA,KAAA,CAAAtB,QAAA,IAAAsB,KAAA,CAAAtB,QAAA,CAAAvE,IAAA;UACA,IAAA6F,KAAA,CAAAtB,QAAA,CAAAvE,IAAA,CAAA8H,GAAA;YACAyB,MAAA,CAAAzD,MAAA,CAAAC,QAAA,CAAAF,KAAA,CAAAtB,QAAA,CAAAvE,IAAA,CAAA8H,GAAA;UACA,WAAAjC,KAAA,CAAAtB,QAAA,CAAAvE,IAAA,CAAA4B,OAAA;YACA2H,MAAA,CAAAzD,MAAA,CAAAC,QAAA,CAAAF,KAAA,CAAAtB,QAAA,CAAAvE,IAAA,CAAA4B,OAAA;UACA,WAAAiE,KAAA,CAAAtB,QAAA,CAAAvE,IAAA,CAAAiK,MAAA;YACA;YACA,IAAAtF,KAAA,CAAAC,OAAA,CAAAiB,KAAA,CAAAtB,QAAA,CAAAvE,IAAA,CAAAiK,MAAA;cACA,IAAAC,OAAA,GAAArE,KAAA,CAAAtB,QAAA,CAAAvE,IAAA,CAAAiK,MAAA,CAAAjF,GAAA,WAAAmF,CAAA;gBAAA,OAAAA,CAAA,CAAArC,GAAA,IAAAqC,CAAA,CAAAvI,OAAA,IAAAwI,IAAA,CAAAC,SAAA,CAAAF,CAAA;cAAA,GAAAR,IAAA;cACAJ,MAAA,CAAAzD,MAAA,CAAAC,QAAA,8BAAAiC,MAAA,CAAAkC,OAAA;YACA;cACAX,MAAA,CAAAzD,MAAA,CAAAC,QAAA,8BAAAiC,MAAA,CAAAnC,KAAA,CAAAtB,QAAA,CAAAvE,IAAA,CAAAiK,MAAA;YACA;UACA;YACAV,MAAA,CAAAzD,MAAA,CAAAC,QAAA,8BAAAiC,MAAA,CAAAoC,IAAA,CAAAC,SAAA,CAAAxE,KAAA,CAAAtB,QAAA,CAAAvE,IAAA;UACA;QACA,WAAA6F,KAAA,CAAAjE,OAAA;UACA2H,MAAA,CAAAzD,MAAA,CAAAC,QAAA,8BAAAiC,MAAA,CAAAnC,KAAA,CAAAjE,OAAA;QACA;UACA2H,MAAA,CAAAzD,MAAA,CAAAC,QAAA;QACA;MACA;IACA;IAEA,aACAuE,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAzE,MAAA,CAAA8D,OAAA,qBAAAtF,IAAA;QACA,IAAAb,MAAA;;QAEA;QACA,IAAA8G,MAAA,CAAA9J,WAAA,CAAAG,eAAA;UACA6C,MAAA,CAAA+G,gBAAA,GAAAD,MAAA,CAAA9J,WAAA,CAAAG,eAAA;QACA;QACA,IAAA2J,MAAA,CAAA9J,WAAA,CAAAI,aAAA;UACA4C,MAAA,CAAAgH,cAAA,GAAAF,MAAA,CAAA9J,WAAA,CAAAI,aAAA;QACA;QACA,IAAA0J,MAAA,CAAA9J,WAAA,CAAAK,cAAA,aAAAyJ,MAAA,CAAA9J,WAAA,CAAAK,cAAA,KAAA+C,SAAA,IAAA0G,MAAA,CAAA9J,WAAA,CAAAK,cAAA;UACA2C,MAAA,CAAAiH,eAAA,GAAAH,MAAA,CAAA9J,WAAA,CAAAK,cAAA;QACA;;QAEA;QACA,IAAAyJ,MAAA,CAAA/J,SAAA,IAAA+J,MAAA,CAAA/J,SAAA,CAAAwD,MAAA;UACAP,MAAA,CAAAQ,iBAAA,GAAAsG,MAAA,CAAA/J,SAAA;UACAiD,MAAA,CAAAS,eAAA,GAAAqG,MAAA,CAAA/J,SAAA;QACA;QAEA,WAAAmK,mCAAA,EAAAlH,MAAA;MACA,GAAAa,IAAA,WAAAC,QAAA;QACA;QACA,IAAAqG,IAAA,OAAAC,IAAA,EAAAtG,QAAA;UAAAuG,IAAA;QAAA;QACA,IAAAC,GAAA,GAAAnD,MAAA,CAAAoD,GAAA,CAAAC,eAAA,CAAAL,IAAA;QACA,IAAAM,IAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,IAAA,CAAAG,IAAA,GAAAN,GAAA;QACAG,IAAA,CAAAI,QAAA,+BAAAtD,MAAA,KAAAuD,IAAA,GAAAC,WAAA,GAAAC,KAAA;QACAN,QAAA,CAAAO,IAAA,CAAAC,WAAA,CAAAT,IAAA;QACAA,IAAA,CAAAU,KAAA;QACAT,QAAA,CAAAO,IAAA,CAAAG,WAAA,CAAAX,IAAA;QACAtD,MAAA,CAAAoD,GAAA,CAAAc,eAAA,CAAAf,GAAA;QAEAR,MAAA,CAAAzE,MAAA,CAAAmD,UAAA;MACA,GAAArD,KAAA,WAAAC,KAAA;QACA,IAAAA,KAAA,CAAAtB,QAAA,IAAAsB,KAAA,CAAAtB,QAAA,CAAAvE,IAAA,IAAA6F,KAAA,CAAAtB,QAAA,CAAAvE,IAAA,CAAA8H,GAAA;UACAyC,MAAA,CAAAzE,MAAA,CAAAC,QAAA,CAAAF,KAAA,CAAAtB,QAAA,CAAAvE,IAAA,CAAA8H,GAAA;QACA;UACAyC,MAAA,CAAAzE,MAAA,CAAAC,QAAA;QACA;MACA;IACA;IAEA,WACAgG,kBAAA,WAAAA,mBAAA;MACA,KAAAjG,MAAA,CAAAmD,UAAA;IACA;IAEA,eACA+C,gBAAA,WAAAA,iBAAAjI,MAAA;MACA,IAAAD,SAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,SAAA,CAAAC,MAAA;IACA;IAEA,aACAkI,aAAA,WAAAA,cAAAlI,MAAA;MACA,IAAAD,SAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAC,MAAA;IACA;IAEA,iBACAmI,kBAAA,WAAAA,mBAAAC,KAAA;MACA,IAAAC,QAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,QAAA,CAAAD,KAAA;IACA;IAEA;IAEA;IACAE,eAAA,WAAAA,gBAAA;MACA,KAAAhK,sBAAA;MACA;MACA,KAAAiK,iBAAA;IACA;IAEA;IACAC,oBAAA,WAAAA,qBAAA;MACA,KAAAlK,sBAAA;IACA;IAEA;IACAmK,qBAAA,WAAAA,sBAAA;MACA,KAAAC,aAAA;MACA,KAAAjK,gBAAA;MACA,KAAAkK,mBAAA;MACA,KAAAnK,uBAAA;IACA;IAEA;IACAoK,kBAAA,WAAAA,mBAAA;MACA,KAAAF,aAAA;MACA,KAAAjK,gBAAA;MACA,KAAAkK,mBAAA;MACA,KAAAnK,uBAAA;IACA;IAEA;IACAmK,mBAAA,WAAAA,oBAAA;MAAA,IAAAE,MAAA;MAAA,WAAAC,kBAAA,CAAA/M,OAAA,mBAAAgN,aAAA,CAAAhN,OAAA,IAAAiN,CAAA,UAAAC,QAAA;QAAA,IAAAzI,QAAA,EAAA0I,KAAA,EAAAC,EAAA;QAAA,WAAAJ,aAAA,CAAAhN,OAAA,IAAAqN,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAF,QAAA,CAAAC,CAAA;cAAA,OAEA,IAAAhJ,oCAAA;gBACA3D,QAAA;gBACAC,SAAA;cACA;YAAA;cAHA4D,QAAA,GAAA6I,QAAA,CAAAG,CAAA;cAKA,IAAAhJ,QAAA,CAAAyE,OAAA;gBACA;gBACAiE,KAAA;gBAEA,IAAA1I,QAAA,CAAAvE,IAAA,IAAAuE,QAAA,CAAAvE,IAAA,CAAA0E,IAAA;kBACAuI,KAAA,GAAA1I,QAAA,CAAAvE,IAAA,CAAA0E,IAAA;gBACA,WAAAC,KAAA,CAAAC,OAAA,CAAAL,QAAA,CAAAvE,IAAA;kBACAiN,KAAA,GAAA1I,QAAA,CAAAvE,IAAA;gBACA,WAAAuE,QAAA,CAAAG,IAAA;kBACAuI,KAAA,GAAA1I,QAAA,CAAAG,IAAA;gBACA;;gBAEA;gBACAkI,MAAA,CAAA9J,eAAA,GAAAmK,KAAA,CAAAjI,GAAA,WAAAwI,IAAA;kBAAA;oBACAzI,EAAA,EAAAyI,IAAA,CAAAzI,EAAA;oBACAnE,eAAA,EAAA4M,IAAA,CAAA5M,eAAA,IAAA4M,IAAA,CAAAhD,gBAAA,mBAAAxC,MAAA,CAAAwF,IAAA,CAAAzI,EAAA;oBACAgC,MAAA,EAAAyG,IAAA,CAAAzI,EAAA;oBACA0I,QAAA,EAAAD,IAAA,CAAAC,QAAA,IAAAD,IAAA,CAAA/C,cAAA;oBACAnF,mBAAA,EAAAkI,IAAA,CAAAlI,mBAAA,IAAAkI,IAAA,CAAAE,oBAAA;oBACAnI,UAAA,EAAAiI,IAAA,CAAAjI,UAAA,IAAAiI,IAAA,CAAAhI;kBACA;gBAAA;cACA;gBACAoH,MAAA,CAAAe,QAAA,CAAA9H,KAAA,CAAAtB,QAAA,CAAAuD,GAAA;gBACA8E,MAAA,CAAA9J,eAAA;cACA;cAAAsK,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAJ,EAAA,GAAAE,QAAA,CAAAG,CAAA;cAEAX,MAAA,CAAAe,QAAA,CAAA9H,KAAA;cACA+G,MAAA,CAAA9J,eAAA;YAAA;cAAA,OAAAsK,QAAA,CAAAQ,CAAA;UAAA;QAAA,GAAAZ,OAAA;MAAA;IAEA;IAEA;IACAV,iBAAA,WAAAA,kBAAA;MAAA,IAAAuB,MAAA;MAAA,WAAAhB,kBAAA,CAAA/M,OAAA,mBAAAgN,aAAA,CAAAhN,OAAA,IAAAiN,CAAA,UAAAe,SAAA;QAAA,IAAAvJ,QAAA,EAAAwJ,GAAA;QAAA,WAAAjB,aAAA,CAAAhN,OAAA,IAAAqN,CAAA,WAAAa,SAAA;UAAA,kBAAAA,SAAA,CAAAX,CAAA;YAAA;cAAAW,SAAA,CAAAV,CAAA;cAAAU,SAAA,CAAAX,CAAA;cAAA,OAEA,IAAAY,iCAAA;YAAA;cAAA1J,QAAA,GAAAyJ,SAAA,CAAAT,CAAA;cAEA,IAAAhJ,QAAA,CAAAyE,OAAA;gBACA;;gBAEA,IAAAzE,QAAA,CAAAvE,IAAA,IAAAuE,QAAA,CAAAvE,IAAA,CAAA0E,IAAA;kBACAmJ,MAAA,CAAAvL,aAAA,GAAAiC,QAAA,CAAAvE,IAAA,CAAA0E,IAAA,CAAAM,GAAA,WAAAwI,IAAA;oBAAA;sBACAzI,EAAA,EAAAyI,IAAA,CAAAzI,EAAA;sBACAmJ,aAAA,EAAAV,IAAA,CAAAU,aAAA;sBACAnO,IAAA,EAAAyN,IAAA,CAAApI,QAAA;sBACAzC,WAAA,EAAA6K,IAAA,CAAAnI,eAAA;sBACA8I,WAAA,EAAAX,IAAA,CAAAW,WAAA;sBACAC,eAAA,EAAAZ,IAAA,CAAAtL,SAAA,eAAAsL,IAAA,CAAAa,YAAA,MAAArG,MAAA,CAAAwF,IAAA,CAAAa,YAAA,OAAArG,MAAA,CAAAwF,IAAA,CAAAW,WAAA;sBACAjM,SAAA,EAAAsL,IAAA,CAAAtL,SAAA;sBACA6B,MAAA,EAAAyJ,IAAA,CAAAzJ,MAAA;sBACAuK,OAAA,EAAAd,IAAA,CAAAc;oBACA;kBAAA;gBACA,WAAA/J,QAAA,CAAAvE,IAAA,IAAAuE,QAAA,CAAAvE,IAAA,CAAAyE,OAAA,IAAAE,KAAA,CAAAC,OAAA,CAAAL,QAAA,CAAAvE,IAAA,CAAAyE,OAAA;kBACAoJ,MAAA,CAAAvL,aAAA,GAAAiC,QAAA,CAAAvE,IAAA,CAAAyE,OAAA,CAAAO,GAAA,WAAAwI,IAAA;oBAAA;sBACAzI,EAAA,EAAAyI,IAAA,CAAAzI,EAAA;sBACAmJ,aAAA,EAAAV,IAAA,CAAAU,aAAA;sBACAnO,IAAA,EAAAyN,IAAA,CAAApI,QAAA;sBACAzC,WAAA,EAAA6K,IAAA,CAAAnI,eAAA;sBACA8I,WAAA,EAAAX,IAAA,CAAAW,WAAA;sBACAC,eAAA,EAAAZ,IAAA,CAAAtL,SAAA,eAAAsL,IAAA,CAAAa,YAAA,MAAArG,MAAA,CAAAwF,IAAA,CAAAa,YAAA,OAAArG,MAAA,CAAAwF,IAAA,CAAAW,WAAA;sBACAjM,SAAA,EAAAsL,IAAA,CAAAtL,SAAA;sBACA6B,MAAA,EAAAyJ,IAAA,CAAAzJ,MAAA;sBACAuK,OAAA,EAAAd,IAAA,CAAAc;oBACA;kBAAA;gBACA,WAAA3J,KAAA,CAAAC,OAAA,CAAAL,QAAA,CAAAvE,IAAA;kBACA6N,MAAA,CAAAvL,aAAA,GAAAiC,QAAA,CAAAvE,IAAA,CAAAgF,GAAA,WAAAwI,IAAA;oBAAA;sBACAzI,EAAA,EAAAyI,IAAA,CAAAzI,EAAA;sBACAmJ,aAAA,EAAAV,IAAA,CAAAU,aAAA;sBACAnO,IAAA,EAAAyN,IAAA,CAAApI,QAAA;sBACAzC,WAAA,EAAA6K,IAAA,CAAAnI,eAAA;sBACA8I,WAAA,EAAAX,IAAA,CAAAW,WAAA;sBACAC,eAAA,EAAAZ,IAAA,CAAAtL,SAAA,eAAAsL,IAAA,CAAAa,YAAA,MAAArG,MAAA,CAAAwF,IAAA,CAAAa,YAAA,OAAArG,MAAA,CAAAwF,IAAA,CAAAW,WAAA;sBACAjM,SAAA,EAAAsL,IAAA,CAAAtL,SAAA;sBACA6B,MAAA,EAAAyJ,IAAA,CAAAzJ,MAAA;sBACAuK,OAAA,EAAAd,IAAA,CAAAc;oBACA;kBAAA;gBACA,WAAA/J,QAAA,CAAAG,IAAA,IAAAC,KAAA,CAAAC,OAAA,CAAAL,QAAA,CAAAG,IAAA;kBACAmJ,MAAA,CAAAvL,aAAA,GAAAiC,QAAA,CAAAG,IAAA,CAAAM,GAAA,WAAAwI,IAAA;oBAAA;sBACAzI,EAAA,EAAAyI,IAAA,CAAAzI,EAAA;sBACAmJ,aAAA,EAAAV,IAAA,CAAAU,aAAA;sBACAnO,IAAA,EAAAyN,IAAA,CAAApI,QAAA;sBACAzC,WAAA,EAAA6K,IAAA,CAAAnI,eAAA;sBACA8I,WAAA,EAAAX,IAAA,CAAAW,WAAA;sBACAC,eAAA,EAAAZ,IAAA,CAAAtL,SAAA,eAAAsL,IAAA,CAAAa,YAAA,MAAArG,MAAA,CAAAwF,IAAA,CAAAa,YAAA,OAAArG,MAAA,CAAAwF,IAAA,CAAAW,WAAA;sBACAjM,SAAA,EAAAsL,IAAA,CAAAtL,SAAA;sBACA6B,MAAA,EAAAyJ,IAAA,CAAAzJ,MAAA;sBACAuK,OAAA,EAAAd,IAAA,CAAAc;oBACA;kBAAA;gBACA,WAAA/J,QAAA,CAAAE,OAAA,IAAAE,KAAA,CAAAC,OAAA,CAAAL,QAAA,CAAAE,OAAA;kBACAoJ,MAAA,CAAAvL,aAAA,GAAAiC,QAAA,CAAAE,OAAA,CAAAO,GAAA,WAAAwI,IAAA;oBAAA;sBACAzI,EAAA,EAAAyI,IAAA,CAAAzI,EAAA;sBACAmJ,aAAA,EAAAV,IAAA,CAAAU,aAAA;sBACAnO,IAAA,EAAAyN,IAAA,CAAApI,QAAA;sBACAzC,WAAA,EAAA6K,IAAA,CAAAnI,eAAA;sBACA8I,WAAA,EAAAX,IAAA,CAAAW,WAAA;sBACAC,eAAA,EAAAZ,IAAA,CAAAtL,SAAA,eAAAsL,IAAA,CAAAa,YAAA,MAAArG,MAAA,CAAAwF,IAAA,CAAAa,YAAA,OAAArG,MAAA,CAAAwF,IAAA,CAAAW,WAAA;sBACAjM,SAAA,EAAAsL,IAAA,CAAAtL,SAAA;sBACA6B,MAAA,EAAAyJ,IAAA,CAAAzJ,MAAA;sBACAuK,OAAA,EAAAd,IAAA,CAAAc;oBACA;kBAAA;gBACA;kBACAT,MAAA,CAAAvL,aAAA;gBACA;cACA;gBACAuL,MAAA,CAAAF,QAAA,CAAA9H,KAAA,CAAAtB,QAAA,CAAAuD,GAAA;gBACA+F,MAAA,CAAAvL,aAAA;cACA;cAAA0L,SAAA,CAAAX,CAAA;cAAA;YAAA;cAAAW,SAAA,CAAAV,CAAA;cAAAS,GAAA,GAAAC,SAAA,CAAAT,CAAA;cAEAM,MAAA,CAAAF,QAAA,CAAA9H,KAAA;cACAgI,MAAA,CAAAvL,aAAA;YAAA;cAAA,OAAA0L,SAAA,CAAAJ,CAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IAEA;IAEA;IACAS,qBAAA,WAAAA,sBAAA;MACA,KAAAhM,uBAAA;MACA,KAAAkK,aAAA;MACA,KAAAjK,gBAAA;IACA;IAEA;IACAiK,aAAA,WAAAA,cAAA;MACA,KAAAhK,QAAA;QACAC,cAAA;QACA3C,IAAA;QACA4C,WAAA;QACAC,YAAA;QACAC,iBAAA;QACAX,SAAA;QACAV,QAAA;MACA;IACA;IAEA;IACAgN,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MAAA,WAAA5B,kBAAA,CAAA/M,OAAA,mBAAAgN,aAAA,CAAAhN,OAAA,IAAAiN,CAAA,UAAA2B,SAAA;QAAA,IAAAzL,QAAA,EAAAsB,QAAA,EAAAiJ,IAAA,EAAAmB,SAAA,EAAAC,GAAA;QAAA,WAAA9B,aAAA,CAAAhN,OAAA,IAAAqN,CAAA,WAAA0B,SAAA;UAAA,kBAAAA,SAAA,CAAAxB,CAAA;YAAA;cAAA,IACAoB,MAAA,CAAAK,gBAAA;gBAAAD,SAAA,CAAAxB,CAAA;gBAAA;cAAA;cAAA,OAAAwB,SAAA,CAAAjB,CAAA;YAAA;cAAAiB,SAAA,CAAAvB,CAAA;cAKArK,QAAA,GAAAwL,MAAA,CAAAM,aAAA;cAAA,MAEAN,MAAA,CAAAjM,gBAAA;gBAAAqM,SAAA,CAAAxB,CAAA;gBAAA;cAAA;cAAAwB,SAAA,CAAAxB,CAAA;cAAA,OAEA,IAAAhE,gCAAA,EAAApG,QAAA;YAAA;cAAAsB,QAAA,GAAAsK,SAAA,CAAAtB,CAAA;cAAA,KACAhJ,QAAA,CAAAyE,OAAA;gBAAA6F,SAAA,CAAAxB,CAAA;gBAAA;cAAA;cACAoB,MAAA,CAAAd,QAAA,CAAA3E,OAAA;cACA;cAAA6F,SAAA,CAAAxB,CAAA;cAAA,OACAoB,MAAA,CAAAnC,iBAAA;YAAA;cAAA,MAEA/H,QAAA,CAAAvE,IAAA,IAAAuE,QAAA,CAAAvE,IAAA,CAAA+E,EAAA;gBAAA8J,SAAA,CAAAxB,CAAA;gBAAA;cAAA;cAAAwB,SAAA,CAAAxB,CAAA;cAAA,OACA,IAAA2B,iCAAA,EAAAzK,QAAA,CAAAvE,IAAA,CAAA+E,EAAA;YAAA;cAAA8J,SAAA,CAAAxB,CAAA;cAAA,OACAoB,MAAA,CAAAnC,iBAAA;YAAA;cAAAuC,SAAA,CAAAxB,CAAA;cAAA;YAAA;cAGAoB,MAAA,CAAAd,QAAA,CAAA9H,KAAA,aAAAtB,QAAA,CAAAuD,GAAA;cAAA,OAAA+G,SAAA,CAAAjB,CAAA;YAAA;cAAAiB,SAAA,CAAAxB,CAAA;cAAA;YAAA;cAIA;cACAG,IAAA,GAAAiB,MAAA,CAAAnM,aAAA,CAAAmM,MAAA,CAAAjM,gBAAA;cAAAqM,SAAA,CAAAxB,CAAA;cAAA,OACA,IAAA4B,gCAAA,EAAAzB,IAAA,CAAAzI,EAAA,EAAA9B,QAAA;YAAA;cAAAsB,SAAA,GAAAsK,SAAA,CAAAtB,CAAA;cAAA,KAEAhJ,SAAA,CAAAyE,OAAA;gBAAA6F,SAAA,CAAAxB,CAAA;gBAAA;cAAA;cAAAwB,SAAA,CAAAxB,CAAA;cAAA,OAEA,IAAA2B,iCAAA,EAAAxB,IAAA,CAAAzI,EAAA;YAAA;cACA0J,MAAA,CAAAd,QAAA,CAAA3E,OAAA;cACA;cAAA6F,SAAA,CAAAxB,CAAA;cAAA,OACAoB,MAAA,CAAAnC,iBAAA;YAAA;cAAAuC,SAAA,CAAAxB,CAAA;cAAA;YAAA;cAEAoB,MAAA,CAAAd,QAAA,CAAA9H,KAAA,aAAAtB,SAAA,CAAAuD,GAAA;cAAA,OAAA+G,SAAA,CAAAjB,CAAA;YAAA;cAKAa,MAAA,CAAAlM,uBAAA;cACAkM,MAAA,CAAAhC,aAAA;cACAgC,MAAA,CAAAjM,gBAAA;cAAAqM,SAAA,CAAAxB,CAAA;cAAA;YAAA;cAAAwB,SAAA,CAAAvB,CAAA;cAAAsB,GAAA,GAAAC,SAAA,CAAAtB,CAAA;cAEAkB,MAAA,CAAAd,QAAA,CAAA9H,KAAA;YAAA;cAAA,OAAAgJ,SAAA,CAAAjB,CAAA;UAAA;QAAA,GAAAc,QAAA;MAAA;IAEA;IAEA;IACAQ,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MAAA,WAAAtC,kBAAA,CAAA/M,OAAA,mBAAAgN,aAAA,CAAAhN,OAAA,IAAAiN,CAAA,UAAAqC,SAAA;QAAA,IAAAnM,QAAA,EAAAsB,QAAA,EAAAiJ,IAAA,EAAA6B,UAAA,EAAAC,GAAA;QAAA,WAAAxC,aAAA,CAAAhN,OAAA,IAAAqN,CAAA,WAAAoC,SAAA;UAAA,kBAAAA,SAAA,CAAAlC,CAAA;YAAA;cAAA,IACA8B,MAAA,CAAAL,gBAAA;gBAAAS,SAAA,CAAAlC,CAAA;gBAAA;cAAA;cAAA,OAAAkC,SAAA,CAAA3B,CAAA;YAAA;cAAA2B,SAAA,CAAAjC,CAAA;cAKArK,QAAA,GAAAkM,MAAA,CAAAJ,aAAA;cAAA,MAEAI,MAAA,CAAA3M,gBAAA;gBAAA+M,SAAA,CAAAlC,CAAA;gBAAA;cAAA;cAAAkC,SAAA,CAAAlC,CAAA;cAAA,OAEA,IAAAhE,gCAAA,EAAApG,QAAA;YAAA;cAAAsB,QAAA,GAAAgL,SAAA,CAAAhC,CAAA;cAAA,KACAhJ,QAAA,CAAAyE,OAAA;gBAAAuG,SAAA,CAAAlC,CAAA;gBAAA;cAAA;cACA8B,MAAA,CAAAxB,QAAA,CAAA3E,OAAA;cACA;cAAAuG,SAAA,CAAAlC,CAAA;cAAA,OACA8B,MAAA,CAAA7C,iBAAA;YAAA;cAAAiD,SAAA,CAAAlC,CAAA;cAAA;YAAA;cAEA8B,MAAA,CAAAxB,QAAA,CAAA9H,KAAA,eAAAtB,QAAA,CAAAuD,GAAA;cAAA,OAAAyH,SAAA,CAAA3B,CAAA;YAAA;cAAA2B,SAAA,CAAAlC,CAAA;cAAA;YAAA;cAIA;cACAG,IAAA,GAAA2B,MAAA,CAAA7M,aAAA,CAAA6M,MAAA,CAAA3M,gBAAA;cAAA+M,SAAA,CAAAlC,CAAA;cAAA,OACA,IAAA4B,gCAAA,EAAAzB,IAAA,CAAAzI,EAAA,EAAA9B,QAAA;YAAA;cAAAsB,UAAA,GAAAgL,SAAA,CAAAhC,CAAA;cAAA,KAEAhJ,UAAA,CAAAyE,OAAA;gBAAAuG,SAAA,CAAAlC,CAAA;gBAAA;cAAA;cACA8B,MAAA,CAAAxB,QAAA,CAAA3E,OAAA;cACA;cAAAuG,SAAA,CAAAlC,CAAA;cAAA,OACA8B,MAAA,CAAA7C,iBAAA;YAAA;cAAAiD,SAAA,CAAAlC,CAAA;cAAA;YAAA;cAEA8B,MAAA,CAAAxB,QAAA,CAAA9H,KAAA,eAAAtB,UAAA,CAAAuD,GAAA;cAAA,OAAAyH,SAAA,CAAA3B,CAAA;YAAA;cAKAuB,MAAA,CAAA5M,uBAAA;cACA4M,MAAA,CAAA1C,aAAA;cACA0C,MAAA,CAAA3M,gBAAA;cAAA+M,SAAA,CAAAlC,CAAA;cAAA;YAAA;cAAAkC,SAAA,CAAAjC,CAAA;cAAAgC,GAAA,GAAAC,SAAA,CAAAhC,CAAA;cAEA4B,MAAA,CAAAxB,QAAA,CAAA9H,KAAA;YAAA;cAAA,OAAA0J,SAAA,CAAA3B,CAAA;UAAA;QAAA,GAAAwB,QAAA;MAAA;IAEA;IAEA;IACAN,gBAAA,WAAAA,iBAAA;MACA,UAAArM,QAAA,CAAAC,cAAA;QACA,KAAAiL,QAAA,CAAA6B,OAAA;QACA;MACA;MAEA,UAAA/M,QAAA,CAAA1C,IAAA,UAAA0C,QAAA,CAAA1C,IAAA,CAAA2D,IAAA;QACA,KAAAiK,QAAA,CAAA6B,OAAA;QACA;MACA;MAEA,UAAA/M,QAAA,CAAAE,WAAA,UAAAF,QAAA,CAAAE,WAAA,CAAAe,IAAA;QACA,KAAAiK,QAAA,CAAA6B,OAAA;QACA;MACA;;MAEA;MACA,SAAA/M,QAAA,CAAAP,SAAA;QACA,UAAAO,QAAA,CAAAI,iBAAA;UACA,KAAA8K,QAAA,CAAA6B,OAAA;UACA;QACA;QACA;QACA,IAAArB,WAAA,OAAA5C,IAAA,MAAA9I,QAAA,CAAAI,iBAAA;QACA,IAAA4M,GAAA,OAAAlE,IAAA;QACA,IAAA4C,WAAA,IAAAsB,GAAA;UACA,KAAA9B,QAAA,CAAA6B,OAAA;UACA;QACA;MACA;QACA,UAAA/M,QAAA,CAAAG,YAAA;UACA,KAAA+K,QAAA,CAAA6B,OAAA;UACA;QACA;MACA;MAEA;IACA;IAEA;IACAT,aAAA,WAAAA,cAAA;MACA;MACA,IAAAZ,WAAA;MACA,SAAA1L,QAAA,CAAAP,SAAA;QACA;QACA,SAAAO,QAAA,CAAAI,iBAAA;UACA,IAAA6M,QAAA,OAAAnE,IAAA,MAAA9I,QAAA,CAAAI,iBAAA;UACAsL,WAAA,MAAAnG,MAAA,CAAA0H,QAAA,CAAAC,QAAA,GAAAC,QAAA,GAAAC,QAAA,eAAA7H,MAAA,CAAA0H,QAAA,CAAAI,UAAA,GAAAF,QAAA,GAAAC,QAAA;QACA;UACA1B,WAAA;QACA;MACA;QACA;QACAA,WAAA,QAAA1L,QAAA,CAAAG,YAAA;MACA;MAEA,IAAAK,QAAA;QACAP,cAAA,OAAAD,QAAA,CAAAC,cAAA;QACAiB,SAAA,OAAAlB,QAAA,CAAA1C,IAAA;QACAgQ,SAAA;QACA7N,SAAA,OAAAO,QAAA,CAAAP,SAAA;QAAA;QACAU,YAAA,EAAAuL,WAAA;QAAA;QACAvK,gBAAA,OAAAnB,QAAA,CAAAE,WAAA;QACAnB,QAAA,OAAAiB,QAAA,CAAAjB,QAAA;MACA;;MAEA;MACA,SAAAiB,QAAA,CAAAP,SAAA,oBAAAO,QAAA,CAAAI,iBAAA;QACAI,QAAA,wBAAAR,QAAA,CAAAI,iBAAA,CAAAmN,KAAA;MACA;MAEA,OAAA/M,QAAA;IACA;IAEA;IACAgN,UAAA,WAAAA,WAAAC,KAAA;MAAA,IAAAC,OAAA;MAAA,WAAAtD,kBAAA,CAAA/M,OAAA,mBAAAgN,aAAA,CAAAhN,OAAA,IAAAiN,CAAA,UAAAqD,SAAA;QAAA,IAAA5C,IAAA,EAAAjJ,QAAA,EAAA8L,GAAA;QAAA,WAAAvD,aAAA,CAAAhN,OAAA,IAAAqN,CAAA,WAAAmD,SAAA;UAAA,kBAAAA,SAAA,CAAAjD,CAAA;YAAA;cAAAiD,SAAA,CAAAhD,CAAA;cAEAE,IAAA,GAAA2C,OAAA,CAAA7N,aAAA,CAAA4N,KAAA;cAAA,IAEA1C,IAAA;gBAAA8C,SAAA,CAAAjD,CAAA;gBAAA;cAAA;cACA8C,OAAA,CAAAxC,QAAA,CAAA9H,KAAA;cAAA,OAAAyK,SAAA,CAAA1C,CAAA;YAAA;cAAA0C,SAAA,CAAAjD,CAAA;cAAA,OAIA8C,OAAA,CAAAI,QAAA,oDAAAvI,MAAA,CAAAwF,IAAA,CAAAzN,IAAA;gBACAyQ,iBAAA;gBACAC,gBAAA;gBACA3F,IAAA;cACA;YAAA;cAAAwF,SAAA,CAAAjD,CAAA;cAAA,OAEA,IAAAqD,gCAAA,EAAAlD,IAAA,CAAAzI,EAAA;YAAA;cAAAR,QAAA,GAAA+L,SAAA,CAAA/C,CAAA;cAAA,KAEAhJ,QAAA,CAAAyE,OAAA;gBAAAsH,SAAA,CAAAjD,CAAA;gBAAA;cAAA;cACA8C,OAAA,CAAAxC,QAAA,CAAA3E,OAAA;cACA;cAAAsH,SAAA,CAAAjD,CAAA;cAAA,OACA8C,OAAA,CAAA7D,iBAAA;YAAA;cAAAgE,SAAA,CAAAjD,CAAA;cAAA;YAAA;cAEA8C,OAAA,CAAAxC,QAAA,CAAA9H,KAAA,YAAAtB,QAAA,CAAAuD,GAAA,IAAAvD,QAAA,CAAA3C,OAAA;YAAA;cAAA0O,SAAA,CAAAjD,CAAA;cAAA;YAAA;cAAAiD,SAAA,CAAAhD,CAAA;cAAA+C,GAAA,GAAAC,SAAA,CAAA/C,CAAA;cAAA,MAGA8C,GAAA;gBAAAC,SAAA,CAAAjD,CAAA;gBAAA;cAAA;cAAA,OAAAiD,SAAA,CAAA1C,CAAA;YAAA;cAGAuC,OAAA,CAAAxC,QAAA,CAAA9H,KAAA;YAAA;cAAA,OAAAyK,SAAA,CAAA1C,CAAA;UAAA;QAAA,GAAAwC,QAAA;MAAA;IAEA;IAEA;IACAO,QAAA,WAAAA,SAAAT,KAAA;MACA,IAAA1C,IAAA,QAAAlL,aAAA,CAAA4N,KAAA;MAEA,KAAA1C,IAAA;QACA,KAAAG,QAAA,CAAA9H,KAAA;QACA;MACA;MAEA,KAAArD,gBAAA,GAAA0N,KAAA;MACA,KAAAzN,QAAA;QACAC,cAAA,EAAA8K,IAAA,CAAAU,aAAA;QACAnO,IAAA,EAAAyN,IAAA,CAAAzN,IAAA;QACA4C,WAAA,EAAA6K,IAAA,CAAA7K,WAAA;QACAC,YAAA,EAAA4K,IAAA,CAAAW,WAAA;QACAtL,iBAAA,EAAA2K,IAAA,CAAAY,eAAA;QACAlM,SAAA,EAAAsL,IAAA,CAAAtL,SAAA;QACAV,QAAA,EAAAgM,IAAA,CAAAc,OAAA;MACA;MAEA,KAAA5B,mBAAA;MACA,KAAAnK,uBAAA;IACA;IAEA;IACAqO,gBAAA,WAAAA,iBAAAV,KAAA;MAAA,IAAAW,OAAA;MAAA,WAAAhE,kBAAA,CAAA/M,OAAA,mBAAAgN,aAAA,CAAAhN,OAAA,IAAAiN,CAAA,UAAA+D,SAAA;QAAA,IAAAtD,IAAA,EAAAuD,SAAA,EAAAxM,QAAA,EAAAyM,GAAA;QAAA,WAAAlE,aAAA,CAAAhN,OAAA,IAAAqN,CAAA,WAAA8D,SAAA;UAAA,kBAAAA,SAAA,CAAA5D,CAAA;YAAA;cAAA4D,SAAA,CAAA3D,CAAA;cAEAE,IAAA,GAAAqD,OAAA,CAAAvO,aAAA,CAAA4N,KAAA;cACAa,SAAA,GAAAvD,IAAA,CAAAzJ,MAAA;cAAAkN,SAAA,CAAA5D,CAAA;cAAA,OAEA,IAAA2B,iCAAA,EAAAxB,IAAA,CAAAzI,EAAA,EAAAgM,SAAA;YAAA;cAAAxM,QAAA,GAAA0M,SAAA,CAAA1D,CAAA;cAAA,KAEAhJ,QAAA,CAAAyE,OAAA;gBAAAiI,SAAA,CAAA5D,CAAA;gBAAA;cAAA;cACA,IAAA0D,SAAA;gBACAF,OAAA,CAAAlD,QAAA,CAAA3E,OAAA,sBAAAhB,MAAA,CAAAwF,IAAA,CAAAzN,IAAA;cACA;gBACA8Q,OAAA,CAAAlD,QAAA,CAAAuD,IAAA,sBAAAlJ,MAAA,CAAAwF,IAAA,CAAAzN,IAAA;cACA;cACA;cAAAkR,SAAA,CAAA5D,CAAA;cAAA,OACAwD,OAAA,CAAAvE,iBAAA;YAAA;cAAA2E,SAAA,CAAA5D,CAAA;cAAA;YAAA;cAEAwD,OAAA,CAAAlD,QAAA,CAAA9H,KAAA,aAAAtB,QAAA,CAAAuD,GAAA;YAAA;cAAAmJ,SAAA,CAAA5D,CAAA;cAAA;YAAA;cAAA4D,SAAA,CAAA3D,CAAA;cAAA0D,GAAA,GAAAC,SAAA,CAAA1D,CAAA;cAGAsD,OAAA,CAAAlD,QAAA,CAAA9H,KAAA;YAAA;cAAA,OAAAoL,SAAA,CAAArD,CAAA;UAAA;QAAA,GAAAkD,QAAA;MAAA;IAEA;IAEA;IACAK,WAAA,WAAAA,YAAAjB,KAAA;MACA,IAAA1C,IAAA,QAAAlL,aAAA,CAAA4N,KAAA;MACA,KAAA1C,IAAA;QACA,KAAAG,QAAA,CAAA9H,KAAA;QACA;MACA;MACA,KAAA9C,iBAAA,CAAAE,QAAA,GAAAuK,IAAA;MACA,KAAAzK,iBAAA,CAAAC,OAAA;IACA;IAEA;IACAoO,mBAAA,WAAAA,oBAAA5D,IAAA;MACA,KAAAA,IAAA;MAEA,IAAAA,IAAA,CAAAtL,SAAA;QACA;QACA,6BAAA8F,MAAA,CAAAwF,IAAA,CAAAY,eAAA,IAAAZ,IAAA,CAAAW,WAAA;MACA;QACA;QACA,IAAAkD,aAAA,QAAAC,gBAAA,CAAA9D,IAAA,CAAAtL,SAAA;QACA,UAAA8F,MAAA,CAAAqJ,aAAA,OAAArJ,MAAA,CAAAwF,IAAA,CAAAW,WAAA;MACA;IACA;IAEA;IACAmD,gBAAA,WAAAA,iBAAApP,SAAA;MACA,IAAAqP,YAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,YAAA,CAAArP,SAAA,KAAAA,SAAA;IACA;IAEA;IACAsP,UAAA,WAAAA,WAAA;MACA;MACA,KAAA7D,QAAA,CAAAuD,IAAA;IACA;IAEA;IACAO,SAAA,EAAAA;EACA;AACA", "ignoreList": []}]}