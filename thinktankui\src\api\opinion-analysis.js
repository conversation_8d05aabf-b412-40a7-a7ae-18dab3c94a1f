import request from '@/utils/request'

// 获取舆情分析数据
export function getOpinionAnalysisData(query) {
  return request({
    url: '/public/opinion-analysis/data',
    method: 'get',
    params: query
  })
}

// 获取情感分析结果
export function getEmotionAnalysisResult(query) {
  return request({
    url: '/public/opinion-analysis/emotion',
    method: 'get',
    params: query
  })
}

// 获取趋势分析数据
export function getTrendAnalysisData(query) {
  return request({
    url: '/public/opinion-analysis/trend',
    method: 'get',
    params: query
  })
}

// 获取地域分析数据
export function getRegionalAnalysisData(query) {
  return request({
    url: '/public/opinion-analysis/regional',
    method: 'get',
    params: query
  })
}

// 创建新的分析任务
export function createAnalysisTask(data) {
  return request({
    url: '/system/opinion-analysis/task',
    method: 'post',
    data: data
  })
}

// 获取分析任务列表
export function getAnalysisTaskList(query) {
  return request({
    url: '/system/opinion-analysis/tasks',
    method: 'get',
    params: query
  })
}

// 删除分析任务（修复：调用正确的任务删除接口）
export function deleteAnalysisTask(taskId) {
  console.log('API调用 - 单个删除任务，ID:', taskId)

  return request({
    url: `/system/opinion-analysis/tasks/${taskId}`,
    method: 'delete'
  })
}

// 批量删除分析任务
export function batchDeleteAnalysisTasks(taskIds) {
  console.log('API调用 - 批量删除任务，IDs:', taskIds)
  const requestData = { ids: taskIds }
  console.log('API调用 - 请求数据:', requestData)

  return request({
    url: '/system/opinion-analysis/tasks',
    method: 'delete',
    data: requestData
  })
}

// 获取关键词分析结果
export function getKeywordAnalysis(query) {
  return request({
    url: '/public/opinion-analysis/keywords',
    method: 'get',
    params: query
  })
}

// 获取影响力评估数据
export function getInfluenceAssessment(query) {
  return request({
    url: '/public/opinion-analysis/influence',
    method: 'get',
    params: query
  })
}

// 获取传播路径分析
export function getSpreadPathAnalysis(query) {
  return request({
    url: '/public/opinion-analysis/spread-path',
    method: 'get',
    params: query
  })
}

// 获取需求列表
export function getRequirementList(query) {
  return request({
    url: '/public/opinion-analysis/requirements',
    method: 'get',
    params: query
  })
}

// 创建需求
export function createRequirement(data) {
  return request({
    url: '/public/opinion-analysis/requirements',
    method: 'post',
    data: data
  })
}

// 更新需求
export function updateRequirement(id, data) {
  return request({
    url: `/public/opinion-analysis/requirements/${id}`,
    method: 'put',
    data: data
  })
}

// 删除需求（单个）
export function deleteRequirement(id) {
  console.log('API调用 - 单个删除，ID:', id)
  const requestData = { ids: [id] }
  console.log('API调用 - 请求数据:', requestData)

  return request({
    url: '/public/opinion-analysis/requirements',
    method: 'delete',
    data: requestData
  })
}

// 批量删除需求
export function batchDeleteRequirements(ids) {
  console.log('API调用 - 批量删除，IDs:', ids)
  const requestData = { ids: ids }
  console.log('API调用 - 请求数据:', requestData)

  return request({
    url: '/public/opinion-analysis/requirements',
    method: 'delete',
    data: requestData
  })
}

// 导出需求列表
export function exportRequirements(query) {
  return request({
    url: '/public/opinion-analysis/requirements/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获取关键词分类
export function getKeywordCategories() {
  return request({
    url: '/public/opinion-analysis/keywords/categories',
    method: 'get'
  })
}

// 获取数据源列表
export function getDataSourceList(query) {
  return request({
    url: '/public/opinion-analysis/data-sources',
    method: 'get',
    params: query
  })
}

// 获取任务统计
export function getTaskStatistics() {
  return request({
    url: '/public/opinion-analysis/tasks/statistics',
    method: 'get'
  })
}

// 获取数据源统计
export function getDataSourceStatistics() {
  return request({
    url: '/public/opinion-analysis/data-sources/statistics',
    method: 'get'
  })
}

// 新增数据源
export function createDataSource(data) {
  return request({
    url: '/system/opinion-analysis/data-sources',
    method: 'post',
    data: data
  })
}

// 删除数据源
export function deleteDataSource(dataSourceId) {
  return request({
    url: `/public/opinion-analysis/data-sources/${dataSourceId}`,
    method: 'delete'
  })
}

// 执行联网搜索
export function performOnlineSearch(data) {
  return request({
    url: '/public/opinion-analysis/online-search',
    method: 'post',
    data: data,
    timeout: 600000 // 3分钟超时，因为AI API响应较慢
  })
}

// 开始分析
export function startAnalysis(data) {
  return request({
    url: '/public/opinion-analysis/start-analysis',
    method: 'post',
    data: data,
    timeout: 600000 // 10分钟超时，因为AI分析可能需要较长时间
  })
}

// 推送报告
export function pushReport(data) {
  return request({
    url: '/public/opinion-analysis/push-report',
    method: 'post',
    data: data,
    timeout: 600000 // 1分钟超时，推送可能需要较长时间
  })
}

// 获取公开报告数据（用于推送链接访问）
export function getPublicReportData(requirementId) {
  return request({
    url: `/public/opinion-analysis/report/${requirementId}`,
    method: 'get'
  })
}

// ==================== 定时任务管理API ====================

// 获取定时任务列表
export function getTimedTaskList(query) {
  return request({
    url: '/system/opinion-analysis/tasks',
    method: 'get',
    params: query
  })
}

// 获取定时任务详情
export function getTimedTaskDetail(taskId) {
  return request({
    url: `/system/opinion-analysis/tasks/${taskId}`,
    method: 'get'
  })
}

// 创建定时任务
export function createTimedTask(data) {
  return request({
    url: '/system/opinion-analysis/tasks',
    method: 'post',
    data: data
  })
}

// 检查任务是否已存在
export function checkTaskExists(params) {
  return request({
    url: '/system/opinion-analysis/tasks/check-exists',
    method: 'get',
    params: params
  })
}

// 更新定时任务
export function updateTimedTask(taskId, data) {
  return request({
    url: `/system/opinion-analysis/tasks/${taskId}`,
    method: 'put',
    data: data
  })
}

// 更新任务状态
export function updateTaskStatus(taskId, status) {
  return request({
    url: `/system/opinion-analysis/tasks/${taskId}/status`,
    method: 'put',
    data: {
      task_id: taskId,
      task_status: status
    }
  })
}

// 删除定时任务
export function deleteTimedTask(taskId) {
  return request({
    url: `/system/opinion-analysis/tasks/${taskId}`,
    method: 'delete'
  })
}

// 根据需求ID获取任务列表
export function getTasksByRequirement(requirementId) {
  return request({
    url: `/system/opinion-analysis/requirements/${requirementId}/tasks`,
    method: 'get'
  })
}

// ==================== AI关联词生成API ====================

// 生成关联词
export function generateRelatedKeywords(data) {
  return request({
    url: '/api/opinion/generate-related-keywords',
    method: 'post',
    data: data,
    timeout: 60000 // 1分钟超时，AI生成可能需要较长时间
  })
}

// ==================== 分析进度相关接口 ====================

// 创建分析进度任务
export function createAnalysisProgressTask(data) {
  return request({
    url: '/public/opinion-analysis/analysis-progress/create-task',
    method: 'post',
    data: data
  })
}

// 添加进度日志
export function addProgressLog(data) {
  return request({
    url: '/public/opinion-analysis/analysis-progress/add-log',
    method: 'post',
    data: data
  })
}

// 获取分析进度
export function getAnalysisProgress(taskId, pageNum = 1, pageSize = 50) {
  return request({
    url: `/public/opinion-analysis/analysis-progress/${taskId}`,
    method: 'get',
    params: {
      pageNum: pageNum,
      pageSize: pageSize
    }
  })
}

// 完成分析任务
export function completeAnalysisTask(taskId, resultSummary = null) {
  return request({
    url: `/public/opinion-analysis/analysis-progress/${taskId}/complete`,
    method: 'post',
    data: resultSummary
  })
}

// 取消分析任务
export function cancelAnalysisTask(taskId) {
  return request({
    url: `/public/opinion-analysis/analysis-progress/${taskId}/cancel`,
    method: 'post'
  })
}

// ==================== 分析记录管理 ====================

// 获取分析记录列表
export function getAnalysisRecordList(query) {
  return request({
    url: '/public/opinion-analysis/analysis-records',
    method: 'get',
    params: query
  })
}

// 获取分析记录详情
export function getAnalysisRecordDetail(recordId) {
  return request({
    url: `/public/opinion-analysis/analysis-records/${recordId}`,
    method: 'get'
  })
}

// 删除分析记录
export function deleteAnalysisRecord(recordId) {
  return request({
    url: `/public/opinion-analysis/analysis-records/${recordId}`,
    method: 'delete'
  })
}

// 批量删除分析记录
export function batchDeleteAnalysisRecords(recordIds) {
  return request({
    url: '/public/opinion-analysis/analysis-records/batch',
    method: 'delete',
    data: recordIds
  })
}

// 重新运行分析任务
export function rerunAnalysisTask(recordId) {
  return request({
    url: `/public/opinion-analysis/analysis-records/${recordId}/rerun`,
    method: 'post'
  })
}

// 导出分析记录
export function exportAnalysisRecords(query) {
  return request({
    url: '/public/opinion-analysis/analysis-records/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
