<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="68px">

      <el-form-item label="需求名称" prop="requirementName">
        <el-input
          id="requirementName"
          v-model="queryParams.requirementName"
          placeholder="请输入需求名称"
          clearable
          @input="onRequirementNameInput"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="关键词" prop="entityKeyword">
        <el-input
          id="entityKeyword"
          v-model="queryParams.entityKeyword"
          placeholder="请输入实体关键词"
          clearable
          @input="onEntityKeywordInput"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分析状态" prop="analysisStatus">
        <el-select
          id="analysisStatus"
          v-model="queryParams.analysisStatus"
          placeholder="请选择分析状态"
          clearable
          @change="onAnalysisStatusChange"
        >
          <el-option label="未开始" value="0" />
          <el-option label="分析中" value="1" />
          <el-option label="已完成" value="2" />
          <el-option label="失败" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          id="create_time_range"
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区域 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['opinion:record:export']"
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-time"
          size="mini"
          @click="handleTimedPush"
        >定时推送</el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="recordList"
      :default-sort="{prop: 'createTime', order: 'descending'}"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" width="80" />
      <el-table-column label="需求名称" align="center" prop="requirementName" :show-overflow-tooltip="true" />
      <el-table-column label="关键词" align="center" prop="entityKeyword" width="120" :show-overflow-tooltip="true" />
      <el-table-column label="具体需求" align="center" prop="specificRequirement" width="200" :show-overflow-tooltip="true" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="生成状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <el-tag
            :type="getStatusTagType(scope.row.status)"
            size="mini"
          >
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="380">
        <template slot-scope="scope">

          <el-button
            v-if="scope.row.status === 2"
            size="mini"
            type="text"
            icon="el-icon-notebook-1"
            @click="handleReport(scope.row)"
          >报告</el-button>
          <el-button
            v-if="scope.row.status === 2"
            size="mini"
            type="text"
            icon="el-icon-s-promotion"
            @click="handlePushNow(scope.row)"
          >立即推送</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.page_num"
      :limit.sync="queryParams.page_size"
      @pagination="getList"
    />

    <!-- 分析日志对话框 -->
    <el-dialog title="分析日志" :visible.sync="logDialogVisible" width="1000px" append-to-body>
      <el-table
        v-loading="logLoading"
        :data="logList"
        height="400"
        :default-sort="{prop: 'createTime', order: 'descending'}"
      >
        <el-table-column label="时间" align="center" prop="createTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="级别" align="center" prop="logLevel" width="80">
          <template slot-scope="scope">
            <el-tag
              :type="getLogLevelTagType(scope.row.logLevel)"
              size="mini"
            >
              {{ scope.row.logLevel.toUpperCase() }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="步骤" align="center" prop="stepName" width="120" />
        <el-table-column label="进度" align="center" prop="progressPercentage" width="80">
          <template slot-scope="scope">
            <span>{{ scope.row.progressPercentage }}%</span>
          </template>
        </el-table-column>
        <el-table-column label="日志内容" align="center" prop="logMessage" :show-overflow-tooltip="true" />
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="logDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 查看报告对话框 -->
    <el-dialog title="分析报告" :visible.sync="reportDialogVisible" width="1200px" append-to-body>
      <div v-loading="reportLoading" class="report-content">
        <div v-if="reportData">
          <el-descriptions title="基本信息" :column="2" border>
            <el-descriptions-item label="需求名称">{{ reportData.requirementName }}</el-descriptions-item>
            <el-descriptions-item label="关键词">{{ reportData.entityKeyword }}</el-descriptions-item>
            <el-descriptions-item label="具体需求" :span="2">{{ reportData.specificRequirement }}</el-descriptions-item>
          </el-descriptions>

          <el-divider content-position="left">分析结果</el-divider>
          <div class="analysis-results">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-card shadow="never">
                  <div slot="header">
                    <span>文章总数</span>
                  </div>
                  <div class="statistic-value">{{ reportData.totalArticles || 0 }}</div>
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card shadow="never">
                  <div slot="header">
                    <span>正面情感</span>
                  </div>
                  <div class="statistic-value positive">{{ reportData.sentiment?.positive || 0 }}</div>
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card shadow="never">
                  <div slot="header">
                    <span>负面情感</span>
                  </div>
                  <div class="statistic-value negative">{{ reportData.sentiment?.negative || 0 }}</div>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleExportReport">导出报告</el-button>
        <el-button @click="reportDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 立即推送对话框 -->
    <el-dialog title="立即推送" :visible.sync="pushDialogVisible" width="600px" append-to-body>
      <el-form ref="pushForm" :model="pushForm" :rules="pushRules" label-width="100px">
        <el-form-item label="推送地址" prop="push_url">
          <el-input v-model="pushForm.push_url" placeholder="请输入推送地址" />
        </el-form-item>
        <el-form-item label="推送内容" prop="push_content">
          <el-input
            v-model="pushForm.push_content"
            type="textarea"
            :rows="4"
            placeholder="请输入推送内容"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="pushDialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="pushLoading" @click="submitPush">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 定时推送对话框 -->
    <el-dialog title="定时推送" :visible.sync="schedulePushDialogVisible" width="600px" append-to-body>
      <el-form ref="schedulePushForm" :model="schedulePushForm" :rules="schedulePushRules" label-width="100px">
        <el-form-item label="推送地址" prop="push_url">
          <el-input v-model="schedulePushForm.push_url" placeholder="请输入推送地址" />
        </el-form-item>
        <el-form-item label="推送时间" prop="push_time">
          <el-date-picker
            v-model="schedulePushForm.push_time"
            type="datetime"
            placeholder="选择推送时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="推送频率" prop="frequency">
          <el-select v-model="schedulePushForm.frequency" placeholder="请选择推送频率">
            <el-option label="仅一次" value="once" />
            <el-option label="每日" value="daily" />
            <el-option label="每周" value="weekly" />
            <el-option label="每月" value="monthly" />
          </el-select>
        </el-form-item>
        <el-form-item label="推送内容" prop="push_content">
          <el-input
            v-model="schedulePushForm.push_content"
            type="textarea"
            :rows="4"
            placeholder="请输入推送内容"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="schedulePushDialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="schedulePushLoading" @click="submitSchedulePush">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 定时任务抽屉 -->
    <el-drawer
      title="定时任务"
      :visible.sync="timedTaskDialogVisible"
      direction="rtl"
      size="600px"
      :before-close="closeTimedTaskDialog"
      custom-class="timed-task-drawer"
    >
      <!-- 抽屉头部右侧按钮 -->
      <div slot="title" class="drawer-header">
        <span class="drawer-title">定时任务</span>
        <el-button
          type="primary"
          size="mini"
          icon="el-icon-plus"
          class="add-task-btn"
          @click="handleAddTimedTask"
        >
          定时任务
        </el-button>
      </div>

      <!-- 抽屉内容 -->
      <div class="drawer-content">
        <!-- 空状态 -->
        <div v-if="timedTaskList.length === 0" class="empty-state">
          <div class="empty-content">
            <!-- 空状态图标 -->
            <div class="empty-icon">
              <svg width="120" height="120" viewBox="0 0 120 120" fill="none">
                <!-- 文件夹图标 -->
                <path d="M20 30h25l5-10h50v70H20V30z" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="2" />
                <path d="M25 35h70v50H25V35z" fill="#fafafa" stroke="#e0e0e0" stroke-width="1" />
                <!-- 文档图标 -->
                <rect x="35" y="45" width="30" height="25" fill="#ffffff" stroke="#d0d0d0" stroke-width="1" rx="2" />
                <rect x="70" y="50" width="20" height="15" fill="#ffffff" stroke="#d0d0d0" stroke-width="1" rx="2" />
                <!-- 装饰线条 -->
                <line x1="40" y1="52" x2="60" y2="52" stroke="#e0e0e0" stroke-width="1" />
                <line x1="40" y1="57" x2="55" y2="57" stroke="#e0e0e0" stroke-width="1" />
                <line x1="40" y1="62" x2="58" y2="62" stroke="#e0e0e0" stroke-width="1" />
                <line x1="75" y1="55" x2="85" y2="55" stroke="#e0e0e0" stroke-width="1" />
                <line x1="75" y1="60" x2="82" y2="60" stroke="#e0e0e0" stroke-width="1" />
              </svg>
            </div>
            <p class="empty-text">暂无定时任务</p>
            <el-button type="primary" class="create-btn" @click="handleCreateTimedTask">
              去创建
            </el-button>
          </div>
        </div>

        <!-- 任务列表 -->
        <div v-else class="task-list">
          <div v-if="timedTaskList.length === 0" class="empty-task-list">
            <div class="empty-icon">📅</div>
            <div class="empty-text">暂无定时任务</div>
            <el-button type="primary" size="small" class="add-task-btn" @click="handleAddTimedTask">
              添加任务
            </el-button>
          </div>
          <div v-else class="task-items">
            <div v-for="(task, index) in timedTaskList" :key="index" class="task-item">
              <div class="task-info">
                <div class="task-header">
                  <div class="task-name">{{ task.name }}</div>
                  <div class="task-status" :class="{ 'status-running': task.status === 'running', 'status-pending': task.status === 'pending' }">
                    {{ task.status === 'running' ? '运行中' : '待运行' }}
                  </div>
                </div>
                <!-- 任务描述已隐藏 -->
                <div class="task-schedule">
                  <i class="el-icon-time" />
                  <span>{{ getTaskScheduleText(task) }}</span>
                </div>
              </div>
              <div class="task-actions">
                <el-button type="text" size="mini" title="预览任务详情" @click.stop="previewTask(index)">
                  <i class="el-icon-view" />
                </el-button>
                <el-button type="text" size="mini" :title="task.status === 'running' ? '暂停任务' : '启动任务'" @click.stop="toggleTaskStatus(index)">
                  <i :class="task.status === 'running' ? 'el-icon-video-pause' : 'el-icon-video-play'" />
                </el-button>
                <el-button type="text" size="mini" title="编辑任务" style="margin-right: 5px;" @click.stop="editTask(index)">
                  <i class="el-icon-edit" />
                </el-button>
                <el-button type="text" size="mini" title="删除任务" style="color: #f56c6c;" @click.stop="deleteTask(index)">
                  <i class="el-icon-delete" />
                </el-button>
                <!-- 测试按钮 -->
                <el-button type="text" size="mini" title="测试点击" style="color: #409eff;" @click.stop="testClick(index)">
                  <i class="el-icon-info" />
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 任务预览对话框 -->
      <el-dialog
        title="任务详情"
        :visible.sync="taskPreviewDialog.visible"
        width="600px"
        append-to-body
      >
        <div v-if="taskPreviewDialog.taskData" class="task-preview-content">
          <el-descriptions title="基本信息" :column="2" border>
            <el-descriptions-item label="任务名称">{{ taskPreviewDialog.taskData.name }}</el-descriptions-item>
            <el-descriptions-item label="任务状态">
              <el-tag :type="taskPreviewDialog.taskData.status === 'running' ? 'success' : 'info'">
                {{ taskPreviewDialog.taskData.status === 'running' ? '运行中' : '待运行' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="执行频率">{{ getFrequencyText(taskPreviewDialog.taskData.frequency) }}</el-descriptions-item>
            <el-descriptions-item label="执行时间">{{ taskPreviewDialog.taskData.executeTime }}</el-descriptions-item>
            <el-descriptions-item label="推送地址" :span="2">{{ taskPreviewDialog.taskData.pushUrl || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="任务描述" :span="2">{{ taskPreviewDialog.taskData.description || '无描述' }}</el-descriptions-item>
          </el-descriptions>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="taskPreviewDialog.visible = false">关 闭</el-button>
        </div>
      </el-dialog>

      <!-- 创建/编辑任务弹窗 -->
      <el-dialog
        :title="editingTaskIndex === -1 ? '创建定时任务' : '编辑定时任务'"
        :visible.sync="createTaskDialogVisible"
        width="500px"
        :before-close="closeCreateTaskDialog"
        :append-to-body="true"
        class="create-task-dialog"
      >
        <div class="task-form">
          <!-- 任务需求 -->
          <div class="task-requirement-section">
            <div class="section-label">
              任务需求
              <span class="required">*</span>
            </div>
            <div class="form-group">
              <div class="input-label">关联任务</div>
              <el-select
                v-model="taskForm.requirement_id"
                placeholder="请选择要关联的舆情分析任务"
                class="task-name-input"
                style="width: 100%"
              >
                <el-option
                  v-for="task in requirementList"
                  :key="task.id"
                  :label="task.requirementName"
                  :value="task.id"
                >
                  <span style="float: left">{{ task.requirementName }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">ID: {{ task.id }}</span>
                </el-option>
              </el-select>
            </div>
            <div class="form-group">
              <div class="input-label">任务名称</div>
              <el-input
                v-model="taskForm.name"
                placeholder="请输入任务名称"
                class="task-name-input"
              />
            </div>
            <div class="form-group">
              <div class="input-label">任务描述</div>
              <el-input
                v-model="taskForm.description"
                type="textarea"
                :rows="3"
                placeholder="请描述具体的任务需求，例如：基于当前分析需求自动生成AI新闻总结"
                class="task-description-input"
              />
              <div class="form-group">
                <div class="input-label">推送地址</div>
                <el-input
                  v-model="taskForm.push_url"
                  placeholder="例如：https://www.baidu.com"
                  class="task-name-input"
                />
              </div>
            </div>
          </div>

          <!-- 执行时间 -->
          <div class="execute-time-section">
            <div class="section-label">执行时间</div>
            <div class="time-selector">
              <el-select v-model="taskForm.frequency" placeholder="选择频率" class="frequency-select">
                <el-option label="仅一次" value="once" />
                <el-option label="每天" value="daily" />
                <el-option label="每周" value="weekly" />
                <el-option label="每月" value="monthly" />
              </el-select>
              <!-- 一次性任务：选择具体日期时间 -->
              <el-date-picker
                v-if="taskForm.frequency === 'once'"
                v-model="taskForm.execute_date_time"
                type="datetime"
                placeholder="选择执行日期和时间"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                class="datetime-picker"
                :picker-options="{
                  disabledDate(time) {
                    return time.getTime() < Date.now() - 8.64e7
                  }
                }"
              />
              <!-- 周期性任务：选择时间 -->
              <el-time-picker
                v-else
                v-model="taskForm.execute_time"
                format="HH:mm"
                value-format="HH:mm"
                placeholder="选择时间"
                class="time-picker"
              />
            </div>
          </div>
        </div>

        <!-- 底部按钮 -->
        <div slot="footer" class="dialog-footer">
          <el-button class="modify-btn" @click="modifyPlan">修改计划</el-button>
          <el-button type="primary" class="run-btn" @click="saveAndRunTask">保存并运行</el-button>
          <el-button type="success" class="save-btn" @click="saveTaskPlan">保存计划</el-button>
        </div>
      </el-dialog>
    </el-drawer>
  </div>
</template>

<script>
import {
  getAnalysisTaskList,
  deleteAnalysisTask,
  batchDeleteAnalysisTasks,
  exportRequirements,
  getAnalysisProgress,
  pushReport,
  createTimedTask,
  getTimedTaskList,
  getTimedTaskDetail,
  updateTimedTask,
  updateTaskStatus,
  deleteTimedTask
} from '@/api/opinion-analysis'
import { parseTime } from '@/utils/ruoyi'

export default {
  name: 'AnalyzeRecord',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 分析记录表格数据
      recordList: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        page_num: 1,
        page_size: 10,
        requirementName: null,
        entityKeyword: null,
        analysisStatus: null
      },
      // 分析日志对话框
      logDialogVisible: false,
      logLoading: false,
      logList: [],
      // 查看报告对话框
      reportDialogVisible: false,
      reportLoading: false,
      reportData: null,
      // 立即推送对话框
      pushDialogVisible: false,
      pushLoading: false,
      pushForm: {
        push_url: '',
        push_content: ''
      },
      pushRules: {
        push_url: [
          { required: true, message: '推送地址不能为空', trigger: 'blur' }
        ],
        push_content: [
          { required: true, message: '推送内容不能为空', trigger: 'blur' }
        ]
      },
      // 定时推送对话框
      schedulePushDialogVisible: false,
      schedulePushLoading: false,
      schedulePushForm: {
        push_url: '',
        push_time: '',
        frequency: 'once',
        push_content: ''
      },
      schedulePushRules: {
        push_url: [
          { required: true, message: '推送地址不能为空', trigger: 'blur' }
        ],
        push_time: [
          { required: true, message: '推送时间不能为空', trigger: 'change' }
        ],
        push_content: [
          { required: true, message: '推送内容不能为空', trigger: 'blur' }
        ]
      },
      // 当前操作的记录
      currentRecord: null,
      // 定时任务相关数据
      timedTaskDialogVisible: false, // 定时任务抽屉显示状态
      timedTaskList: [], // 定时任务列表
      createTaskDialogVisible: false, // 创建任务弹窗显示状态
      editingTaskIndex: -1, // 当前编辑的任务索引，-1表示新建任务
      taskForm: {
        requirement_id: '', // 需求ID
        name: '', // 任务名称
        description: '',
        execute_time: '16:00',
        execute_date_time: '', // 一次性任务的执行日期时间
        frequency: 'daily',
        push_url: '' // 推送地址
      },
      requirementList: [], // 需求列表
      // 任务预览弹窗状态
      taskPreviewDialog: {
        visible: false,
        taskData: null,
        loading: false
      }
    }
  },
  created() {
    // 只重置日期范围，保留其他查询参数
    this.dateRange = []
    this.getList()
    // 注释掉页面加载时的定时任务列表加载，改为点击定时推送按钮时才加载
    // this.loadTimedTaskList()
  },
  methods: {
    /** 查询分析记录列表 */
    getList(paginationParams) {
      this.loading = true

      // 处理分页参数
      if (paginationParams) {
        this.queryParams.page_num = paginationParams.page
        this.queryParams.page_size = paginationParams.limit
      }

      const params = {
        page_num: this.queryParams.page_num,
        page_size: this.queryParams.page_size
      }

      // 添加搜索条件（正确映射到任务表字段）
      if (this.queryParams.requirementName && this.queryParams.requirementName.trim() !== '') {
        params.task_name = this.queryParams.requirementName.trim() // 映射到任务名称
      }
      if (this.queryParams.entityKeyword && this.queryParams.entityKeyword.trim() !== '') {
        params.task_description = this.queryParams.entityKeyword.trim() // 映射到任务描述（包含关键词）
      }
      if (this.queryParams.analysisStatus !== null && this.queryParams.analysisStatus !== undefined && this.queryParams.analysisStatus !== '') {
        // 将前端状态映射回任务状态
        const statusMap = { 0: 'pending', 1: 'running', 2: 'completed', 3: 'failed' }
        params.status = statusMap[this.queryParams.analysisStatus] || 'pending'
      }

      // 添加时间范围
      if (this.dateRange && this.dateRange.length === 2) {
        params.create_time_start = this.dateRange[0]
        params.create_time_end = this.dateRange[1]
        console.log('🔍 添加时间范围筛选:', this.dateRange)
      }

      // 调用真实API获取任务列表数据
      console.log('🔍 发送API请求，参数:', params)
      getAnalysisTaskList(params).then(response => {
        console.log('🔍 API响应:', response)
        // 处理响应数据，将数据库字段映射到前端显示字段

        // 适配新的响应格式：使用 model_content 后，PageResponseModel 会直接合并到响应根级别
        let requirements = []
        if (response.records) {
          // 新格式：model_content 使 PageResponseModel 直接合并到响应根级别
          requirements = response.records
        } else if (response.data && response.data.records) {
          // 备用格式：PageResponseModel 在 data 字段中
          requirements = response.data.records
        } else if (response.data && response.data.rows) {
          // 旧的分页数据格式
          requirements = response.data.rows
        } else if (response.data && Array.isArray(response.data)) {
          // 直接数组格式
          requirements = response.data
        } else if (response.rows) {
          // 兼容旧格式
          requirements = response.rows
        } else {
          requirements = []
        }
        this.recordList = requirements
          .filter(item => item.id != null && item.id !== undefined && item.id !== '') // 过滤掉无效id的数据
          .map(item => {
            // 确保ID是有效的整数
            const id = parseInt(item.id)
            if (isNaN(id)) {
              return null
            }

            const mappedItem = {
              id: id, // 确保id是整数
              requirementName: item.task_name || item.taskName || '', // 任务名称
              entityKeyword: item.task_description || item.taskDescription || '', // 任务描述作为关键词显示
              specificRequirement: item.task_description || item.taskDescription || '', // 任务描述
              createTime: item.create_time || item.createTime || '',
              // 将任务状态映射到前端的status字段
              // pending-待执行->0待生成, running-执行中->1生成中, completed-已完成->2已生成, failed-失败->-1生成失败
              status: this.mapTaskStatus(item.status || 'pending'),
              // 添加报告OSS URL字段
              reportOssUrl: item.report_oss_url || item.reportOssUrl || null
            }
            return mappedItem
          })
          .filter(item => item !== null) // 过滤掉映射失败的项

        // 适配新的总记录数格式：使用 model_content 后，total 会直接在响应根级别
        if (response.total !== undefined) {
          this.total = response.total
        } else if (response.data && response.data.total !== undefined) {
          this.total = response.data.total
        } else {
          this.total = 0
        }

        this.loading = false
      }).catch(error => {
        this.$modal.msgError('获取分析记录列表失败: ' + (error.message || '未知错误'))
        this.loading = false
      })
    },

    /** 映射任务状态 */
    mapTaskStatus(taskStatus) {
      // 数据库task status: pending-待执行，running-执行中，completed-已完成，failed-失败，paused-暂停
      // 前端status: 0-待生成，1-生成中，2-已生成，-1-生成失败
      const statusMap = {
        'pending': 0, // 待执行 -> 待生成
        'running': 1, // 执行中 -> 生成中
        'completed': 2, // 已完成 -> 已生成
        'failed': -1, // 失败 -> 生成失败
        'paused': 0 // 暂停 -> 待生成
      }
      return statusMap[taskStatus] !== undefined ? statusMap[taskStatus] : 0
    },

    /** 映射分析状态（保留兼容性） */
    mapAnalysisStatus(analysisStatus) {
      // 数据库analysis_status: 0-未开始，1-分析中，2-已完成，3-失败
      // 前端status: 0-待生成，1-生成中，2-已生成，-1-生成失败
      const statusMap = {
        0: 0, // 未开始 -> 待生成
        1: 1, // 分析中 -> 生成中
        2: 2, // 已完成 -> 已生成
        3: -1 // 失败 -> 生成失败
      }
      return statusMap[analysisStatus] !== undefined ? statusMap[analysisStatus] : 0
    },

    /** 需求名称输入监听 */
    onRequirementNameInput(value) {
      console.log('🔍 需求名称输入变化:', value)
      // 可以添加防抖逻辑，避免频繁查询
      // this.debounceSearch()
    },

    /** 关键词输入监听 */
    onEntityKeywordInput(value) {
      console.log('🔍 关键词输入变化:', value)
      // 可以添加防抖逻辑，避免频繁查询
      // this.debounceSearch()
    },

    /** 分析状态变化监听 */
    onAnalysisStatusChange(value) {
      console.log('🔍 分析状态变化:', value)
      // 状态变化时立即搜索
      this.handleQuery()
    },

    /** 搜索按钮操作 */
    handleQuery() {
      // 重置到第一页
      this.queryParams.page_num = 1
      // 添加搜索日志
      console.log('🔍 执行搜索，查询参数:', this.queryParams)
      this.getList()
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      // 手动重置查询参数
      this.queryParams = {
        page_num: 1,
        page_size: 10,
        requirementName: null,
        entityKeyword: null,
        analysisStatus: null
      }
      // 重置表单
      this.resetForm('queryForm')
      // 添加重置日志
      console.log('🔄 重置搜索条件，查询参数:', this.queryParams)
      this.handleQuery()
    },

    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      // 过滤掉id为null或undefined的项，并确保id是整数
      this.ids = selection
        .filter(item => item.id != null && item.id !== undefined)
        .map(item => parseInt(item.id))

      this.single = selection.length !== 1
      this.multiple = !selection.length
    },

    /** 查看分析日志 */
    handleViewLogs(row) {
      this.currentRecord = row
      this.logDialogVisible = true
      this.logLoading = true

      // 获取分析进度日志
      getAnalysisProgress(row.taskId || row.id).then(response => {
        this.logList = response.data || []
        this.logLoading = false
      }).catch(() => {
        this.logLoading = false
      })
    },

    /** 查看报告 */
    handleViewReport(row) {
      this.currentRecord = row
      this.reportDialogVisible = true
      this.reportLoading = true

      // 模拟获取报告数据
      setTimeout(() => {
        this.reportData = {
          requirementName: row.requirementName,
          entityKeyword: row.entityKeyword,
          specificRequirement: row.specificRequirement,
          totalArticles: Math.floor(Math.random() * 1000) + 100,
          sentiment: {
            positive: Math.floor(Math.random() * 50) + 20,
            neutral: Math.floor(Math.random() * 30) + 10,
            negative: Math.floor(Math.random() * 20) + 5
          }
        }
        this.reportLoading = false
      }, 1000)
    },

    /** 报告 */
    handleReport(row) {
      // 检查是否有报告OSS URL
      if (row.reportOssUrl) {
        // 如果有OSS URL，直接在新窗口打开
        window.open(row.reportOssUrl, '_blank')
      } else {
        // 如果没有OSS URL，显示暂无报告提示
        this.$modal.msg('暂无报告')
      }
    },

    /** 立即推送 */
    handlePushNow(row) {
      // 检查是否有报告OSS URL - 参照舆情分析页面的OSS检查逻辑
      if (!row.reportOssUrl) {
        this.$modal.msgWarning('报告尚未生成或上传到OSS，请等待报告生成完成后再推送')
        return
      }

      // 检查任务状态是否为已完成
      if (row.status !== 2) {
        this.$modal.msgWarning('任务尚未完成，请等待分析完成后再推送')
        return
      }

      this.currentRecord = row
      this.pushForm = {
        push_url: '',
        push_content: `分析报告：${row.requirementName} - 立即推送`
      }
      this.pushDialogVisible = true
    },

    /** 定时推送 */
    handleSchedulePush(row) {
      this.currentRecord = row
      this.schedulePushForm = {
        push_url: '',
        push_time: '',
        frequency: 'once',
        push_content: `分析报告：${row.requirementName}`
      }
      this.schedulePushDialogVisible = true
    },

    /** 提交立即推送 */
    submitPush() {
      this.$refs['pushForm'].validate(valid => {
        if (valid) {
          // 参照舆情分析页面的OSS检查逻辑 - 验证推送前的必要条件
          if (!this.currentRecord.reportOssUrl) {
            this.$modal.msgError('报告尚未上传到OSS，无法推送。请等待报告生成完成后再试。')
            return
          }

          // 检查任务状态
          if (this.currentRecord.status !== 2) {
            this.$modal.msgError('任务尚未完成，无法推送。请等待分析完成后再试。')
            return
          }

          // 验证推送URL格式（参照源文件的URL验证逻辑）
          if (!this.validatePushUrl(this.pushForm.push_url)) {
            return
          }

          this.pushLoading = true

          // 构建推送数据 - 参照舆情分析页面的数据结构
          const pushData = {
            target_url: this.pushForm.push_url,
            report_data: {
              requirementName: this.currentRecord.requirementName,
              entityKeyword: this.currentRecord.entityKeyword,
              specificRequirement: this.currentRecord.specificRequirement,
              reportOssUrl: this.currentRecord.reportOssUrl, // 确保有OSS URL
              totalArticles: 0, // 后端会从实际数据中获取
              sentiment: {}, // 后端会从实际数据中获取
              dataSources: 0, // 后端会从实际数据中获取
              taskId: this.currentRecord.id,
              createTime: this.currentRecord.createTime,
              status: this.currentRecord.status,
              pushTime: new Date().toISOString() // 添加推送时间戳
            },
            analysis_results: {
              summary: this.pushForm.push_content,
              reportUrl: this.currentRecord.reportOssUrl // 确保有报告URL
            },
            requirement_id: this.currentRecord.id,
            push_type: 'immediate'
          }

          pushReport(pushData).then(response => {
            // 检查响应并显示详细信息
            if (response && response.success) {
              this.$modal.msgSuccess('推送成功')

              // 如果后端生成了新的报告URL，可以更新本地记录
              if (response.data && response.data.report_url && !this.currentRecord.reportOssUrl) {
                this.currentRecord.reportOssUrl = response.data.report_url
                console.log('更新本地报告URL:', response.data.report_url)
              }
            } else {
              this.$modal.msgSuccess('推送完成')
            }

            this.pushDialogVisible = false
            this.pushLoading = false
          }).catch(error => {
            console.error('推送失败:', error)
            if (error.response && error.response.data && error.response.data.msg) {
              this.$modal.msgError('推送失败: ' + error.response.data.msg)
            } else {
              this.$modal.msgError('推送失败，请重试')
            }
            this.pushLoading = false
          })
        }
      })
    },

    /** 提交定时推送 */
    submitSchedulePush() {
      this.$refs['schedulePushForm'].validate(valid => {
        if (valid) {
          this.schedulePushLoading = true
          const taskData = {
            requirement_id: this.currentRecord.id,
            push_url: this.schedulePushForm.push_url,
            push_time: this.schedulePushForm.push_time,
            frequency: this.schedulePushForm.frequency,
            push_content: this.schedulePushForm.push_content
          }

          createTimedTask(taskData).then(response => {
            this.$modal.msgSuccess('定时推送任务创建成功')
            this.schedulePushDialogVisible = false
            this.schedulePushLoading = false
          }).catch(() => {
            this.schedulePushLoading = false
          })
        }
      })
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      let ids, names

      // 判断是否为批量删除（无参数或参数为事件对象）
      if (!row || row instanceof Event) {
        // 批量删除
        if (!this.ids || this.ids.length === 0) {
          this.$modal.msgError('请先选择要删除的记录')
          return
        }
        // 过滤并验证IDs
        ids = this.ids.filter(id => id != null && !isNaN(id)).map(id => parseInt(id))
        if (ids.length === 0) {
          this.$modal.msgError('选中的记录ID无效，无法删除')
          return
        }
        names = this.recordList.filter(item => ids.includes(item.id)).map(item => item.requirementName).join('、')
      } else {
        // 单个删除
        if (!row.id || row.id == null) {
          this.$modal.msgError('记录ID无效，无法删除')
          return
        }
        ids = [parseInt(row.id)]
        names = row.requirementName || '未知记录'
      }
      this.$modal.confirm(`是否确认删除分析记录"${names}"？`).then(() => {
        // 根据IDs数量判断是单个删除还是批量删除
        if (ids.length === 1) {
          // 单个删除
          return deleteAnalysisTask(ids[0])
        } else {
          // 批量删除
          return batchDeleteAnalysisTasks(ids)
        }
      }).then(response => {
        console.log('🗑️ 删除操作成功，响应:', response)

        // 检查是否需要重置分页（物理删除后需要重新计算分页）
        const currentPageRecords = this.recordList.length
        const deletedCount = ids.length

        // 如果删除后当前页可能没有记录了，且不是第一页，则回到上一页
        if (currentPageRecords <= deletedCount && this.queryParams.page_num > 1) {
          this.queryParams.page_num = this.queryParams.page_num - 1
          console.log('🔄 删除后回到上一页:', this.queryParams.page_num)
        }

        // 重置选中状态
        this.ids = []
        this.single = true
        this.multiple = true

        // 物理删除成功后，直接重新获取数据以确保数据一致性
        this.getList()

        this.$modal.msgSuccess('删除成功')
      }).catch(error => {
        if (error.response && error.response.data) {
          if (error.response.data.msg) {
            this.$modal.msgError(error.response.data.msg)
          } else if (error.response.data.message) {
            this.$modal.msgError(error.response.data.message)
          } else if (error.response.data.detail) {
            // 处理FastAPI的验证错误
            if (Array.isArray(error.response.data.detail)) {
              const details = error.response.data.detail.map(d => d.msg || d.message || JSON.stringify(d)).join(', ')
              this.$modal.msgError(`删除失败: ${details}`)
            } else {
              this.$modal.msgError(`删除失败: ${error.response.data.detail}`)
            }
          } else {
            this.$modal.msgError(`删除失败: ${JSON.stringify(error.response.data)}`)
          }
        } else if (error.message) {
          this.$modal.msgError(`删除失败: ${error.message}`)
        } else {
          this.$modal.msgError('删除失败，请重试')
        }
      })
    },

    /** 导出按钮操作 */
    handleExport() {
      this.$modal.confirm('是否确认导出所有分析记录数据项？').then(() => {
        const params = {}

        // 添加搜索条件（使用后端期望的下划线命名）
        if (this.queryParams.requirementName) {
          params.requirement_name = this.queryParams.requirementName
        }
        if (this.queryParams.entityKeyword) {
          params.entity_keyword = this.queryParams.entityKeyword
        }
        if (this.queryParams.analysisStatus !== null && this.queryParams.analysisStatus !== undefined && this.queryParams.analysisStatus !== '') {
          params.analysis_status = this.queryParams.analysisStatus
        }

        // 添加时间范围
        if (this.dateRange && this.dateRange.length === 2) {
          params.create_time_start = this.dateRange[0]
          params.create_time_end = this.dateRange[1]
        }

        return exportRequirements(params)
      }).then(response => {
        // 处理文件下载
        const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `分析记录_${new Date().toISOString().slice(0, 10)}.xlsx`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        this.$modal.msgSuccess('导出成功')
      }).catch(error => {
        if (error.response && error.response.data && error.response.data.msg) {
          this.$modal.msgError(error.response.data.msg)
        } else {
          this.$modal.msgError('导出失败，请重试')
        }
      })
    },

    /** 导出报告 */
    handleExportReport() {
      this.$modal.msgSuccess('报告导出成功')
    },

    /** 验证推送URL格式（参照舆情分析页面的验证逻辑） */
    validatePushUrl(url) {
      if (!url || !url.trim()) {
        this.$modal.msgError('请输入推送目标URL地址')
        return false
      }

      let fullUrl = url.trim()

      // 如果没有协议前缀，自动添加https://
      if (!fullUrl.includes('://')) {
        fullUrl = 'https://' + fullUrl
      }

      // 基本URL格式验证
      try {
        new URL(fullUrl)
        console.log('URL验证通过:', fullUrl)
        return fullUrl
      } catch (error) {
        // 宽松验证：如果包含://且长度合理，则认为可能是有效URL
        if (fullUrl.includes('://') && fullUrl.length > 10) {
          console.log('使用宽松验证通过URL:', fullUrl)
          this.$modal.msgWarning('URL格式可能不正确，但仍将尝试推送。如果推送失败，请检查URL格式')
          return fullUrl
        }

        this.$modal.msgError('URL格式不正确，请检查后重试')
        return false
      }
    },

    /** 获取状态标签类型 */
    getStatusTagType(status) {
      const statusMap = {
        0: 'info', // 待生成 - 灰色
        1: 'warning', // 生成中 - 橙色
        2: 'success', // 已生成 - 绿色
        '-1': 'danger' // 生成失败 - 红色
      }
      return statusMap[status] || 'info'
    },

    /** 获取状态文本 */
    getStatusText(status) {
      const statusMap = {
        0: '待生成',
        1: '生成中',
        2: '已生成',
        '-1': '生成失败'
      }
      return statusMap[status] || '未知'
    },

    /** 获取日志级别标签类型 */
    getLogLevelTagType(level) {
      const levelMap = {
        'info': 'info',
        'success': 'success',
        'warning': 'warning',
        'error': 'danger'
      }
      return levelMap[level] || 'info'
    },

    // ==================== 定时任务相关方法 ====================

    // 处理定时推送按钮点击
    handleTimedPush() {
      this.timedTaskDialogVisible = true
      // 打开定时推送弹窗时加载定时任务列表
      this.loadTimedTaskList()
    },

    // 关闭定时任务弹窗
    closeTimedTaskDialog() {
      this.timedTaskDialogVisible = false
    },

    // 处理创建定时任务
    handleCreateTimedTask() {
      this.resetTaskForm()
      this.editingTaskIndex = -1 // 设置为新建模式
      this.loadOpinionTaskList() // 加载舆情任务列表
      this.createTaskDialogVisible = true
    },

    // 处理添加定时任务按钮
    handleAddTimedTask() {
      this.resetTaskForm()
      this.editingTaskIndex = -1 // 设置为新建模式
      this.loadOpinionTaskList() // 加载舆情任务列表
      this.createTaskDialogVisible = true
    },

    // 加载舆情任务列表（用于定时推送绑定）
    async loadOpinionTaskList() {
      try {
        const response = await getAnalysisTaskList({
          page_num: 1,
          page_size: 100
        })

        if (response.success) {
          // 处理分页数据，从 opinion_task 表获取数据
          let tasks = []

          if (response.data && response.data.rows) {
            tasks = response.data.rows
          } else if (Array.isArray(response.data)) {
            tasks = response.data
          } else if (response.rows) {
            tasks = response.rows
          }

          // 映射任务数据到需求列表格式
          this.requirementList = tasks.map(task => ({
            id: task.id,
            requirementName: task.requirementName || task.requirement_name || `任务${task.id}`,
            taskId: task.id,
            keywords: task.keywords || task.entity_keyword,
            specificRequirement: task.specificRequirement || task.specific_requirement,
            createTime: task.createTime || task.create_time
          }))
        } else {
          this.$message.error(response.msg || '获取舆情任务列表失败')
          this.requirementList = []
        }
      } catch (error) {
        this.$message.error('加载舆情任务列表失败')
        this.requirementList = []
      }
    },

    // 加载定时任务列表
    async loadTimedTaskList() {
      try {
        const response = await getTimedTaskList()

        if (response.success) {
          // 处理分页数据 - 后端返回的是PageResponseModel格式

          if (response.data && response.data.rows) {
            this.timedTaskList = response.data.rows.map(task => ({
              id: task.id,
              requirementId: task.requirementId,
              name: task.taskName,
              description: task.taskDescription,
              executeTime: task.executeTime,
              executeDateTime: task.frequency === 'once' && task.execute_date ? `${task.execute_date} ${task.executeTime}` : '',
              frequency: task.frequency,
              status: task.status === 'running' ? 'running' : 'pending',
              pushUrl: task.pushUrl
            }))
          } else if (response.data && response.data.records && Array.isArray(response.data.records)) {
            this.timedTaskList = response.data.records.map(task => ({
              id: task.id,
              requirementId: task.requirementId,
              name: task.taskName,
              description: task.taskDescription,
              executeTime: task.executeTime,
              executeDateTime: task.frequency === 'once' && task.execute_date ? `${task.execute_date} ${task.executeTime}` : '',
              frequency: task.frequency,
              status: task.status === 'running' ? 'running' : 'pending',
              pushUrl: task.pushUrl
            }))
          } else if (Array.isArray(response.data)) {
            this.timedTaskList = response.data.map(task => ({
              id: task.id,
              requirementId: task.requirementId,
              name: task.taskName,
              description: task.taskDescription,
              executeTime: task.executeTime,
              executeDateTime: task.frequency === 'once' && task.execute_date ? `${task.execute_date} ${task.executeTime}` : '',
              frequency: task.frequency,
              status: task.status === 'running' ? 'running' : 'pending',
              pushUrl: task.pushUrl
            }))
          } else if (response.rows && Array.isArray(response.rows)) {
            this.timedTaskList = response.rows.map(task => ({
              id: task.id,
              requirementId: task.requirementId,
              name: task.taskName,
              description: task.taskDescription,
              executeTime: task.executeTime,
              executeDateTime: task.frequency === 'once' && task.execute_date ? `${task.execute_date} ${task.executeTime}` : '',
              frequency: task.frequency,
              status: task.status === 'running' ? 'running' : 'pending',
              pushUrl: task.pushUrl
            }))
          } else if (response.records && Array.isArray(response.records)) {
            this.timedTaskList = response.records.map(task => ({
              id: task.id,
              requirementId: task.requirementId,
              name: task.taskName,
              description: task.taskDescription,
              executeTime: task.executeTime,
              executeDateTime: task.frequency === 'once' && task.execute_date ? `${task.execute_date} ${task.executeTime}` : '',
              frequency: task.frequency,
              status: task.status === 'running' ? 'running' : 'pending',
              pushUrl: task.pushUrl
            }))
          } else {
            this.timedTaskList = []
          }
        } else {
          this.$message.error(response.msg || '获取定时任务列表失败')
          this.timedTaskList = []
        }
      } catch (error) {
        this.$message.error('加载定时任务列表失败')
        this.timedTaskList = []
      }
    },

    // 关闭创建任务弹窗
    closeCreateTaskDialog() {
      this.createTaskDialogVisible = false
      this.resetTaskForm()
      this.editingTaskIndex = -1 // 重置编辑状态
    },

    // 重置任务表单
    resetTaskForm() {
      this.taskForm = {
        requirement_id: '',
        name: '',
        description: '',
        execute_time: '16:00',
        execute_date_time: '',
        frequency: 'daily',
        push_url: ''
      }
    },

    // 保存并运行任务
    async saveAndRunTask() {
      if (!this.validateTaskForm()) {
        return
      }

      try {
        const taskData = this.buildTaskData()

        if (this.editingTaskIndex === -1) {
          // 新建任务
          const response = await createTimedTask(taskData)
          if (response.success) {
            this.$message.success('任务已保存并开始运行')
            // 重新加载任务列表
            await this.loadTimedTaskList()
            // 更新任务状态为运行中
            if (response.data && response.data.id) {
              await updateTaskStatus(response.data.id, 'running')
              await this.loadTimedTaskList()
            }
          } else {
            this.$message.error('创建任务失败：' + response.msg)
            return
          }
        } else {
          // 编辑任务
          const task = this.timedTaskList[this.editingTaskIndex]
          const response = await updateTimedTask(task.id, taskData)

          if (response.success) {
            // 更新任务状态为运行中
            await updateTaskStatus(task.id, 'running')
            this.$message.success('任务已更新并开始运行')
            // 重新加载任务列表
            await this.loadTimedTaskList()
          } else {
            this.$message.error('更新任务失败：' + response.msg)
            return
          }
        }

        this.createTaskDialogVisible = false
        this.resetTaskForm()
        this.editingTaskIndex = -1 // 重置编辑状态
      } catch (error) {
        this.$message.error('保存任务失败，请重试')
      }
    },

    // 保存任务计划
    async saveTaskPlan() {
      if (!this.validateTaskForm()) {
        return
      }

      try {
        const taskData = this.buildTaskData()

        if (this.editingTaskIndex === -1) {
          // 新建任务
          const response = await createTimedTask(taskData)
          if (response.success) {
            this.$message.success('任务计划已保存')
            // 重新加载任务列表
            await this.loadTimedTaskList()
          } else {
            this.$message.error('保存任务计划失败：' + response.msg)
            return
          }
        } else {
          // 编辑任务
          const task = this.timedTaskList[this.editingTaskIndex]
          const response = await updateTimedTask(task.id, taskData)

          if (response.success) {
            this.$message.success('任务计划已更新')
            // 重新加载任务列表
            await this.loadTimedTaskList()
          } else {
            this.$message.error('更新任务计划失败：' + response.msg)
            return
          }
        }

        this.createTaskDialogVisible = false
        this.resetTaskForm()
        this.editingTaskIndex = -1 // 重置编辑状态
      } catch (error) {
        this.$message.error('保存任务计划失败，请重试')
      }
    },

    // 验证任务表单
    validateTaskForm() {
      if (!this.taskForm.requirement_id) {
        this.$message.warning('请选择要关联的舆情分析任务')
        return false
      }

      if (!this.taskForm.name || !this.taskForm.name.trim()) {
        this.$message.warning('请输入任务名称')
        return false
      }

      if (!this.taskForm.description || !this.taskForm.description.trim()) {
        this.$message.warning('请输入任务描述')
        return false
      }

      // 验证执行时间
      if (this.taskForm.frequency === 'once') {
        if (!this.taskForm.execute_date_time) {
          this.$message.warning('请选择执行日期和时间')
          return false
        }
        // 检查是否是未来时间
        const executeTime = new Date(this.taskForm.execute_date_time)
        const now = new Date()
        if (executeTime <= now) {
          this.$message.warning('执行时间必须是未来时间')
          return false
        }
      } else {
        if (!this.taskForm.execute_time) {
          this.$message.warning('请选择执行时间')
          return false
        }
      }

      return true
    },

    // 构建任务数据
    buildTaskData() {
      // 处理执行时间：对于一次性任务，只提取时间部分；对于周期性任务，直接使用时间
      let executeTime
      if (this.taskForm.frequency === 'once') {
        // 一次性任务：从完整日期时间中提取时间部分 (HH:MM)
        if (this.taskForm.execute_date_time) {
          const dateTime = new Date(this.taskForm.execute_date_time)
          executeTime = `${dateTime.getHours().toString().padStart(2, '0')}:${dateTime.getMinutes().toString().padStart(2, '0')}`
        } else {
          executeTime = '09:00' // 默认时间
        }
      } else {
        // 周期性任务：直接使用时间
        executeTime = this.taskForm.execute_time || '09:00'
      }

      const taskData = {
        requirement_id: this.taskForm.requirement_id,
        task_name: this.taskForm.name,
        task_type: 'scheduled',
        frequency: this.taskForm.frequency, // 使用 frequency 而不是 schedule_type
        execute_time: executeTime, // 只存储时间部分 (HH:MM)
        task_description: this.taskForm.description,
        push_url: this.taskForm.push_url || ''
      }

      // 对于一次性任务，可能需要额外的日期信息
      if (this.taskForm.frequency === 'once' && this.taskForm.execute_date_time) {
        taskData['execute_date'] = this.taskForm.execute_date_time.split(' ')[0] // 提取日期部分 (YYYY-MM-DD)
      }

      return taskData
    },

    // 删除任务
    async deleteTask(index) {
      try {
        const task = this.timedTaskList[index]

        if (!task) {
          this.$message.error('任务数据不存在')
          return
        }

        await this.$confirm(`确定要删除任务「${task.name}」吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const response = await deleteTimedTask(task.id)

        if (response.success) {
          this.$message.success('删除成功')
          // 重新加载任务列表
          await this.loadTimedTaskList()
        } else {
          this.$message.error('删除失败：' + (response.msg || response.message || '未知错误'))
        }
      } catch (error) {
        if (error === 'cancel') {
          return
        }
        this.$message.error('删除任务失败，请重试')
      }
    },

    // 编辑任务
    editTask(index) {
      const task = this.timedTaskList[index]

      if (!task) {
        this.$message.error('任务数据不存在')
        return
      }

      this.editingTaskIndex = index
      this.taskForm = {
        requirement_id: task.requirementId || '',
        name: task.name || '',
        description: task.description || '',
        execute_time: task.executeTime || '16:00',
        execute_date_time: task.executeDateTime || '',
        frequency: task.frequency || 'daily',
        push_url: task.pushUrl || ''
      }

      this.loadOpinionTaskList() // 加载舆情任务列表
      this.createTaskDialogVisible = true
    },

    // 切换任务状态
    async toggleTaskStatus(index) {
      try {
        const task = this.timedTaskList[index]
        const newStatus = task.status === 'running' ? 'pending' : 'running'

        const response = await updateTaskStatus(task.id, newStatus)

        if (response.success) {
          if (newStatus === 'running') {
            this.$message.success(`任务「${task.name}」已启动`)
          } else {
            this.$message.info(`任务「${task.name}」已暂停`)
          }
          // 重新加载任务列表
          await this.loadTimedTaskList()
        } else {
          this.$message.error('状态更新失败：' + response.msg)
        }
      } catch (error) {
        this.$message.error('状态更新失败，请重试')
      }
    },

    // 预览任务
    previewTask(index) {
      const task = this.timedTaskList[index]
      if (!task) {
        this.$message.error('任务数据不存在')
        return
      }
      this.taskPreviewDialog.taskData = task
      this.taskPreviewDialog.visible = true
    },

    // 获取任务计划文本
    getTaskScheduleText(task) {
      if (!task) return ''

      if (task.frequency === 'once') {
        // 一次性任务显示具体执行时间
        return `仅一次 ${task.executeDateTime || task.executeTime}`
      } else {
        // 周期性任务显示频率和时间
        const frequencyText = this.getFrequencyText(task.frequency)
        return `${frequencyText} ${task.executeTime}`
      }
    },

    // 获取频率文本
    getFrequencyText(frequency) {
      const frequencyMap = {
        'once': '仅一次',
        'daily': '每天',
        'weekly': '每周',
        'monthly': '每月'
      }
      return frequencyMap[frequency] || frequency
    },

    // 修改计划
    modifyPlan() {
      // 这里可以添加修改计划的逻辑
      this.$message.info('修改计划功能待实现')
    },

    /** 时间格式化 */
    parseTime
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.mb8 {
  margin-bottom: 8px;
}

.report-content {
  min-height: 400px;

  .analysis-results {
    margin-top: 20px;

    .statistic-value {
      font-size: 24px;
      font-weight: bold;
      text-align: center;
      padding: 20px 0;

      &.positive {
        color: #67c23a;
      }

      &.negative {
        color: #f56c6c;
      }
    }
  }
}

.el-table .cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.small-padding .cell {
  padding-left: 5px;
  padding-right: 5px;
}

.fixed-width .el-button--mini {
  margin-right: 5px;
}

.dialog-footer {
  text-align: right;
}

// 状态标签样式
.el-tag {
  &.el-tag--info {
    background-color: #f4f4f5;
    border-color: #e9e9eb;
    color: #909399;
  }

  &.el-tag--warning {
    background-color: #fdf6ec;
    border-color: #f5dab1;
    color: #e6a23c;
  }

  &.el-tag--success {
    background-color: #f0f9ff;
    border-color: #c6f7d0;
    color: #67c23a;
  }

  &.el-tag--danger {
    background-color: #fef0f0;
    border-color: #fbc4c4;
    color: #f56c6c;
  }
}

// 操作按钮样式
.el-button--text {
  padding: 0;
  margin-right: 10px;

  &:last-child {
    margin-right: 0;
  }
}

// 搜索表单样式
.el-form--inline .el-form-item {
  margin-right: 10px;
}

// 定时任务抽屉样式
.timed-task-drawer {
  .drawer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .drawer-title {
      font-size: 16px;
      font-weight: 500;
    }

    .add-task-btn {
      margin-left: auto;
    }
  }

  .drawer-content {
    padding: 20px;
    height: calc(100vh - 120px);
    overflow-y: auto;

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      text-align: center;

      .empty-content {
        .empty-icon {
          margin-bottom: 20px;
        }

        .empty-text {
          font-size: 14px;
          color: #999;
          margin-bottom: 20px;
        }

        .create-btn {
          padding: 10px 20px;
        }
      }
    }

    .task-list {
      .task-items {
        .task-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px;
          border: 1px solid #e8e8e8;
          border-radius: 8px;
          margin-bottom: 12px;
          background: #fff;

          &:hover {
            border-color: #409eff;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
          }

          .task-info {
            flex: 1;

            .task-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 8px;

              .task-name {
                font-size: 14px;
                font-weight: 500;
                color: #333;
              }

              .task-status {
                padding: 2px 8px;
                border-radius: 4px;
                font-size: 12px;

                &.status-running {
                  background: #f0f9ff;
                  color: #1890ff;
                }

                &.status-pending {
                  background: #f6f6f6;
                  color: #666;
                }
              }
            }

            .task-schedule {
              display: flex;
              align-items: center;
              font-size: 12px;
              color: #666;

              i {
                margin-right: 4px;
              }
            }
          }

          .task-actions {
            display: flex;
            gap: 8px;

            .el-button {
              padding: 4px;
              min-width: auto;
            }
          }
        }
      }
    }
  }
}

// 创建任务弹窗样式
.create-task-dialog {
  .task-form {
    .task-requirement-section,
    .execute-time-section {
      margin-bottom: 24px;

      .section-label {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 16px;
        color: #333;

        .required {
          color: #f56c6c;
          margin-left: 4px;
        }
      }

      .form-group {
        margin-bottom: 16px;

        .input-label {
          font-size: 13px;
          color: #666;
          margin-bottom: 8px;
        }

        .task-name-input,
        .task-description-input {
          width: 100%;
        }
      }

      .time-selector {
        display: flex;
        gap: 12px;

        .frequency-select {
          width: 120px;
        }

        .datetime-picker,
        .time-picker {
          flex: 1;
        }
      }
    }
  }

  .dialog-footer {
    text-align: right;

    .el-button {
      margin-left: 8px;
    }
  }
}
</style>
