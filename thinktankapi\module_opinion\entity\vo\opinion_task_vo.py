from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from module_admin.entity.vo.common_vo import PageQueryModel
from module_admin.annotation.pydantic_annotation import as_query


class OpinionTaskModel(BaseModel):
    """
    舆情任务模型
    """
    id: Optional[int] = Field(default=None, description='主键ID')
    requirement_id: int = Field(..., description='需求ID')
    user_id: int = Field(..., description='用户ID')
    task_name: str = Field(..., description='任务名称')
    task_description: Optional[str] = Field(default=None, description='任务描述')
    task_type: str = Field(..., description='任务类型')
    frequency: Optional[str] = Field(default=None, description='执行频率')
    execute_time: Optional[str] = Field(default=None, description='执行时间')
    cron_expression: Optional[str] = Field(default=None, description='Cron表达式')
    status: Optional[str] = Field(default='completed', description='任务状态')
    push_url: Optional[str] = Field(default=None, description='推送URL')
    push_config: Optional[str] = Field(default=None, description='推送配置')
    last_execute_time: Optional[datetime] = Field(default=None, description='最后执行时间')
    next_execute_time: Optional[datetime] = Field(default=None, description='下次执行时间')
    execute_count: Optional[int] = Field(default=0, description='执行次数')
    success_count: Optional[int] = Field(default=0, description='成功次数')
    fail_count: Optional[int] = Field(default=0, description='失败次数')
    is_enabled: Optional[int] = Field(default=1, description='是否启用')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')
    create_by: Optional[str] = Field(default='', description='创建者')
    update_by: Optional[str] = Field(default='', description='更新者')
    remark: Optional[str] = Field(default='', description='备注信息')
    report_oss_url: Optional[str] = Field(default=None, description='报告OSS访问URL')
    positive_count: Optional[int] = Field(default=0, description='正面情感数量')
    negative_count: Optional[int] = Field(default=0, description='负面情感数量')
    neutral_count: Optional[int] = Field(default=0, description='中性情感数量')


@as_query
class OpinionTaskPageQueryModel(PageQueryModel):
    """
    舆情任务分页查询模型
    """
    requirement_id: Optional[int] = Field(default=None, description='需求ID')
    user_id: Optional[int] = Field(default=None, description='用户ID')
    task_name: Optional[str] = Field(default=None, description='任务名称')
    task_type: Optional[str] = Field(default=None, description='任务类型')
    task_description: Optional[str] = Field(default=None, description='任务描述（用于关键词搜索）')
    frequency: Optional[str] = Field(default=None, description='执行频率')
    status: Optional[str] = Field(default=None, description='任务状态')
    is_enabled: Optional[int] = Field(default=None, description='是否启用')
    create_time_start: Optional[str] = Field(default=None, description='创建时间开始')
    create_time_end: Optional[str] = Field(default=None, description='创建时间结束')


class CreateOpinionTaskModel(BaseModel):
    """
    创建舆情任务模型
    """
    requirement_id: int = Field(..., description='需求ID')
    task_name: str = Field(..., description='任务名称')
    task_description: Optional[str] = Field(default='', description='任务描述')
    task_type: str = Field(..., description='任务类型：immediate-立即执行，scheduled-定时执行')
    schedule_type: Optional[str] = Field(default=None, description='调度类型：once-单次，daily-每日，weekly-每周，monthly-每月')
    schedule_config: Optional[Dict[str, Any]] = Field(default=None, description='调度配置')
    execution_time: Optional[datetime] = Field(default=None, description='执行时间')
    frequency: Optional[str] = Field(default=None, description='执行频率（前端兼容字段）')
    execute_time: Optional[str] = Field(default=None, description='执行时间字符串（前端兼容字段）')
    push_url: Optional[str] = Field(default=None, description='推送URL')
    push_config: Optional[str] = Field(default=None, description='推送配置')
    max_execution_count: Optional[int] = Field(default=None, description='最大执行次数')
    priority: Optional[str] = Field(default='medium', description='优先级')
    timeout_minutes: Optional[int] = Field(default=60, description='超时时间（分钟）')
    max_retry_count: Optional[int] = Field(default=3, description='最大重试次数')
    remark: Optional[str] = Field(default='', description='备注信息')


class UpdateOpinionTaskModel(BaseModel):
    """
    更新舆情任务模型
    """
    id: Optional[int] = Field(default=None, description='主键ID')
    task_name: Optional[str] = Field(default=None, description='任务名称')
    task_type: Optional[str] = Field(default=None, description='任务类型')
    schedule_type: Optional[str] = Field(default=None, description='调度类型')
    schedule_config: Optional[Dict[str, Any]] = Field(default=None, description='调度配置')
    execution_time: Optional[datetime] = Field(default=None, description='执行时间')
    max_execution_count: Optional[int] = Field(default=None, description='最大执行次数')
    task_status: Optional[str] = Field(default=None, description='任务状态')
    priority: Optional[str] = Field(default=None, description='优先级')
    timeout_minutes: Optional[int] = Field(default=None, description='超时时间（分钟）')
    max_retry_count: Optional[int] = Field(default=None, description='最大重试次数')
    remark: Optional[str] = Field(default=None, description='备注信息')


class UpdateTaskStatusModel(BaseModel):
    """
    更新任务状态模型
    """
    task_id: int = Field(..., description='任务ID')
    task_status: str = Field(..., description='任务状态：pending-待执行，running-执行中，completed-已完成，failed-失败，paused-暂停')


class UpdateSentimentCountsModel(BaseModel):
    """
    更新任务情感统计数据模型
    """
    task_id: int = Field(..., description='任务ID')
    positive_count: int = Field(..., description='正面情感数量', ge=0)
    negative_count: int = Field(..., description='负面情感数量', ge=0)
    neutral_count: int = Field(..., description='中性情感数量', ge=0)


class DeleteOpinionTaskModel(BaseModel):
    """
    删除舆情任务模型
    """
    ids: List[int] = Field(..., description='需要删除的任务ID列表')


class TaskScheduleConfigModel(BaseModel):
    """
    任务调度配置模型
    """
    schedule_type: str = Field(..., description='调度类型')
    start_date: Optional[str] = Field(default=None, description='开始日期')
    end_date: Optional[str] = Field(default=None, description='结束日期')
    execution_time: Optional[str] = Field(default=None, description='执行时间')
    interval_hours: Optional[int] = Field(default=None, description='间隔小时数')
    days_of_week: Optional[List[int]] = Field(default=None, description='星期几执行（1-7）')
    day_of_month: Optional[int] = Field(default=None, description='每月第几天执行')
    cron_expression: Optional[str] = Field(default=None, description='Cron表达式')


class TaskExecutionLogModel(BaseModel):
    """
    任务执行日志模型
    """
    task_id: int = Field(..., description='任务ID')
    execution_time: datetime = Field(..., description='执行时间')
    execution_status: str = Field(..., description='执行状态')
    execution_result: Optional[str] = Field(default=None, description='执行结果')
    error_message: Optional[str] = Field(default=None, description='错误信息')
    execution_duration: Optional[int] = Field(default=None, description='执行时长（秒）')
