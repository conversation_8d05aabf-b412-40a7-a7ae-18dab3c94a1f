{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\analyze-record\\index.vue?vue&type=style&index=0&id=b758b5e2&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\analyze-record\\index.vue", "mtime": 1753421184867}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753065270777}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753065272975}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753065271542}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1753065270150}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753065270161}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753065272328}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA4pDA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/analyze-record", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 搜索区域 -->\r\n    <el-form v-show=\"showSearch\" ref=\"queryForm\" :model=\"queryParams\" size=\"small\" :inline=\"true\" label-width=\"68px\">\r\n\r\n      <el-form-item label=\"需求名称\" prop=\"requirementName\">\r\n        <el-input\r\n          id=\"requirementName\"\r\n          v-model=\"queryParams.requirementName\"\r\n          placeholder=\"请输入需求名称\"\r\n          clearable\r\n          @input=\"onRequirementNameInput\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"关键词\" prop=\"entityKeyword\">\r\n        <el-input\r\n          id=\"entityKeyword\"\r\n          v-model=\"queryParams.entityKeyword\"\r\n          placeholder=\"请输入实体关键词\"\r\n          clearable\r\n          @input=\"onEntityKeywordInput\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"分析状态\" prop=\"analysisStatus\">\r\n        <el-select\r\n          id=\"analysisStatus\"\r\n          v-model=\"queryParams.analysisStatus\"\r\n          placeholder=\"请选择分析状态\"\r\n          clearable\r\n          @change=\"onAnalysisStatusChange\"\r\n        >\r\n          <el-option label=\"未开始\" value=\"0\" />\r\n          <el-option label=\"分析中\" value=\"1\" />\r\n          <el-option label=\"已完成\" value=\"2\" />\r\n          <el-option label=\"失败\" value=\"3\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"创建时间\">\r\n        <el-date-picker\r\n          id=\"create_time_range\"\r\n          v-model=\"dateRange\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 操作按钮区域 -->\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          v-hasPermi=\"['opinion:record:export']\"\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-time\"\r\n          size=\"mini\"\r\n          @click=\"handleTimedPush\"\r\n        >定时推送</el-button>\r\n      </el-col>\r\n      <right-toolbar :show-search.sync=\"showSearch\" @queryTable=\"getList\" />\r\n    </el-row>\r\n\r\n    <!-- 数据表格 -->\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"recordList\"\r\n      :default-sort=\"{prop: 'createTime', order: 'descending'}\"\r\n      @selection-change=\"handleSelectionChange\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"ID\" align=\"center\" prop=\"id\" width=\"80\" />\r\n      <el-table-column label=\"需求名称\" align=\"center\" prop=\"requirementName\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"关键词\" align=\"center\" prop=\"entityKeyword\" width=\"120\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"具体需求\" align=\"center\" prop=\"specificRequirement\" width=\"200\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"生成状态\" align=\"center\" prop=\"status\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            :type=\"getStatusTagType(scope.row.status)\"\r\n            size=\"mini\"\r\n          >\r\n            {{ getStatusText(scope.row.status) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"380\">\r\n        <template slot-scope=\"scope\">\r\n\r\n          <el-button\r\n            v-if=\"scope.row.status === 2\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-notebook-1\"\r\n            @click=\"handleReport(scope.row)\"\r\n          >报告</el-button>\r\n          <el-button\r\n            v-if=\"scope.row.status === 2\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-s-promotion\"\r\n            @click=\"handlePushNow(scope.row)\"\r\n          >立即推送</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <!-- 分页 -->\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.page_num\"\r\n      :limit.sync=\"queryParams.page_size\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 分析日志对话框 -->\r\n    <el-dialog title=\"分析日志\" :visible.sync=\"logDialogVisible\" width=\"1000px\" append-to-body>\r\n      <el-table\r\n        v-loading=\"logLoading\"\r\n        :data=\"logList\"\r\n        height=\"400\"\r\n        :default-sort=\"{prop: 'createTime', order: 'descending'}\"\r\n      >\r\n        <el-table-column label=\"时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"级别\" align=\"center\" prop=\"logLevel\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag\r\n              :type=\"getLogLevelTagType(scope.row.logLevel)\"\r\n              size=\"mini\"\r\n            >\r\n              {{ scope.row.logLevel.toUpperCase() }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"步骤\" align=\"center\" prop=\"stepName\" width=\"120\" />\r\n        <el-table-column label=\"进度\" align=\"center\" prop=\"progressPercentage\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ scope.row.progressPercentage }}%</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"日志内容\" align=\"center\" prop=\"logMessage\" :show-overflow-tooltip=\"true\" />\r\n      </el-table>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"logDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 查看报告对话框 -->\r\n    <el-dialog title=\"分析报告\" :visible.sync=\"reportDialogVisible\" width=\"1200px\" append-to-body>\r\n      <div v-loading=\"reportLoading\" class=\"report-content\">\r\n        <div v-if=\"reportData\">\r\n          <el-descriptions title=\"基本信息\" :column=\"2\" border>\r\n            <el-descriptions-item label=\"需求名称\">{{ reportData.requirementName }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"关键词\">{{ reportData.entityKeyword }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"具体需求\" :span=\"2\">{{ reportData.specificRequirement }}</el-descriptions-item>\r\n          </el-descriptions>\r\n\r\n          <el-divider content-position=\"left\">分析结果</el-divider>\r\n          <div class=\"analysis-results\">\r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"8\">\r\n                <el-card shadow=\"never\">\r\n                  <div slot=\"header\">\r\n                    <span>文章总数</span>\r\n                  </div>\r\n                  <div class=\"statistic-value\">{{ reportData.totalArticles || 0 }}</div>\r\n                </el-card>\r\n              </el-col>\r\n              <el-col :span=\"8\">\r\n                <el-card shadow=\"never\">\r\n                  <div slot=\"header\">\r\n                    <span>正面情感</span>\r\n                  </div>\r\n                  <div class=\"statistic-value positive\">{{ reportData.sentiment?.positive || 0 }}</div>\r\n                </el-card>\r\n              </el-col>\r\n              <el-col :span=\"8\">\r\n                <el-card shadow=\"never\">\r\n                  <div slot=\"header\">\r\n                    <span>负面情感</span>\r\n                  </div>\r\n                  <div class=\"statistic-value negative\">{{ reportData.sentiment?.negative || 0 }}</div>\r\n                </el-card>\r\n              </el-col>\r\n            </el-row>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"handleExportReport\">导出报告</el-button>\r\n        <el-button @click=\"reportDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 立即推送对话框 -->\r\n    <el-dialog title=\"立即推送\" :visible.sync=\"pushDialogVisible\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"pushForm\" :model=\"pushForm\" :rules=\"pushRules\" label-width=\"100px\">\r\n        <el-form-item label=\"推送地址\" prop=\"push_url\">\r\n          <el-input v-model=\"pushForm.push_url\" placeholder=\"请输入推送地址\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"推送内容\" prop=\"push_content\">\r\n          <el-input\r\n            v-model=\"pushForm.push_content\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n            placeholder=\"请输入推送内容\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"pushDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" :loading=\"pushLoading\" @click=\"submitPush\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 定时推送对话框 -->\r\n    <el-dialog title=\"定时推送\" :visible.sync=\"schedulePushDialogVisible\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"schedulePushForm\" :model=\"schedulePushForm\" :rules=\"schedulePushRules\" label-width=\"100px\">\r\n        <el-form-item label=\"推送地址\" prop=\"push_url\">\r\n          <el-input v-model=\"schedulePushForm.push_url\" placeholder=\"请输入推送地址\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"推送时间\" prop=\"push_time\">\r\n          <el-date-picker\r\n            v-model=\"schedulePushForm.push_time\"\r\n            type=\"datetime\"\r\n            placeholder=\"选择推送时间\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            style=\"width: 100%\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"推送频率\" prop=\"frequency\">\r\n          <el-select v-model=\"schedulePushForm.frequency\" placeholder=\"请选择推送频率\">\r\n            <el-option label=\"仅一次\" value=\"once\" />\r\n            <el-option label=\"每日\" value=\"daily\" />\r\n            <el-option label=\"每周\" value=\"weekly\" />\r\n            <el-option label=\"每月\" value=\"monthly\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"推送内容\" prop=\"push_content\">\r\n          <el-input\r\n            v-model=\"schedulePushForm.push_content\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n            placeholder=\"请输入推送内容\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"schedulePushDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" :loading=\"schedulePushLoading\" @click=\"submitSchedulePush\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 定时任务抽屉 -->\r\n    <el-drawer\r\n      title=\"定时任务\"\r\n      :visible.sync=\"timedTaskDialogVisible\"\r\n      direction=\"rtl\"\r\n      size=\"600px\"\r\n      :before-close=\"closeTimedTaskDialog\"\r\n      custom-class=\"timed-task-drawer\"\r\n    >\r\n      <!-- 抽屉头部右侧按钮 -->\r\n      <div slot=\"title\" class=\"drawer-header\">\r\n        <span class=\"drawer-title\">定时任务</span>\r\n        <el-button\r\n          type=\"primary\"\r\n          size=\"mini\"\r\n          icon=\"el-icon-plus\"\r\n          class=\"add-task-btn\"\r\n          @click=\"handleAddTimedTask\"\r\n        >\r\n          定时任务\r\n        </el-button>\r\n      </div>\r\n\r\n      <!-- 抽屉内容 -->\r\n      <div class=\"drawer-content\">\r\n        <!-- 空状态 -->\r\n        <div v-if=\"timedTaskList.length === 0\" class=\"empty-state\">\r\n          <div class=\"empty-content\">\r\n            <!-- 空状态图标 -->\r\n            <div class=\"empty-icon\">\r\n              <svg width=\"120\" height=\"120\" viewBox=\"0 0 120 120\" fill=\"none\">\r\n                <!-- 文件夹图标 -->\r\n                <path d=\"M20 30h25l5-10h50v70H20V30z\" fill=\"#f0f0f0\" stroke=\"#d0d0d0\" stroke-width=\"2\" />\r\n                <path d=\"M25 35h70v50H25V35z\" fill=\"#fafafa\" stroke=\"#e0e0e0\" stroke-width=\"1\" />\r\n                <!-- 文档图标 -->\r\n                <rect x=\"35\" y=\"45\" width=\"30\" height=\"25\" fill=\"#ffffff\" stroke=\"#d0d0d0\" stroke-width=\"1\" rx=\"2\" />\r\n                <rect x=\"70\" y=\"50\" width=\"20\" height=\"15\" fill=\"#ffffff\" stroke=\"#d0d0d0\" stroke-width=\"1\" rx=\"2\" />\r\n                <!-- 装饰线条 -->\r\n                <line x1=\"40\" y1=\"52\" x2=\"60\" y2=\"52\" stroke=\"#e0e0e0\" stroke-width=\"1\" />\r\n                <line x1=\"40\" y1=\"57\" x2=\"55\" y2=\"57\" stroke=\"#e0e0e0\" stroke-width=\"1\" />\r\n                <line x1=\"40\" y1=\"62\" x2=\"58\" y2=\"62\" stroke=\"#e0e0e0\" stroke-width=\"1\" />\r\n                <line x1=\"75\" y1=\"55\" x2=\"85\" y2=\"55\" stroke=\"#e0e0e0\" stroke-width=\"1\" />\r\n                <line x1=\"75\" y1=\"60\" x2=\"82\" y2=\"60\" stroke=\"#e0e0e0\" stroke-width=\"1\" />\r\n              </svg>\r\n            </div>\r\n            <p class=\"empty-text\">暂无定时任务</p>\r\n            <el-button type=\"primary\" class=\"create-btn\" @click=\"handleCreateTimedTask\">\r\n              去创建\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 任务列表 -->\r\n        <div v-else class=\"task-list\">\r\n          <div v-if=\"timedTaskList.length === 0\" class=\"empty-task-list\">\r\n            <div class=\"empty-icon\">📅</div>\r\n            <div class=\"empty-text\">暂无定时任务</div>\r\n            <el-button type=\"primary\" size=\"small\" class=\"add-task-btn\" @click=\"handleAddTimedTask\">\r\n              添加任务\r\n            </el-button>\r\n          </div>\r\n          <div v-else class=\"task-items\">\r\n            <div v-for=\"(task, index) in timedTaskList\" :key=\"index\" class=\"task-item\">\r\n              <div class=\"task-info\">\r\n                <div class=\"task-header\">\r\n                  <div class=\"task-name\">{{ task.name }}</div>\r\n                  <div class=\"task-status\" :class=\"{ 'status-running': task.status === 'running', 'status-pending': task.status === 'pending' }\">\r\n                    {{ task.status === 'running' ? '运行中' : '待运行' }}\r\n                  </div>\r\n                </div>\r\n                <!-- 任务描述已隐藏 -->\r\n                <div class=\"task-schedule\">\r\n                  <i class=\"el-icon-time\" />\r\n                  <span>{{ getTaskScheduleText(task) }}</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"task-actions\">\r\n                <el-button type=\"text\" size=\"mini\" title=\"预览任务详情\" @click.stop=\"previewTask(index)\">\r\n                  <i class=\"el-icon-view\" />\r\n                </el-button>\r\n                <el-button type=\"text\" size=\"mini\" :title=\"task.status === 'running' ? '暂停任务' : '启动任务'\" @click.stop=\"toggleTaskStatus(index)\">\r\n                  <i :class=\"task.status === 'running' ? 'el-icon-video-pause' : 'el-icon-video-play'\" />\r\n                </el-button>\r\n                <el-button type=\"text\" size=\"mini\" title=\"编辑任务\" style=\"margin-right: 5px;\" @click.stop=\"editTask(index)\">\r\n                  <i class=\"el-icon-edit\" />\r\n                </el-button>\r\n                <el-button type=\"text\" size=\"mini\" title=\"删除任务\" style=\"color: #f56c6c;\" @click.stop=\"deleteTask(index)\">\r\n                  <i class=\"el-icon-delete\" />\r\n                </el-button>\r\n                <!-- 测试按钮 -->\r\n                <el-button type=\"text\" size=\"mini\" title=\"测试点击\" style=\"color: #409eff;\" @click.stop=\"testClick(index)\">\r\n                  <i class=\"el-icon-info\" />\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 任务预览对话框 -->\r\n      <el-dialog\r\n        title=\"任务详情\"\r\n        :visible.sync=\"taskPreviewDialog.visible\"\r\n        width=\"600px\"\r\n        append-to-body\r\n      >\r\n        <div v-if=\"taskPreviewDialog.taskData\" class=\"task-preview-content\">\r\n          <el-descriptions title=\"基本信息\" :column=\"2\" border>\r\n            <el-descriptions-item label=\"任务名称\">{{ taskPreviewDialog.taskData.name }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"任务状态\">\r\n              <el-tag :type=\"taskPreviewDialog.taskData.status === 'running' ? 'success' : 'info'\">\r\n                {{ taskPreviewDialog.taskData.status === 'running' ? '运行中' : '待运行' }}\r\n              </el-tag>\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"执行频率\">{{ getFrequencyText(taskPreviewDialog.taskData.frequency) }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"执行时间\">{{ taskPreviewDialog.taskData.executeTime }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"推送地址\" :span=\"2\">{{ taskPreviewDialog.taskData.pushUrl || '未设置' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"任务描述\" :span=\"2\">{{ taskPreviewDialog.taskData.description || '无描述' }}</el-descriptions-item>\r\n          </el-descriptions>\r\n        </div>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button @click=\"taskPreviewDialog.visible = false\">关 闭</el-button>\r\n        </div>\r\n      </el-dialog>\r\n\r\n      <!-- 创建/编辑任务弹窗 -->\r\n      <el-dialog\r\n        :title=\"editingTaskIndex === -1 ? '创建定时任务' : '编辑定时任务'\"\r\n        :visible.sync=\"createTaskDialogVisible\"\r\n        width=\"500px\"\r\n        :before-close=\"closeCreateTaskDialog\"\r\n        :append-to-body=\"true\"\r\n        class=\"create-task-dialog\"\r\n      >\r\n        <div class=\"task-form\">\r\n          <!-- 任务需求 -->\r\n          <div class=\"task-requirement-section\">\r\n            <div class=\"section-label\">\r\n              任务需求\r\n              <span class=\"required\">*</span>\r\n            </div>\r\n            <div class=\"form-group\">\r\n              <div class=\"input-label\">关联任务</div>\r\n              <el-select\r\n                v-model=\"taskForm.requirement_id\"\r\n                placeholder=\"请选择要关联的舆情分析任务\"\r\n                class=\"task-name-input\"\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"task in requirementList\"\r\n                  :key=\"task.id\"\r\n                  :label=\"task.requirementName\"\r\n                  :value=\"task.id\"\r\n                >\r\n                  <span style=\"float: left\">{{ task.requirementName }}</span>\r\n                  <span style=\"float: right; color: #8492a6; font-size: 13px\">ID: {{ task.id }}</span>\r\n                </el-option>\r\n              </el-select>\r\n            </div>\r\n            <div class=\"form-group\">\r\n              <div class=\"input-label\">任务名称</div>\r\n              <el-input\r\n                v-model=\"taskForm.name\"\r\n                placeholder=\"请输入任务名称\"\r\n                class=\"task-name-input\"\r\n              />\r\n            </div>\r\n            <div class=\"form-group\">\r\n              <div class=\"input-label\">任务描述</div>\r\n              <el-input\r\n                v-model=\"taskForm.description\"\r\n                type=\"textarea\"\r\n                :rows=\"3\"\r\n                placeholder=\"请描述具体的任务需求，例如：基于当前分析需求自动生成AI新闻总结\"\r\n                class=\"task-description-input\"\r\n              />\r\n              <div class=\"form-group\">\r\n                <div class=\"input-label\">推送地址</div>\r\n                <el-input\r\n                  v-model=\"taskForm.push_url\"\r\n                  placeholder=\"例如：https://www.baidu.com\"\r\n                  class=\"task-name-input\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 执行时间 -->\r\n          <div class=\"execute-time-section\">\r\n            <div class=\"section-label\">执行时间</div>\r\n            <div class=\"time-selector\">\r\n              <el-select v-model=\"taskForm.frequency\" placeholder=\"选择频率\" class=\"frequency-select\">\r\n                <el-option label=\"仅一次\" value=\"once\" />\r\n                <el-option label=\"每天\" value=\"daily\" />\r\n                <el-option label=\"每周\" value=\"weekly\" />\r\n                <el-option label=\"每月\" value=\"monthly\" />\r\n              </el-select>\r\n              <!-- 一次性任务：选择具体日期时间 -->\r\n              <el-date-picker\r\n                v-if=\"taskForm.frequency === 'once'\"\r\n                v-model=\"taskForm.execute_date_time\"\r\n                type=\"datetime\"\r\n                placeholder=\"选择执行日期和时间\"\r\n                format=\"yyyy-MM-dd HH:mm\"\r\n                value-format=\"yyyy-MM-dd HH:mm\"\r\n                class=\"datetime-picker\"\r\n                :picker-options=\"{\r\n                  disabledDate(time) {\r\n                    return time.getTime() < Date.now() - 8.64e7\r\n                  }\r\n                }\"\r\n              />\r\n              <!-- 周期性任务：选择时间 -->\r\n              <el-time-picker\r\n                v-else\r\n                v-model=\"taskForm.execute_time\"\r\n                format=\"HH:mm\"\r\n                value-format=\"HH:mm\"\r\n                placeholder=\"选择时间\"\r\n                class=\"time-picker\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 底部按钮 -->\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button class=\"modify-btn\" @click=\"modifyPlan\">修改计划</el-button>\r\n          <el-button type=\"primary\" class=\"run-btn\" @click=\"saveAndRunTask\">保存并运行</el-button>\r\n          <el-button type=\"success\" class=\"save-btn\" @click=\"saveTaskPlan\">保存计划</el-button>\r\n        </div>\r\n      </el-dialog>\r\n    </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  getAnalysisTaskList,\r\n  deleteAnalysisTask,\r\n  batchDeleteAnalysisTasks,\r\n  exportRequirements,\r\n  getAnalysisProgress,\r\n  pushReport,\r\n  createTimedTask,\r\n  getTimedTaskList,\r\n  getTimedTaskDetail,\r\n  updateTimedTask,\r\n  updateTaskStatus,\r\n  deleteTimedTask\r\n} from '@/api/opinion-analysis'\r\nimport { parseTime } from '@/utils/ruoyi'\r\n\r\nexport default {\r\n  name: 'AnalyzeRecord',\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 分析记录表格数据\r\n      recordList: [],\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        page_num: 1,\r\n        page_size: 10,\r\n        requirementName: null,\r\n        entityKeyword: null,\r\n        analysisStatus: null\r\n      },\r\n      // 分析日志对话框\r\n      logDialogVisible: false,\r\n      logLoading: false,\r\n      logList: [],\r\n      // 查看报告对话框\r\n      reportDialogVisible: false,\r\n      reportLoading: false,\r\n      reportData: null,\r\n      // 立即推送对话框\r\n      pushDialogVisible: false,\r\n      pushLoading: false,\r\n      pushForm: {\r\n        push_url: '',\r\n        push_content: ''\r\n      },\r\n      pushRules: {\r\n        push_url: [\r\n          { required: true, message: '推送地址不能为空', trigger: 'blur' }\r\n        ],\r\n        push_content: [\r\n          { required: true, message: '推送内容不能为空', trigger: 'blur' }\r\n        ]\r\n      },\r\n      // 定时推送对话框\r\n      schedulePushDialogVisible: false,\r\n      schedulePushLoading: false,\r\n      schedulePushForm: {\r\n        push_url: '',\r\n        push_time: '',\r\n        frequency: 'once',\r\n        push_content: ''\r\n      },\r\n      schedulePushRules: {\r\n        push_url: [\r\n          { required: true, message: '推送地址不能为空', trigger: 'blur' }\r\n        ],\r\n        push_time: [\r\n          { required: true, message: '推送时间不能为空', trigger: 'change' }\r\n        ],\r\n        push_content: [\r\n          { required: true, message: '推送内容不能为空', trigger: 'blur' }\r\n        ]\r\n      },\r\n      // 当前操作的记录\r\n      currentRecord: null,\r\n      // 定时任务相关数据\r\n      timedTaskDialogVisible: false, // 定时任务抽屉显示状态\r\n      timedTaskList: [], // 定时任务列表\r\n      createTaskDialogVisible: false, // 创建任务弹窗显示状态\r\n      editingTaskIndex: -1, // 当前编辑的任务索引，-1表示新建任务\r\n      taskForm: {\r\n        requirement_id: '', // 需求ID\r\n        name: '', // 任务名称\r\n        description: '',\r\n        execute_time: '16:00',\r\n        execute_date_time: '', // 一次性任务的执行日期时间\r\n        frequency: 'daily',\r\n        push_url: '' // 推送地址\r\n      },\r\n      requirementList: [], // 需求列表\r\n      // 任务预览弹窗状态\r\n      taskPreviewDialog: {\r\n        visible: false,\r\n        taskData: null,\r\n        loading: false\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    // 只重置日期范围，保留其他查询参数\r\n    this.dateRange = []\r\n    this.getList()\r\n    // 注释掉页面加载时的定时任务列表加载，改为点击定时推送按钮时才加载\r\n    // this.loadTimedTaskList()\r\n  },\r\n  methods: {\r\n    /** 查询分析记录列表 */\r\n    getList(paginationParams) {\r\n      this.loading = true\r\n\r\n      // 处理分页参数\r\n      if (paginationParams) {\r\n        this.queryParams.page_num = paginationParams.page\r\n        this.queryParams.page_size = paginationParams.limit\r\n      }\r\n\r\n      const params = {\r\n        page_num: this.queryParams.page_num,\r\n        page_size: this.queryParams.page_size\r\n      }\r\n\r\n      // 添加搜索条件（正确映射到任务表字段）\r\n      if (this.queryParams.requirementName && this.queryParams.requirementName.trim() !== '') {\r\n        params.task_name = this.queryParams.requirementName.trim() // 映射到任务名称\r\n      }\r\n      if (this.queryParams.entityKeyword && this.queryParams.entityKeyword.trim() !== '') {\r\n        params.task_description = this.queryParams.entityKeyword.trim() // 映射到任务描述（包含关键词）\r\n      }\r\n      if (this.queryParams.analysisStatus !== null && this.queryParams.analysisStatus !== undefined && this.queryParams.analysisStatus !== '') {\r\n        // 将前端状态映射回任务状态\r\n        const statusMap = { 0: 'pending', 1: 'running', 2: 'completed', 3: 'failed' }\r\n        params.status = statusMap[this.queryParams.analysisStatus] || 'pending'\r\n      }\r\n\r\n      // 添加时间范围\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        params.create_time_start = this.dateRange[0]\r\n        params.create_time_end = this.dateRange[1]\r\n        console.log('🔍 添加时间范围筛选:', this.dateRange)\r\n      }\r\n\r\n      // 调用真实API获取任务列表数据\r\n      console.log('🔍 发送API请求，参数:', params)\r\n      getAnalysisTaskList(params).then(response => {\r\n        console.log('🔍 API响应:', response)\r\n        // 处理响应数据，将数据库字段映射到前端显示字段\r\n\r\n        // 适配新的响应格式：使用 model_content 后，PageResponseModel 会直接合并到响应根级别\r\n        let requirements = []\r\n        if (response.records) {\r\n          // 新格式：model_content 使 PageResponseModel 直接合并到响应根级别\r\n          requirements = response.records\r\n        } else if (response.data && response.data.records) {\r\n          // 备用格式：PageResponseModel 在 data 字段中\r\n          requirements = response.data.records\r\n        } else if (response.data && response.data.rows) {\r\n          // 旧的分页数据格式\r\n          requirements = response.data.rows\r\n        } else if (response.data && Array.isArray(response.data)) {\r\n          // 直接数组格式\r\n          requirements = response.data\r\n        } else if (response.rows) {\r\n          // 兼容旧格式\r\n          requirements = response.rows\r\n        } else {\r\n          requirements = []\r\n        }\r\n        this.recordList = requirements\r\n          .filter(item => item.id != null && item.id !== undefined && item.id !== '') // 过滤掉无效id的数据\r\n          .map(item => {\r\n            // 确保ID是有效的整数\r\n            const id = parseInt(item.id)\r\n            if (isNaN(id)) {\r\n              return null\r\n            }\r\n\r\n            const mappedItem = {\r\n              id: id, // 确保id是整数\r\n              requirementName: item.task_name || item.taskName || '', // 任务名称\r\n              entityKeyword: item.task_description || item.taskDescription || '', // 任务描述作为关键词显示\r\n              specificRequirement: item.task_description || item.taskDescription || '', // 任务描述\r\n              createTime: item.create_time || item.createTime || '',\r\n              // 将任务状态映射到前端的status字段\r\n              // pending-待执行->0待生成, running-执行中->1生成中, completed-已完成->2已生成, failed-失败->-1生成失败\r\n              status: this.mapTaskStatus(item.status || 'pending'),\r\n              // 添加报告OSS URL字段\r\n              reportOssUrl: item.report_oss_url || item.reportOssUrl || null\r\n            }\r\n            return mappedItem\r\n          })\r\n          .filter(item => item !== null) // 过滤掉映射失败的项\r\n\r\n        // 适配新的总记录数格式：使用 model_content 后，total 会直接在响应根级别\r\n        if (response.total !== undefined) {\r\n          this.total = response.total\r\n        } else if (response.data && response.data.total !== undefined) {\r\n          this.total = response.data.total\r\n        } else {\r\n          this.total = 0\r\n        }\r\n\r\n        this.loading = false\r\n      }).catch(error => {\r\n        this.$modal.msgError('获取分析记录列表失败: ' + (error.message || '未知错误'))\r\n        this.loading = false\r\n      })\r\n    },\r\n\r\n    /** 映射任务状态 */\r\n    mapTaskStatus(taskStatus) {\r\n      // 数据库task status: pending-待执行，running-执行中，completed-已完成，failed-失败，paused-暂停\r\n      // 前端status: 0-待生成，1-生成中，2-已生成，-1-生成失败\r\n      const statusMap = {\r\n        'pending': 0, // 待执行 -> 待生成\r\n        'running': 1, // 执行中 -> 生成中\r\n        'completed': 2, // 已完成 -> 已生成\r\n        'failed': -1, // 失败 -> 生成失败\r\n        'paused': 0 // 暂停 -> 待生成\r\n      }\r\n      return statusMap[taskStatus] !== undefined ? statusMap[taskStatus] : 0\r\n    },\r\n\r\n    /** 映射分析状态（保留兼容性） */\r\n    mapAnalysisStatus(analysisStatus) {\r\n      // 数据库analysis_status: 0-未开始，1-分析中，2-已完成，3-失败\r\n      // 前端status: 0-待生成，1-生成中，2-已生成，-1-生成失败\r\n      const statusMap = {\r\n        0: 0, // 未开始 -> 待生成\r\n        1: 1, // 分析中 -> 生成中\r\n        2: 2, // 已完成 -> 已生成\r\n        3: -1 // 失败 -> 生成失败\r\n      }\r\n      return statusMap[analysisStatus] !== undefined ? statusMap[analysisStatus] : 0\r\n    },\r\n\r\n    /** 需求名称输入监听 */\r\n    onRequirementNameInput(value) {\r\n      console.log('🔍 需求名称输入变化:', value)\r\n      // 可以添加防抖逻辑，避免频繁查询\r\n      // this.debounceSearch()\r\n    },\r\n\r\n    /** 关键词输入监听 */\r\n    onEntityKeywordInput(value) {\r\n      console.log('🔍 关键词输入变化:', value)\r\n      // 可以添加防抖逻辑，避免频繁查询\r\n      // this.debounceSearch()\r\n    },\r\n\r\n    /** 分析状态变化监听 */\r\n    onAnalysisStatusChange(value) {\r\n      console.log('🔍 分析状态变化:', value)\r\n      // 状态变化时立即搜索\r\n      this.handleQuery()\r\n    },\r\n\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      // 重置到第一页\r\n      this.queryParams.page_num = 1\r\n      // 添加搜索日志\r\n      console.log('🔍 执行搜索，查询参数:', this.queryParams)\r\n      this.getList()\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = []\r\n      // 手动重置查询参数\r\n      this.queryParams = {\r\n        page_num: 1,\r\n        page_size: 10,\r\n        requirementName: null,\r\n        entityKeyword: null,\r\n        analysisStatus: null\r\n      }\r\n      // 重置表单\r\n      this.resetForm('queryForm')\r\n      // 添加重置日志\r\n      console.log('🔄 重置搜索条件，查询参数:', this.queryParams)\r\n      this.handleQuery()\r\n    },\r\n\r\n    /** 多选框选中数据 */\r\n    handleSelectionChange(selection) {\r\n      // 过滤掉id为null或undefined的项，并确保id是整数\r\n      this.ids = selection\r\n        .filter(item => item.id != null && item.id !== undefined)\r\n        .map(item => parseInt(item.id))\r\n\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n\r\n    /** 查看分析日志 */\r\n    handleViewLogs(row) {\r\n      this.currentRecord = row\r\n      this.logDialogVisible = true\r\n      this.logLoading = true\r\n\r\n      // 获取分析进度日志\r\n      getAnalysisProgress(row.taskId || row.id).then(response => {\r\n        this.logList = response.data || []\r\n        this.logLoading = false\r\n      }).catch(() => {\r\n        this.logLoading = false\r\n      })\r\n    },\r\n\r\n    /** 查看报告 */\r\n    handleViewReport(row) {\r\n      this.currentRecord = row\r\n      this.reportDialogVisible = true\r\n      this.reportLoading = true\r\n\r\n      // 模拟获取报告数据\r\n      setTimeout(() => {\r\n        this.reportData = {\r\n          requirementName: row.requirementName,\r\n          entityKeyword: row.entityKeyword,\r\n          specificRequirement: row.specificRequirement,\r\n          totalArticles: Math.floor(Math.random() * 1000) + 100,\r\n          sentiment: {\r\n            positive: Math.floor(Math.random() * 50) + 20,\r\n            neutral: Math.floor(Math.random() * 30) + 10,\r\n            negative: Math.floor(Math.random() * 20) + 5\r\n          }\r\n        }\r\n        this.reportLoading = false\r\n      }, 1000)\r\n    },\r\n\r\n    /** 报告 */\r\n    handleReport(row) {\r\n      // 检查是否有报告OSS URL\r\n      if (row.reportOssUrl) {\r\n        // 如果有OSS URL，直接在新窗口打开\r\n        window.open(row.reportOssUrl, '_blank')\r\n      } else {\r\n        // 如果没有OSS URL，显示暂无报告提示\r\n        this.$modal.msg('暂无报告')\r\n      }\r\n    },\r\n\r\n    /** 立即推送 */\r\n    handlePushNow(row) {\r\n      // 检查是否有报告OSS URL - 参照舆情分析页面的OSS检查逻辑\r\n      if (!row.reportOssUrl) {\r\n        this.$modal.msgWarning('报告尚未生成或上传到OSS，请等待报告生成完成后再推送')\r\n        return\r\n      }\r\n\r\n      // 检查任务状态是否为已完成\r\n      if (row.status !== 2) {\r\n        this.$modal.msgWarning('任务尚未完成，请等待分析完成后再推送')\r\n        return\r\n      }\r\n\r\n      this.currentRecord = row\r\n      this.pushForm = {\r\n        push_url: '',\r\n        push_content: `分析报告：${row.requirementName} - 立即推送`\r\n      }\r\n      this.pushDialogVisible = true\r\n    },\r\n\r\n    /** 定时推送 */\r\n    handleSchedulePush(row) {\r\n      this.currentRecord = row\r\n      this.schedulePushForm = {\r\n        push_url: '',\r\n        push_time: '',\r\n        frequency: 'once',\r\n        push_content: `分析报告：${row.requirementName}`\r\n      }\r\n      this.schedulePushDialogVisible = true\r\n    },\r\n\r\n    /** 提交立即推送 */\r\n    submitPush() {\r\n      this.$refs['pushForm'].validate(valid => {\r\n        if (valid) {\r\n          // 参照舆情分析页面的OSS检查逻辑 - 验证推送前的必要条件\r\n          if (!this.currentRecord.reportOssUrl) {\r\n            this.$modal.msgError('报告尚未上传到OSS，无法推送。请等待报告生成完成后再试。')\r\n            return\r\n          }\r\n\r\n          // 检查任务状态\r\n          if (this.currentRecord.status !== 2) {\r\n            this.$modal.msgError('任务尚未完成，无法推送。请等待分析完成后再试。')\r\n            return\r\n          }\r\n\r\n          // 验证推送URL格式（参照源文件的URL验证逻辑）\r\n          if (!this.validatePushUrl(this.pushForm.push_url)) {\r\n            return\r\n          }\r\n\r\n          this.pushLoading = true\r\n\r\n          // 构建推送数据 - 参照舆情分析页面的数据结构\r\n          const pushData = {\r\n            target_url: this.pushForm.push_url,\r\n            report_data: {\r\n              requirementName: this.currentRecord.requirementName,\r\n              entityKeyword: this.currentRecord.entityKeyword,\r\n              specificRequirement: this.currentRecord.specificRequirement,\r\n              reportOssUrl: this.currentRecord.reportOssUrl, // 确保有OSS URL\r\n              totalArticles: 0, // 后端会从实际数据中获取\r\n              sentiment: {}, // 后端会从实际数据中获取\r\n              dataSources: 0, // 后端会从实际数据中获取\r\n              taskId: this.currentRecord.id,\r\n              createTime: this.currentRecord.createTime,\r\n              status: this.currentRecord.status,\r\n              pushTime: new Date().toISOString() // 添加推送时间戳\r\n            },\r\n            analysis_results: {\r\n              summary: this.pushForm.push_content,\r\n              reportUrl: this.currentRecord.reportOssUrl // 确保有报告URL\r\n            },\r\n            requirement_id: this.currentRecord.id,\r\n            push_type: 'immediate'\r\n          }\r\n\r\n          pushReport(pushData).then(response => {\r\n            // 检查响应并显示详细信息\r\n            if (response && response.success) {\r\n              this.$modal.msgSuccess('推送成功')\r\n\r\n              // 如果后端生成了新的报告URL，可以更新本地记录\r\n              if (response.data && response.data.report_url && !this.currentRecord.reportOssUrl) {\r\n                this.currentRecord.reportOssUrl = response.data.report_url\r\n                console.log('更新本地报告URL:', response.data.report_url)\r\n              }\r\n            } else {\r\n              this.$modal.msgSuccess('推送完成')\r\n            }\r\n\r\n            this.pushDialogVisible = false\r\n            this.pushLoading = false\r\n          }).catch(error => {\r\n            console.error('推送失败:', error)\r\n            if (error.response && error.response.data && error.response.data.msg) {\r\n              this.$modal.msgError('推送失败: ' + error.response.data.msg)\r\n            } else {\r\n              this.$modal.msgError('推送失败，请重试')\r\n            }\r\n            this.pushLoading = false\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    /** 提交定时推送 */\r\n    submitSchedulePush() {\r\n      this.$refs['schedulePushForm'].validate(valid => {\r\n        if (valid) {\r\n          this.schedulePushLoading = true\r\n          const taskData = {\r\n            requirement_id: this.currentRecord.id,\r\n            push_url: this.schedulePushForm.push_url,\r\n            push_time: this.schedulePushForm.push_time,\r\n            frequency: this.schedulePushForm.frequency,\r\n            push_content: this.schedulePushForm.push_content\r\n          }\r\n\r\n          createTimedTask(taskData).then(response => {\r\n            this.$modal.msgSuccess('定时推送任务创建成功')\r\n            this.schedulePushDialogVisible = false\r\n            this.schedulePushLoading = false\r\n          }).catch(() => {\r\n            this.schedulePushLoading = false\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      let ids, names\r\n\r\n      // 判断是否为批量删除（无参数或参数为事件对象）\r\n      if (!row || row instanceof Event) {\r\n        // 批量删除\r\n        if (!this.ids || this.ids.length === 0) {\r\n          this.$modal.msgError('请先选择要删除的记录')\r\n          return\r\n        }\r\n        // 过滤并验证IDs\r\n        ids = this.ids.filter(id => id != null && !isNaN(id)).map(id => parseInt(id))\r\n        if (ids.length === 0) {\r\n          this.$modal.msgError('选中的记录ID无效，无法删除')\r\n          return\r\n        }\r\n        names = this.recordList.filter(item => ids.includes(item.id)).map(item => item.requirementName).join('、')\r\n      } else {\r\n        // 单个删除\r\n        if (!row.id || row.id == null) {\r\n          this.$modal.msgError('记录ID无效，无法删除')\r\n          return\r\n        }\r\n        ids = [parseInt(row.id)]\r\n        names = row.requirementName || '未知记录'\r\n      }\r\n      this.$modal.confirm(`是否确认删除分析记录\"${names}\"？`).then(() => {\r\n        // 根据IDs数量判断是单个删除还是批量删除\r\n        if (ids.length === 1) {\r\n          // 单个删除\r\n          return deleteAnalysisTask(ids[0])\r\n        } else {\r\n          // 批量删除\r\n          return batchDeleteAnalysisTasks(ids)\r\n        }\r\n      }).then(response => {\r\n        console.log('🗑️ 删除操作成功，响应:', response)\r\n\r\n        // 检查是否需要重置分页（物理删除后需要重新计算分页）\r\n        const currentPageRecords = this.recordList.length\r\n        const deletedCount = ids.length\r\n\r\n        // 如果删除后当前页可能没有记录了，且不是第一页，则回到上一页\r\n        if (currentPageRecords <= deletedCount && this.queryParams.page_num > 1) {\r\n          this.queryParams.page_num = this.queryParams.page_num - 1\r\n          console.log('🔄 删除后回到上一页:', this.queryParams.page_num)\r\n        }\r\n\r\n        // 重置选中状态\r\n        this.ids = []\r\n        this.single = true\r\n        this.multiple = true\r\n\r\n        // 物理删除成功后，直接重新获取数据以确保数据一致性\r\n        this.getList()\r\n\r\n        this.$modal.msgSuccess('删除成功')\r\n      }).catch(error => {\r\n        if (error.response && error.response.data) {\r\n          if (error.response.data.msg) {\r\n            this.$modal.msgError(error.response.data.msg)\r\n          } else if (error.response.data.message) {\r\n            this.$modal.msgError(error.response.data.message)\r\n          } else if (error.response.data.detail) {\r\n            // 处理FastAPI的验证错误\r\n            if (Array.isArray(error.response.data.detail)) {\r\n              const details = error.response.data.detail.map(d => d.msg || d.message || JSON.stringify(d)).join(', ')\r\n              this.$modal.msgError(`删除失败: ${details}`)\r\n            } else {\r\n              this.$modal.msgError(`删除失败: ${error.response.data.detail}`)\r\n            }\r\n          } else {\r\n            this.$modal.msgError(`删除失败: ${JSON.stringify(error.response.data)}`)\r\n          }\r\n        } else if (error.message) {\r\n          this.$modal.msgError(`删除失败: ${error.message}`)\r\n        } else {\r\n          this.$modal.msgError('删除失败，请重试')\r\n        }\r\n      })\r\n    },\r\n\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.$modal.confirm('是否确认导出所有分析记录数据项？').then(() => {\r\n        const params = {}\r\n\r\n        // 添加搜索条件（使用后端期望的下划线命名）\r\n        if (this.queryParams.requirementName) {\r\n          params.requirement_name = this.queryParams.requirementName\r\n        }\r\n        if (this.queryParams.entityKeyword) {\r\n          params.entity_keyword = this.queryParams.entityKeyword\r\n        }\r\n        if (this.queryParams.analysisStatus !== null && this.queryParams.analysisStatus !== undefined && this.queryParams.analysisStatus !== '') {\r\n          params.analysis_status = this.queryParams.analysisStatus\r\n        }\r\n\r\n        // 添加时间范围\r\n        if (this.dateRange && this.dateRange.length === 2) {\r\n          params.create_time_start = this.dateRange[0]\r\n          params.create_time_end = this.dateRange[1]\r\n        }\r\n\r\n        return exportRequirements(params)\r\n      }).then(response => {\r\n        // 处理文件下载\r\n        const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })\r\n        const url = window.URL.createObjectURL(blob)\r\n        const link = document.createElement('a')\r\n        link.href = url\r\n        link.download = `分析记录_${new Date().toISOString().slice(0, 10)}.xlsx`\r\n        document.body.appendChild(link)\r\n        link.click()\r\n        document.body.removeChild(link)\r\n        window.URL.revokeObjectURL(url)\r\n\r\n        this.$modal.msgSuccess('导出成功')\r\n      }).catch(error => {\r\n        if (error.response && error.response.data && error.response.data.msg) {\r\n          this.$modal.msgError(error.response.data.msg)\r\n        } else {\r\n          this.$modal.msgError('导出失败，请重试')\r\n        }\r\n      })\r\n    },\r\n\r\n    /** 导出报告 */\r\n    handleExportReport() {\r\n      this.$modal.msgSuccess('报告导出成功')\r\n    },\r\n\r\n    /** 验证推送URL格式（参照舆情分析页面的验证逻辑） */\r\n    validatePushUrl(url) {\r\n      if (!url || !url.trim()) {\r\n        this.$modal.msgError('请输入推送目标URL地址')\r\n        return false\r\n      }\r\n\r\n      let fullUrl = url.trim()\r\n\r\n      // 如果没有协议前缀，自动添加https://\r\n      if (!fullUrl.includes('://')) {\r\n        fullUrl = 'https://' + fullUrl\r\n      }\r\n\r\n      // 基本URL格式验证\r\n      try {\r\n        new URL(fullUrl)\r\n        console.log('URL验证通过:', fullUrl)\r\n        return fullUrl\r\n      } catch (error) {\r\n        // 宽松验证：如果包含://且长度合理，则认为可能是有效URL\r\n        if (fullUrl.includes('://') && fullUrl.length > 10) {\r\n          console.log('使用宽松验证通过URL:', fullUrl)\r\n          this.$modal.msgWarning('URL格式可能不正确，但仍将尝试推送。如果推送失败，请检查URL格式')\r\n          return fullUrl\r\n        }\r\n\r\n        this.$modal.msgError('URL格式不正确，请检查后重试')\r\n        return false\r\n      }\r\n    },\r\n\r\n    /** 获取状态标签类型 */\r\n    getStatusTagType(status) {\r\n      const statusMap = {\r\n        0: 'info', // 待生成 - 灰色\r\n        1: 'warning', // 生成中 - 橙色\r\n        2: 'success', // 已生成 - 绿色\r\n        '-1': 'danger' // 生成失败 - 红色\r\n      }\r\n      return statusMap[status] || 'info'\r\n    },\r\n\r\n    /** 获取状态文本 */\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        0: '待生成',\r\n        1: '生成中',\r\n        2: '已生成',\r\n        '-1': '生成失败'\r\n      }\r\n      return statusMap[status] || '未知'\r\n    },\r\n\r\n    /** 获取日志级别标签类型 */\r\n    getLogLevelTagType(level) {\r\n      const levelMap = {\r\n        'info': 'info',\r\n        'success': 'success',\r\n        'warning': 'warning',\r\n        'error': 'danger'\r\n      }\r\n      return levelMap[level] || 'info'\r\n    },\r\n\r\n    // ==================== 定时任务相关方法 ====================\r\n\r\n    // 处理定时推送按钮点击\r\n    handleTimedPush() {\r\n      this.timedTaskDialogVisible = true\r\n      // 打开定时推送弹窗时加载定时任务列表\r\n      this.loadTimedTaskList()\r\n    },\r\n\r\n    // 关闭定时任务弹窗\r\n    closeTimedTaskDialog() {\r\n      this.timedTaskDialogVisible = false\r\n    },\r\n\r\n    // 处理创建定时任务\r\n    handleCreateTimedTask() {\r\n      this.resetTaskForm()\r\n      this.editingTaskIndex = -1 // 设置为新建模式\r\n      this.loadOpinionTaskList() // 加载舆情任务列表\r\n      this.createTaskDialogVisible = true\r\n    },\r\n\r\n    // 处理添加定时任务按钮\r\n    handleAddTimedTask() {\r\n      this.resetTaskForm()\r\n      this.editingTaskIndex = -1 // 设置为新建模式\r\n      this.loadOpinionTaskList() // 加载舆情任务列表\r\n      this.createTaskDialogVisible = true\r\n    },\r\n\r\n    // 加载舆情任务列表（用于定时推送绑定）\r\n    async loadOpinionTaskList() {\r\n      try {\r\n        const response = await getAnalysisTaskList({\r\n          page_num: 1,\r\n          page_size: 100\r\n        })\r\n\r\n        if (response.success) {\r\n          // 处理分页数据，从 opinion_task 表获取数据\r\n          let tasks = []\r\n\r\n          if (response.data && response.data.rows) {\r\n            tasks = response.data.rows\r\n          } else if (Array.isArray(response.data)) {\r\n            tasks = response.data\r\n          } else if (response.rows) {\r\n            tasks = response.rows\r\n          }\r\n\r\n          // 映射任务数据到需求列表格式\r\n          this.requirementList = tasks.map(task => ({\r\n            id: task.id,\r\n            requirementName: task.requirementName || task.requirement_name || `任务${task.id}`,\r\n            taskId: task.id,\r\n            keywords: task.keywords || task.entity_keyword,\r\n            specificRequirement: task.specificRequirement || task.specific_requirement,\r\n            createTime: task.createTime || task.create_time\r\n          }))\r\n        } else {\r\n          this.$message.error(response.msg || '获取舆情任务列表失败')\r\n          this.requirementList = []\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('加载舆情任务列表失败')\r\n        this.requirementList = []\r\n      }\r\n    },\r\n\r\n    // 加载定时任务列表\r\n    async loadTimedTaskList() {\r\n      try {\r\n        const response = await getTimedTaskList()\r\n\r\n        if (response.success) {\r\n          // 处理分页数据 - 后端返回的是PageResponseModel格式\r\n\r\n          if (response.data && response.data.rows) {\r\n            this.timedTaskList = response.data.rows.map(task => ({\r\n              id: task.id,\r\n              requirementId: task.requirementId,\r\n              name: task.taskName,\r\n              description: task.taskDescription,\r\n              executeTime: task.executeTime,\r\n              executeDateTime: task.frequency === 'once' && task.execute_date ? `${task.execute_date} ${task.executeTime}` : '',\r\n              frequency: task.frequency,\r\n              status: task.status === 'running' ? 'running' : 'pending',\r\n              pushUrl: task.pushUrl\r\n            }))\r\n          } else if (response.data && response.data.records && Array.isArray(response.data.records)) {\r\n            this.timedTaskList = response.data.records.map(task => ({\r\n              id: task.id,\r\n              requirementId: task.requirementId,\r\n              name: task.taskName,\r\n              description: task.taskDescription,\r\n              executeTime: task.executeTime,\r\n              executeDateTime: task.frequency === 'once' && task.execute_date ? `${task.execute_date} ${task.executeTime}` : '',\r\n              frequency: task.frequency,\r\n              status: task.status === 'running' ? 'running' : 'pending',\r\n              pushUrl: task.pushUrl\r\n            }))\r\n          } else if (Array.isArray(response.data)) {\r\n            this.timedTaskList = response.data.map(task => ({\r\n              id: task.id,\r\n              requirementId: task.requirementId,\r\n              name: task.taskName,\r\n              description: task.taskDescription,\r\n              executeTime: task.executeTime,\r\n              executeDateTime: task.frequency === 'once' && task.execute_date ? `${task.execute_date} ${task.executeTime}` : '',\r\n              frequency: task.frequency,\r\n              status: task.status === 'running' ? 'running' : 'pending',\r\n              pushUrl: task.pushUrl\r\n            }))\r\n          } else if (response.rows && Array.isArray(response.rows)) {\r\n            this.timedTaskList = response.rows.map(task => ({\r\n              id: task.id,\r\n              requirementId: task.requirementId,\r\n              name: task.taskName,\r\n              description: task.taskDescription,\r\n              executeTime: task.executeTime,\r\n              executeDateTime: task.frequency === 'once' && task.execute_date ? `${task.execute_date} ${task.executeTime}` : '',\r\n              frequency: task.frequency,\r\n              status: task.status === 'running' ? 'running' : 'pending',\r\n              pushUrl: task.pushUrl\r\n            }))\r\n          } else if (response.records && Array.isArray(response.records)) {\r\n            this.timedTaskList = response.records.map(task => ({\r\n              id: task.id,\r\n              requirementId: task.requirementId,\r\n              name: task.taskName,\r\n              description: task.taskDescription,\r\n              executeTime: task.executeTime,\r\n              executeDateTime: task.frequency === 'once' && task.execute_date ? `${task.execute_date} ${task.executeTime}` : '',\r\n              frequency: task.frequency,\r\n              status: task.status === 'running' ? 'running' : 'pending',\r\n              pushUrl: task.pushUrl\r\n            }))\r\n          } else {\r\n            this.timedTaskList = []\r\n          }\r\n        } else {\r\n          this.$message.error(response.msg || '获取定时任务列表失败')\r\n          this.timedTaskList = []\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('加载定时任务列表失败')\r\n        this.timedTaskList = []\r\n      }\r\n    },\r\n\r\n    // 关闭创建任务弹窗\r\n    closeCreateTaskDialog() {\r\n      this.createTaskDialogVisible = false\r\n      this.resetTaskForm()\r\n      this.editingTaskIndex = -1 // 重置编辑状态\r\n    },\r\n\r\n    // 重置任务表单\r\n    resetTaskForm() {\r\n      this.taskForm = {\r\n        requirement_id: '',\r\n        name: '',\r\n        description: '',\r\n        execute_time: '16:00',\r\n        execute_date_time: '',\r\n        frequency: 'daily',\r\n        push_url: ''\r\n      }\r\n    },\r\n\r\n    // 保存并运行任务\r\n    async saveAndRunTask() {\r\n      if (!this.validateTaskForm()) {\r\n        return\r\n      }\r\n\r\n      try {\r\n        const taskData = this.buildTaskData()\r\n\r\n        if (this.editingTaskIndex === -1) {\r\n          // 新建任务\r\n          const response = await createTimedTask(taskData)\r\n          if (response.success) {\r\n            this.$message.success('任务已保存并开始运行')\r\n            // 重新加载任务列表\r\n            await this.loadTimedTaskList()\r\n            // 更新任务状态为运行中\r\n            if (response.data && response.data.id) {\r\n              await updateTaskStatus(response.data.id, 'running')\r\n              await this.loadTimedTaskList()\r\n            }\r\n          } else {\r\n            this.$message.error('创建任务失败：' + response.msg)\r\n            return\r\n          }\r\n        } else {\r\n          // 编辑任务\r\n          const task = this.timedTaskList[this.editingTaskIndex]\r\n          const response = await updateTimedTask(task.id, taskData)\r\n\r\n          if (response.success) {\r\n            // 更新任务状态为运行中\r\n            await updateTaskStatus(task.id, 'running')\r\n            this.$message.success('任务已更新并开始运行')\r\n            // 重新加载任务列表\r\n            await this.loadTimedTaskList()\r\n          } else {\r\n            this.$message.error('更新任务失败：' + response.msg)\r\n            return\r\n          }\r\n        }\r\n\r\n        this.createTaskDialogVisible = false\r\n        this.resetTaskForm()\r\n        this.editingTaskIndex = -1 // 重置编辑状态\r\n      } catch (error) {\r\n        this.$message.error('保存任务失败，请重试')\r\n      }\r\n    },\r\n\r\n    // 保存任务计划\r\n    async saveTaskPlan() {\r\n      if (!this.validateTaskForm()) {\r\n        return\r\n      }\r\n\r\n      try {\r\n        const taskData = this.buildTaskData()\r\n\r\n        if (this.editingTaskIndex === -1) {\r\n          // 新建任务\r\n          const response = await createTimedTask(taskData)\r\n          if (response.success) {\r\n            this.$message.success('任务计划已保存')\r\n            // 重新加载任务列表\r\n            await this.loadTimedTaskList()\r\n          } else {\r\n            this.$message.error('保存任务计划失败：' + response.msg)\r\n            return\r\n          }\r\n        } else {\r\n          // 编辑任务\r\n          const task = this.timedTaskList[this.editingTaskIndex]\r\n          const response = await updateTimedTask(task.id, taskData)\r\n\r\n          if (response.success) {\r\n            this.$message.success('任务计划已更新')\r\n            // 重新加载任务列表\r\n            await this.loadTimedTaskList()\r\n          } else {\r\n            this.$message.error('更新任务计划失败：' + response.msg)\r\n            return\r\n          }\r\n        }\r\n\r\n        this.createTaskDialogVisible = false\r\n        this.resetTaskForm()\r\n        this.editingTaskIndex = -1 // 重置编辑状态\r\n      } catch (error) {\r\n        this.$message.error('保存任务计划失败，请重试')\r\n      }\r\n    },\r\n\r\n    // 验证任务表单\r\n    validateTaskForm() {\r\n      if (!this.taskForm.requirement_id) {\r\n        this.$message.warning('请选择要关联的舆情分析任务')\r\n        return false\r\n      }\r\n\r\n      if (!this.taskForm.name || !this.taskForm.name.trim()) {\r\n        this.$message.warning('请输入任务名称')\r\n        return false\r\n      }\r\n\r\n      if (!this.taskForm.description || !this.taskForm.description.trim()) {\r\n        this.$message.warning('请输入任务描述')\r\n        return false\r\n      }\r\n\r\n      // 验证执行时间\r\n      if (this.taskForm.frequency === 'once') {\r\n        if (!this.taskForm.execute_date_time) {\r\n          this.$message.warning('请选择执行日期和时间')\r\n          return false\r\n        }\r\n        // 检查是否是未来时间\r\n        const executeTime = new Date(this.taskForm.execute_date_time)\r\n        const now = new Date()\r\n        if (executeTime <= now) {\r\n          this.$message.warning('执行时间必须是未来时间')\r\n          return false\r\n        }\r\n      } else {\r\n        if (!this.taskForm.execute_time) {\r\n          this.$message.warning('请选择执行时间')\r\n          return false\r\n        }\r\n      }\r\n\r\n      return true\r\n    },\r\n\r\n    // 构建任务数据\r\n    buildTaskData() {\r\n      // 处理执行时间：对于一次性任务，只提取时间部分；对于周期性任务，直接使用时间\r\n      let executeTime\r\n      if (this.taskForm.frequency === 'once') {\r\n        // 一次性任务：从完整日期时间中提取时间部分 (HH:MM)\r\n        if (this.taskForm.execute_date_time) {\r\n          const dateTime = new Date(this.taskForm.execute_date_time)\r\n          executeTime = `${dateTime.getHours().toString().padStart(2, '0')}:${dateTime.getMinutes().toString().padStart(2, '0')}`\r\n        } else {\r\n          executeTime = '09:00' // 默认时间\r\n        }\r\n      } else {\r\n        // 周期性任务：直接使用时间\r\n        executeTime = this.taskForm.execute_time || '09:00'\r\n      }\r\n\r\n      const taskData = {\r\n        requirement_id: this.taskForm.requirement_id,\r\n        task_name: this.taskForm.name,\r\n        task_type: 'scheduled',\r\n        frequency: this.taskForm.frequency, // 使用 frequency 而不是 schedule_type\r\n        execute_time: executeTime, // 只存储时间部分 (HH:MM)\r\n        task_description: this.taskForm.description,\r\n        push_url: this.taskForm.push_url || ''\r\n      }\r\n\r\n      // 对于一次性任务，可能需要额外的日期信息\r\n      if (this.taskForm.frequency === 'once' && this.taskForm.execute_date_time) {\r\n        taskData['execute_date'] = this.taskForm.execute_date_time.split(' ')[0] // 提取日期部分 (YYYY-MM-DD)\r\n      }\r\n\r\n      return taskData\r\n    },\r\n\r\n    // 删除任务\r\n    async deleteTask(index) {\r\n      try {\r\n        const task = this.timedTaskList[index]\r\n\r\n        if (!task) {\r\n          this.$message.error('任务数据不存在')\r\n          return\r\n        }\r\n\r\n        await this.$confirm(`确定要删除任务「${task.name}」吗？`, '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        })\r\n\r\n        const response = await deleteTimedTask(task.id)\r\n\r\n        if (response.success) {\r\n          this.$message.success('删除成功')\r\n          // 重新加载任务列表\r\n          await this.loadTimedTaskList()\r\n        } else {\r\n          this.$message.error('删除失败：' + (response.msg || response.message || '未知错误'))\r\n        }\r\n      } catch (error) {\r\n        if (error === 'cancel') {\r\n          return\r\n        }\r\n        this.$message.error('删除任务失败，请重试')\r\n      }\r\n    },\r\n\r\n    // 编辑任务\r\n    editTask(index) {\r\n      const task = this.timedTaskList[index]\r\n\r\n      if (!task) {\r\n        this.$message.error('任务数据不存在')\r\n        return\r\n      }\r\n\r\n      this.editingTaskIndex = index\r\n      this.taskForm = {\r\n        requirement_id: task.requirementId || '',\r\n        name: task.name || '',\r\n        description: task.description || '',\r\n        execute_time: task.executeTime || '16:00',\r\n        execute_date_time: task.executeDateTime || '',\r\n        frequency: task.frequency || 'daily',\r\n        push_url: task.pushUrl || ''\r\n      }\r\n\r\n      this.loadOpinionTaskList() // 加载舆情任务列表\r\n      this.createTaskDialogVisible = true\r\n    },\r\n\r\n    // 切换任务状态\r\n    async toggleTaskStatus(index) {\r\n      try {\r\n        const task = this.timedTaskList[index]\r\n        const newStatus = task.status === 'running' ? 'pending' : 'running'\r\n\r\n        const response = await updateTaskStatus(task.id, newStatus)\r\n\r\n        if (response.success) {\r\n          if (newStatus === 'running') {\r\n            this.$message.success(`任务「${task.name}」已启动`)\r\n          } else {\r\n            this.$message.info(`任务「${task.name}」已暂停`)\r\n          }\r\n          // 重新加载任务列表\r\n          await this.loadTimedTaskList()\r\n        } else {\r\n          this.$message.error('状态更新失败：' + response.msg)\r\n        }\r\n      } catch (error) {\r\n        this.$message.error('状态更新失败，请重试')\r\n      }\r\n    },\r\n\r\n    // 预览任务\r\n    previewTask(index) {\r\n      const task = this.timedTaskList[index]\r\n      if (!task) {\r\n        this.$message.error('任务数据不存在')\r\n        return\r\n      }\r\n      this.taskPreviewDialog.taskData = task\r\n      this.taskPreviewDialog.visible = true\r\n    },\r\n\r\n    // 获取任务计划文本\r\n    getTaskScheduleText(task) {\r\n      if (!task) return ''\r\n\r\n      if (task.frequency === 'once') {\r\n        // 一次性任务显示具体执行时间\r\n        return `仅一次 ${task.executeDateTime || task.executeTime}`\r\n      } else {\r\n        // 周期性任务显示频率和时间\r\n        const frequencyText = this.getFrequencyText(task.frequency)\r\n        return `${frequencyText} ${task.executeTime}`\r\n      }\r\n    },\r\n\r\n    // 获取频率文本\r\n    getFrequencyText(frequency) {\r\n      const frequencyMap = {\r\n        'once': '仅一次',\r\n        'daily': '每天',\r\n        'weekly': '每周',\r\n        'monthly': '每月'\r\n      }\r\n      return frequencyMap[frequency] || frequency\r\n    },\r\n\r\n    // 修改计划\r\n    modifyPlan() {\r\n      // 这里可以添加修改计划的逻辑\r\n      this.$message.info('修改计划功能待实现')\r\n    },\r\n\r\n    /** 时间格式化 */\r\n    parseTime\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n.mb8 {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.report-content {\r\n  min-height: 400px;\r\n\r\n  .analysis-results {\r\n    margin-top: 20px;\r\n\r\n    .statistic-value {\r\n      font-size: 24px;\r\n      font-weight: bold;\r\n      text-align: center;\r\n      padding: 20px 0;\r\n\r\n      &.positive {\r\n        color: #67c23a;\r\n      }\r\n\r\n      &.negative {\r\n        color: #f56c6c;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.el-table .cell {\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.small-padding .cell {\r\n  padding-left: 5px;\r\n  padding-right: 5px;\r\n}\r\n\r\n.fixed-width .el-button--mini {\r\n  margin-right: 5px;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n\r\n// 状态标签样式\r\n.el-tag {\r\n  &.el-tag--info {\r\n    background-color: #f4f4f5;\r\n    border-color: #e9e9eb;\r\n    color: #909399;\r\n  }\r\n\r\n  &.el-tag--warning {\r\n    background-color: #fdf6ec;\r\n    border-color: #f5dab1;\r\n    color: #e6a23c;\r\n  }\r\n\r\n  &.el-tag--success {\r\n    background-color: #f0f9ff;\r\n    border-color: #c6f7d0;\r\n    color: #67c23a;\r\n  }\r\n\r\n  &.el-tag--danger {\r\n    background-color: #fef0f0;\r\n    border-color: #fbc4c4;\r\n    color: #f56c6c;\r\n  }\r\n}\r\n\r\n// 操作按钮样式\r\n.el-button--text {\r\n  padding: 0;\r\n  margin-right: 10px;\r\n\r\n  &:last-child {\r\n    margin-right: 0;\r\n  }\r\n}\r\n\r\n// 搜索表单样式\r\n.el-form--inline .el-form-item {\r\n  margin-right: 10px;\r\n}\r\n\r\n// 定时任务抽屉样式\r\n.timed-task-drawer {\r\n  .drawer-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    width: 100%;\r\n\r\n    .drawer-title {\r\n      font-size: 16px;\r\n      font-weight: 500;\r\n    }\r\n\r\n    .add-task-btn {\r\n      margin-left: auto;\r\n    }\r\n  }\r\n\r\n  .drawer-content {\r\n    padding: 20px;\r\n    height: calc(100vh - 120px);\r\n    overflow-y: auto;\r\n\r\n    .empty-state {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      justify-content: center;\r\n      height: 100%;\r\n      text-align: center;\r\n\r\n      .empty-content {\r\n        .empty-icon {\r\n          margin-bottom: 20px;\r\n        }\r\n\r\n        .empty-text {\r\n          font-size: 14px;\r\n          color: #999;\r\n          margin-bottom: 20px;\r\n        }\r\n\r\n        .create-btn {\r\n          padding: 10px 20px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .task-list {\r\n      .task-items {\r\n        .task-item {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          padding: 16px;\r\n          border: 1px solid #e8e8e8;\r\n          border-radius: 8px;\r\n          margin-bottom: 12px;\r\n          background: #fff;\r\n\r\n          &:hover {\r\n            border-color: #409eff;\r\n            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\r\n          }\r\n\r\n          .task-info {\r\n            flex: 1;\r\n\r\n            .task-header {\r\n              display: flex;\r\n              justify-content: space-between;\r\n              align-items: center;\r\n              margin-bottom: 8px;\r\n\r\n              .task-name {\r\n                font-size: 14px;\r\n                font-weight: 500;\r\n                color: #333;\r\n              }\r\n\r\n              .task-status {\r\n                padding: 2px 8px;\r\n                border-radius: 4px;\r\n                font-size: 12px;\r\n\r\n                &.status-running {\r\n                  background: #f0f9ff;\r\n                  color: #1890ff;\r\n                }\r\n\r\n                &.status-pending {\r\n                  background: #f6f6f6;\r\n                  color: #666;\r\n                }\r\n              }\r\n            }\r\n\r\n            .task-schedule {\r\n              display: flex;\r\n              align-items: center;\r\n              font-size: 12px;\r\n              color: #666;\r\n\r\n              i {\r\n                margin-right: 4px;\r\n              }\r\n            }\r\n          }\r\n\r\n          .task-actions {\r\n            display: flex;\r\n            gap: 8px;\r\n\r\n            .el-button {\r\n              padding: 4px;\r\n              min-width: auto;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 创建任务弹窗样式\r\n.create-task-dialog {\r\n  .task-form {\r\n    .task-requirement-section,\r\n    .execute-time-section {\r\n      margin-bottom: 24px;\r\n\r\n      .section-label {\r\n        font-size: 14px;\r\n        font-weight: 500;\r\n        margin-bottom: 16px;\r\n        color: #333;\r\n\r\n        .required {\r\n          color: #f56c6c;\r\n          margin-left: 4px;\r\n        }\r\n      }\r\n\r\n      .form-group {\r\n        margin-bottom: 16px;\r\n\r\n        .input-label {\r\n          font-size: 13px;\r\n          color: #666;\r\n          margin-bottom: 8px;\r\n        }\r\n\r\n        .task-name-input,\r\n        .task-description-input {\r\n          width: 100%;\r\n        }\r\n      }\r\n\r\n      .time-selector {\r\n        display: flex;\r\n        gap: 12px;\r\n\r\n        .frequency-select {\r\n          width: 120px;\r\n        }\r\n\r\n        .datetime-picker,\r\n        .time-picker {\r\n          flex: 1;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .dialog-footer {\r\n    text-align: right;\r\n\r\n    .el-button {\r\n      margin-left: 8px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}